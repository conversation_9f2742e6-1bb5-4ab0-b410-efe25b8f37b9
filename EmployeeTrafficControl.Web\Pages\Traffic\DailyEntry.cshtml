
@page
@model EmployeeTrafficControl.Web.Pages.Traffic.DailyEntryModel
@{
    ViewData["Title"] = "ثبت ورود اولیه گروهی";
}

<h1 class="h3 mb-4 text-gray-800">@ViewData["Title"]</h1>

@if (!Model.EmployeesNotEntered.Any())
{
    <div class="alert alert-success">همه کارمندان فعال، ورود اولیه خود را ثبت کرده‌اند.</div>
}
else
{
    <form method="post">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">لیست کارمندان بدون ورود</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" /></th>
                                <th>نام</th>
                                <th>کد پرسنلی</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var employee in Model.EmployeesNotEntered)
                            {
                                <tr>
                                    <td><input type="checkbox" name="selectedEmployeeIds" value="@employee.EmployeeId" class="employee-checkbox" /></td>
                                    <td>@employee.FirstName @employee.LastName</td>
                                    <td>@employee.PersonnelCode</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <button type="submit" class="btn btn-primary">ثبت ورود برای انتخاب شده‌ها</button>
            </div>
        </div>
    </form>
}

@section Scripts {
    <script>
        document.getElementById('selectAll').addEventListener('change', function (e) {
            const checkboxes = document.querySelectorAll('.employee-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });
    </script>
}
