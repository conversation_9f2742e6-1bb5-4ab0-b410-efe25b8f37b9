@page
@model EmployeeTrafficControl.Web.Pages.Users.PermissionsModel
@{
    ViewData["Title"] = $"مدیریت مجوزهای کاربر {Model.User?.UserName}";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title mb-0">
                                <i class="bi bi-person-lock"></i>
                                مدیریت مجوزهای کاربر: @Model.User?.UserName
                            </h4>
                            <small class="text-muted">
                                نقش: @Model.User?.Role?.Name | 
                                نام: @(Model.User?.Employee?.FirstName + " " + Model.User?.Employee?.LastName)
                            </small>
                        </div>
                        <div>
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-right"></i>
                                بازگشت
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- نمایش مجوزهای نقش -->
                    <div class="mb-4">
                        <h5 class="text-info border-bottom pb-2">
                            <i class="bi bi-shield-check"></i>
                            مجوزهای نقش (@Model.User?.Role?.Name)
                        </h5>
                        @if (Model.RolePermissionsByCategory.Any())
                        {
                            @foreach (var category in Model.RolePermissionsByCategory)
                            {
                                <div class="mb-3">
                                    <h6 class="text-muted">@category.Key</h6>
                                    <div class="row">
                                        @foreach (var permission in category.Value)
                                        {
                                            <div class="col-md-6 col-lg-4 mb-2">
                                                <div class="card border-info">
                                                    <div class="card-body py-2">
                                                        <small class="fw-bold text-info">@permission.Name</small>
                                                        <br>
                                                        <small class="text-muted">@permission.Code</small>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <p class="text-muted">این نقش هیچ مجوزی ندارد.</p>
                        }
                    </div>

                    <!-- مدیریت مجوزهای خاص کاربر -->
                    <div class="mb-4">
                        <h5 class="text-primary border-bottom pb-2">
                            <i class="bi bi-person-gear"></i>
                            مجوزهای خاص کاربر (Override)
                        </h5>
                        
                        <form method="post">
                            <input type="hidden" asp-for="UserId" />
                            
                            @if (Model.AllPermissionsByCategory.Any())
                            {
                                @foreach (var category in Model.AllPermissionsByCategory)
                                {
                                    <div class="mb-4">
                                        <h6 class="text-secondary">@category.Key</h6>
                                        <div class="row">
                                            @foreach (var permission in category.Value)
                                            {
                                                var userPermission = Model.UserPermissions.FirstOrDefault(up => up.PermissionId == permission.PermissionId);
                                                var hasRolePermission = Model.RolePermissionIds.Contains(permission.PermissionId);
                                                
                                                <div class="col-md-6 col-lg-4 mb-3">
                                                    <div class="card @(userPermission?.PermissionType == PermissionType.Grant ? "border-success" : userPermission?.PermissionType == PermissionType.Deny ? "border-danger" : "")">
                                                        <div class="card-body">
                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <div>
                                                                    <strong class="d-block">@permission.Name</strong>
                                                                    <small class="text-muted">@permission.Code</small>
                                                                </div>
                                                                @if (hasRolePermission)
                                                                {
                                                                    <span class="badge bg-info" title="این مجوز از طریق نقش اعطا شده">نقش</span>
                                                                }
                                                            </div>
                                                            
                                                            <div class="btn-group w-100" role="group">
                                                                <input type="radio" class="btn-check" 
                                                                       name="<EMAIL>" 
                                                                       value="none" 
                                                                       id="<EMAIL>"
                                                                       @(userPermission == null ? "checked" : "") />
                                                                <label class="btn btn-outline-secondary btn-sm" for="<EMAIL>">
                                                                    پیش‌فرض
                                                                </label>

                                                                <input type="radio" class="btn-check" 
                                                                       name="<EMAIL>" 
                                                                       value="grant" 
                                                                       id="<EMAIL>"
                                                                       @(userPermission?.PermissionType == PermissionType.Grant ? "checked" : "") />
                                                                <label class="btn btn-outline-success btn-sm" for="<EMAIL>">
                                                                    اعطا
                                                                </label>

                                                                <input type="radio" class="btn-check" 
                                                                       name="<EMAIL>" 
                                                                       value="deny" 
                                                                       id="<EMAIL>"
                                                                       @(userPermission?.PermissionType == PermissionType.Deny ? "checked" : "") />
                                                                <label class="btn btn-outline-danger btn-sm" for="<EMAIL>">
                                                                    منع
                                                                </label>
                                                            </div>
                                                            
                                                            @if (userPermission != null)
                                                            {
                                                                <small class="text-muted d-block mt-2">
                                                                    تاریخ: @userPermission.GrantedAt.ToString("yyyy/MM/dd HH:mm")
                                                                </small>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }
                                
                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-check-circle"></i>
                                        ذخیره تغییرات
                                    </button>
                                    <a asp-page="./Index" class="btn btn-secondary btn-lg">
                                        <i class="bi bi-x-circle"></i>
                                        انصراف
                                    </a>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-5">
                                    <i class="bi bi-shield-lock display-1 text-muted"></i>
                                    <h4 class="text-muted mt-3">هیچ مجوزی یافت نشد</h4>
                                    <p class="text-muted">ابتدا مجوزهای سیستم را تعریف کنید.</p>
                                </div>
                            }
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add visual feedback for permission changes
            const radioButtons = document.querySelectorAll('input[type="radio"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', function() {
                    const card = this.closest('.card');
                    card.classList.remove('border-success', 'border-danger');
                    
                    if (this.value === 'grant') {
                        card.classList.add('border-success');
                    } else if (this.value === 'deny') {
                        card.classList.add('border-danger');
                    }
                });
            });
        });
    </script>
}
