using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    public class DailyAttendanceModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly DailyAttendanceService _attendanceService;

        public DailyAttendanceModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            DailyAttendanceService attendanceService)
        {
            _context = context;
            _userManager = userManager;
            _attendanceService = attendanceService;
        }

        public List<DailyAttendance> Attendances { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public DateTime? ReportDate { get; set; } = DateTime.Today;

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        public int TotalEmployees { get; set; }
        public int PresentCount { get; set; }
        public int AbsentCount { get; set; }
        public int LateCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanViewReports(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            var date = ReportDate ?? DateTime.Today;

            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری حضور روزانه
            var attendanceQuery = _context.DailyAttendances
                                         .Include(da => da.Employee)
                                         .ThenInclude(e => e.Building)
                                         .Include(da => da.Employee)
                                         .ThenInclude(e => e.Job)
                                         .Where(da => da.Date.Date == date.Date);

            if (accessibleBuildingIds != null)
            {
                attendanceQuery = attendanceQuery.Where(da => accessibleBuildingIds.Contains(da.Employee.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                attendanceQuery = attendanceQuery.Where(da => da.Employee.BuildingId == BuildingId.Value);
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                attendanceQuery = attendanceQuery.Where(da =>
                    da.Employee.FirstName.ToLower().Contains(searchLower) ||
                    da.Employee.LastName.ToLower().Contains(searchLower) ||
                    da.Employee.PersonnelCode.ToLower().Contains(searchLower));
            }

            Attendances = await attendanceQuery.OrderBy(da => da.Employee.Building.Name)
                                              .ThenBy(da => da.Employee.FirstName)
                                              .ThenBy(da => da.Employee.LastName)
                                              .ToListAsync();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds, date);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds, DateTime date)
        {
            var attendanceQuery = _context.DailyAttendances
                                         .Include(da => da.Employee)
                                         .Where(da => da.Date.Date == date.Date);

            if (accessibleBuildingIds != null)
            {
                attendanceQuery = attendanceQuery.Where(da => accessibleBuildingIds.Contains(da.Employee.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                attendanceQuery = attendanceQuery.Where(da => da.Employee.BuildingId == BuildingId.Value);
            }

            var attendances = await attendanceQuery.ToListAsync();

            TotalEmployees = attendances.Count;
            PresentCount = attendances.Count(a => a.CheckInTime.HasValue);
            AbsentCount = TotalEmployees - PresentCount;
            LateCount = attendances.Count(a => a.LateMinutes > 0);
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanViewReports(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") || 
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }
    }
} 