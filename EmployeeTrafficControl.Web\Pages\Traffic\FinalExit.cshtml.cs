
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class FinalExitModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService;
        private readonly EmployeeStatusService _employeeStatusService;

        public FinalExitModel(ApplicationDbContext context, UserManager<ApplicationUser> userManager, TrafficLogService trafficLogService, EmployeeStatusService employeeStatusService)
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
            _employeeStatusService = employeeStatusService;
        }

        public List<EmployeeStatus> PresentEmployees { get; set; } = new List<EmployeeStatus>();

        public async Task<IActionResult> OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToPage("/Account/Login");

            PresentEmployees = await _employeeStatusService.GetPresentEmployeesAsync(user.BuildingId);

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(List<int> selectedEmployeeIds)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToPage("/Account/Login");

            var buildingId = user.BuildingId ?? 1;

            foreach (var employeeId in selectedEmployeeIds)
            {
                await _employeeStatusService.RegisterFinalExitAsync(employeeId, user.Id, "خروج نهایی گروهی");
            }

            TempData["SuccessMessage"] = $"خروج نهایی برای {selectedEmployeeIds.Count} کارمند با موفقیت ثبت شد.";
            return RedirectToPage();
        }
    }
}
