﻿// <auto-generated />
using System;
using EmployeeTrafficControl.Data.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250628182030_initialentry")]
    partial class initialentry
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("EmployeeTrafficControl")
                .HasAnnotation("ProductVersion", "8.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.ApplicationUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<int?>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<int?>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<int?>("RoleId")
                        .HasColumnType("int");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("BuildingId");

                    b.HasIndex("EmployeeId")
                        .IsUnique()
                        .HasFilter("[EmployeeId] IS NOT NULL");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserName")
                        .IsUnique();

                    b.ToTable("AspNetUsers", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Building", b =>
                {
                    b.Property<int>("BuildingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BuildingId"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("BuildingId");

                    b.ToTable("Buildings", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Car", b =>
                {
                    b.Property<int>("CarId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInParking")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMoneyTransport")
                        .HasColumnType("bit");

                    b.Property<string>("Model")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("PassengerCapacity")
                        .HasColumnType("int");

                    b.Property<string>("PlateNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("CarId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("PlateNumber")
                        .IsUnique();

                    b.ToTable("Cars", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarKilometer", b =>
                {
                    b.Property<int>("CarKilometerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarKilometerId"));

                    b.Property<int>("CarId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Date")
                        .HasColumnType("date");

                    b.Property<int?>("EndKilometer")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("StartKilometer")
                        .HasColumnType("int");

                    b.HasKey("CarKilometerId");

                    b.HasIndex("CarId");

                    b.HasIndex("CreatedByUserId");

                    b.ToTable("CarKilometers", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarPassenger", b =>
                {
                    b.Property<int>("CarPassengerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarPassengerId"));

                    b.Property<int>("CarTrafficLogId")
                        .HasColumnType("int");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<bool>("IsEntered")
                        .HasColumnType("bit");

                    b.HasKey("CarPassengerId");

                    b.HasIndex("CarTrafficLogId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("CarPassengers", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarTrafficLog", b =>
                {
                    b.Property<int>("CarTrafficLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarTrafficLogId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<int>("CarId")
                        .HasColumnType("int");

                    b.Property<int>("DriverEmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExitTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExitType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsInParking")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("CarTrafficLogId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("CarId");

                    b.HasIndex("DriverEmployeeId");

                    b.HasIndex("UserId");

                    b.ToTable("CarTrafficLogs", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.DailyAttendance", b =>
                {
                    b.Property<int>("AttendanceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AttendanceId"));

                    b.Property<DateTime?>("ApprovalTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ApprovedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CheckInTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CheckOutTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("EarlyLeaveMinutes")
                        .HasColumnType("int");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<int>("HourlyExitCount")
                        .HasColumnType("int");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPresent")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int>("LateMinutes")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("RegisteredByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("RegisteredTime")
                        .HasColumnType("datetime2");

                    b.Property<TimeSpan>("TotalHourlyExitTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("TotalWorkHours")
                        .HasColumnType("time");

                    b.HasKey("AttendanceId");

                    b.HasIndex("ApprovedByUserId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("RegisteredByUserId");

                    b.ToTable("DailyAttendances", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.DailyWorkSchedule", b =>
                {
                    b.Property<int>("ScheduleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ScheduleId"));

                    b.Property<string>("DayNamePersian")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("DayOfWeek")
                        .HasColumnType("int");

                    b.Property<bool>("IsWorkingDay")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LastUpdatedByUserId")
                        .HasColumnType("int");

                    b.Property<int>("MaxEarlyLeaveMinutes")
                        .HasColumnType("int");

                    b.Property<int>("MaxLateMinutes")
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("WorkEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("WorkStartTime")
                        .HasColumnType("time");

                    b.HasKey("ScheduleId");

                    b.HasIndex("LastUpdatedByUserId");

                    b.ToTable("DailyWorkSchedules", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Employee", b =>
                {
                    b.Property<int>("EmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EmployeeId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("HasDrivingLicense")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NationalCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("PersonnelCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("EmployeeId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("JobId");

                    b.HasIndex("NationalCode")
                        .IsUnique()
                        .HasFilter("[NationalCode] IS NOT NULL");

                    b.HasIndex("PersonnelCode")
                        .IsUnique();

                    b.ToTable("Employees", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.EmployeeStatus", b =>
                {
                    b.Property<int>("StatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StatusId"));

                    b.Property<int>("CurrentStatus")
                        .HasColumnType("int");

                    b.Property<int?>("CurrentVehicleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExitPermitNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("FinalExitTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("HasInitialEntry")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInVehicle")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPresentInBuilding")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("UpdatedByUserId")
                        .HasColumnType("int");

                    b.HasKey("StatusId");

                    b.HasIndex("CurrentVehicleId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("EmployeeStatuses", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.EmployeeWorkingHours", b =>
                {
                    b.Property<int>("EmployeeWorkingHoursId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EmployeeWorkingHoursId"));

                    b.Property<TimeSpan>("CustomEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("CustomStartTime")
                        .HasColumnType("time");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("date");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("date");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("EmployeeWorkingHoursId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("UserId");

                    b.ToTable("EmployeeWorkingHours", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.GuestCarTrafficLog", b =>
                {
                    b.Property<int>("GuestCarTrafficLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("GuestCarTrafficLogId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExitTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PlateNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("GuestCarTrafficLogId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("UserId");

                    b.ToTable("GuestCarTrafficLogs", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Job", b =>
                {
                    b.Property<int>("JobId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int?>("OwnerEmployeeId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("JobId");

                    b.HasIndex("BuildingId");

                    b.ToTable("Jobs", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Permission", b =>
                {
                    b.Property<int>("PermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PermissionId"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("PermissionId");

                    b.ToTable("Permissions", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Role", b =>
                {
                    b.Property<int>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RoleId"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystemRole")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("RoleId");

                    b.ToTable("Roles", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.RolePermission", b =>
                {
                    b.Property<int>("RolePermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RolePermissionId"));

                    b.Property<DateTime>("GrantedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("GrantedByUserId")
                        .HasColumnType("int");

                    b.Property<int>("PermissionId")
                        .HasColumnType("int");

                    b.Property<int?>("PermissionId1")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("RolePermissionId");

                    b.HasIndex("GrantedByUserId");

                    b.HasIndex("PermissionId");

                    b.HasIndex("PermissionId1");

                    b.HasIndex("RoleId", "PermissionId")
                        .IsUnique();

                    b.ToTable("RolePermissions", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.SystemSettings", b =>
                {
                    b.Property<int>("SettingsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettingsId"));

                    b.Property<bool>("AutoRegisterEntry")
                        .HasColumnType("bit");

                    b.Property<bool>("AutoRegisterExit")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LastUpdatedByUserId")
                        .HasColumnType("int");

                    b.Property<int>("MaxEarlyLeaveMinutes")
                        .HasColumnType("int");

                    b.Property<int>("MaxHourlyExitHours")
                        .HasColumnType("int");

                    b.Property<int>("MaxLateMinutes")
                        .HasColumnType("int");

                    b.Property<string>("OrganizationAddress")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("OrganizationEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("OrganizationPhone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("RequireApprovalForHourlyExit")
                        .HasColumnType("bit");

                    b.Property<TimeSpan>("ThursdayWorkEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("ThursdayWorkStartTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("WorkEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("WorkStartTime")
                        .HasColumnType("time");

                    b.Property<string>("WorkingDays")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("SettingsId");

                    b.HasIndex("LastUpdatedByUserId");

                    b.ToTable("SystemSettings", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.TrafficLog", b =>
                {
                    b.Property<int>("TrafficLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TrafficLogId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExitTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsAbsence")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAutomaticExit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEarlyExit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLate")
                        .HasColumnType("bit");

                    b.Property<string>("TrafficType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("TrafficLogId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("UserId");

                    b.ToTable("TrafficLogs", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.UserPermission", b =>
                {
                    b.Property<int>("UserPermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserPermissionId"));

                    b.Property<DateTime>("GrantedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("GrantedByUserId")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("PermissionId")
                        .HasColumnType("int");

                    b.Property<int?>("PermissionId1")
                        .HasColumnType("int");

                    b.Property<int>("PermissionType")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("UserPermissionId");

                    b.HasIndex("GrantedByUserId");

                    b.HasIndex("PermissionId");

                    b.HasIndex("PermissionId1");

                    b.HasIndex("UserId", "PermissionId")
                        .IsUnique();

                    b.ToTable("UserPermissions", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.UserSession", b =>
                {
                    b.Property<int>("SessionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SessionId"));

                    b.Property<DateTime>("ExpiryTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExpired")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastActivity")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LoginTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LogoutTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("SessionToken")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("SessionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSessions", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.WorkingHoursSetting", b =>
                {
                    b.Property<int>("SettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettingId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("DefaultEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("DefaultStartTime")
                        .HasColumnType("time");

                    b.Property<int>("EarlyExitToleranceMinutes")
                        .HasColumnType("int");

                    b.Property<int>("LateEntryToleranceMinutes")
                        .HasColumnType("int");

                    b.HasKey("SettingId");

                    b.HasIndex("BuildingId")
                        .IsUnique();

                    b.ToTable("WorkingHoursSetting", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", "EmployeeTrafficControl");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.ApplicationUser", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("Users")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "Employee")
                        .WithOne("User")
                        .HasForeignKey("EmployeeTrafficControl.Data.Models.ApplicationUser", "EmployeeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("EmployeeTrafficControl.Data.Models.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Building");

                    b.Navigation("Employee");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Car", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("Cars")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Building");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarKilometer", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Car", "Car")
                        .WithMany("CarKilometers")
                        .HasForeignKey("CarId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Car");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarPassenger", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.CarTrafficLog", "CarTrafficLog")
                        .WithMany("CarPassengers")
                        .HasForeignKey("CarTrafficLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "Employee")
                        .WithMany("CarPassengers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CarTrafficLog");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarTrafficLog", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("CarTrafficLogs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Car", "Car")
                        .WithMany("CarTrafficLogs")
                        .HasForeignKey("CarId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "DriverEmployee")
                        .WithMany("CarTrafficLogsAsDriver")
                        .HasForeignKey("DriverEmployeeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("Car");

                    b.Navigation("DriverEmployee");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.DailyAttendance", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "ApprovedByUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByUserId");

                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "RegisteredByUser")
                        .WithMany()
                        .HasForeignKey("RegisteredByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedByUser");

                    b.Navigation("Employee");

                    b.Navigation("RegisteredByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.DailyWorkSchedule", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "LastUpdatedByUser")
                        .WithMany()
                        .HasForeignKey("LastUpdatedByUserId");

                    b.Navigation("LastUpdatedByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Employee", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("Employees")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Job", "Job")
                        .WithMany("Employees")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.EmployeeStatus", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Car", "CurrentVehicle")
                        .WithMany()
                        .HasForeignKey("CurrentVehicleId");

                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId");

                    b.Navigation("CurrentVehicle");

                    b.Navigation("Employee");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.EmployeeWorkingHours", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "Employee")
                        .WithMany("EmployeeWorkingHours")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.GuestCarTrafficLog", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("GuestCarTrafficLogs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Job", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("Jobs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Building");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.RolePermission", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "GrantedByUser")
                        .WithMany()
                        .HasForeignKey("GrantedByUserId");

                    b.HasOne("EmployeeTrafficControl.Data.Models.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Permission", null)
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId1");

                    b.HasOne("EmployeeTrafficControl.Data.Models.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GrantedByUser");

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.SystemSettings", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "LastUpdatedByUser")
                        .WithMany()
                        .HasForeignKey("LastUpdatedByUserId");

                    b.Navigation("LastUpdatedByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.TrafficLog", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithMany("TrafficLogs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Employee", "Employee")
                        .WithMany("TrafficLogs")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("Employee");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.UserPermission", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "GrantedByUser")
                        .WithMany()
                        .HasForeignKey("GrantedByUserId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("EmployeeTrafficControl.Data.Models.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.Permission", null)
                        .WithMany("UserPermissions")
                        .HasForeignKey("PermissionId1");

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "User")
                        .WithMany("UserPermissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GrantedByUser");

                    b.Navigation("Permission");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.UserSession", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.WorkingHoursSetting", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.Building", "Building")
                        .WithOne("WorkingHoursSetting")
                        .HasForeignKey("EmployeeTrafficControl.Data.Models.WorkingHoursSetting", "BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.ApplicationUser", b =>
                {
                    b.Navigation("UserPermissions");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Building", b =>
                {
                    b.Navigation("CarTrafficLogs");

                    b.Navigation("Cars");

                    b.Navigation("Employees");

                    b.Navigation("GuestCarTrafficLogs");

                    b.Navigation("Jobs");

                    b.Navigation("TrafficLogs");

                    b.Navigation("Users");

                    b.Navigation("WorkingHoursSetting");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Car", b =>
                {
                    b.Navigation("CarKilometers");

                    b.Navigation("CarTrafficLogs");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.CarTrafficLog", b =>
                {
                    b.Navigation("CarPassengers");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Employee", b =>
                {
                    b.Navigation("CarPassengers");

                    b.Navigation("CarTrafficLogsAsDriver");

                    b.Navigation("EmployeeWorkingHours");

                    b.Navigation("TrafficLogs");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Job", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Permission", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserPermissions");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Data.Models.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
