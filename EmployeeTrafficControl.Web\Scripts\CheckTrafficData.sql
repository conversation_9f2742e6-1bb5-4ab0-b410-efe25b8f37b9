-- ==========================================
-- اسکریپت بررسی داده‌های تردد
-- Employee Traffic Control System
-- ==========================================

USE [EmployeeTrafficControlDb]
GO

PRINT '=========================================='
PRINT 'بررسی داده‌های تردد در دیتابیس'
PRINT 'تاریخ اجرا: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- 1. بررسی کل ترددهای کارمندان
PRINT '1. آمار کلی ترددهای کارمندان:'
SELECT 
    COUNT(*) AS [کل_ترددها],
    COUNT(CASE WHEN EntryTime IS NOT NULL THEN 1 END) AS [ورودها],
    COUNT(CASE WHEN ExitTime IS NOT NULL THEN 1 END) AS [خروجها],
    COUNT(CASE WHEN TrafficType IS NOT NULL THEN 1 END) AS [دارای_نوع_تردد],
    COUNT(CASE WHEN TrafficType IS NULL THEN 1 END) AS [بدون_نوع_تردد]
FROM TrafficLogs

-- 2. بررسی انواع ترددها
PRINT '2. انواع ترددهای ثبت شده:'
SELECT 
    ISNULL(TrafficType, 'NULL') AS [نوع_تردد],
    COUNT(*) AS [تعداد],
    COUNT(CASE WHEN EntryTime IS NOT NULL THEN 1 END) AS [ورودها],
    COUNT(CASE WHEN ExitTime IS NOT NULL THEN 1 END) AS [خروجها]
FROM TrafficLogs
GROUP BY TrafficType
ORDER BY COUNT(*) DESC

-- 3. بررسی ترددهای اخیر (10 روز گذشته)
PRINT '3. ترددهای اخیر (10 روز گذشته):'
SELECT TOP 20
    e.FirstName + ' ' + e.LastName AS [نام_کارمند],
    CASE 
        WHEN tl.EntryTime IS NOT NULL THEN 'ورود'
        WHEN tl.ExitTime IS NOT NULL THEN 'خروج'
        ELSE 'نامشخص'
    END AS [نوع_عملیات],
    ISNULL(tl.EntryTime, tl.ExitTime) AS [زمان],
    ISNULL(tl.TrafficType, 'بدون نوع') AS [نوع_تردد],
    ISNULL(tl.Description, 'بدون توضیح') AS [توضیحات]
FROM TrafficLogs tl
INNER JOIN Employees e ON tl.EmployeeId = e.EmployeeId
WHERE (tl.EntryTime >= DATEADD(DAY, -10, GETDATE()) OR tl.ExitTime >= DATEADD(DAY, -10, GETDATE()))
ORDER BY ISNULL(tl.EntryTime, tl.ExitTime) DESC

-- 4. بررسی خروج‌های ساعتی و ماموریت‌ها
PRINT '4. خروج‌های ساعتی و ماموریت‌ها:'
SELECT 
    CASE 
        WHEN TrafficType LIKE '%خروج ساعتی%' OR TrafficType LIKE '%hourly%' OR TrafficType LIKE '%مرخصی%' THEN 'خروج ساعتی'
        WHEN TrafficType LIKE '%ماموریت%' OR TrafficType LIKE '%mission%' THEN 'ماموریت'
        ELSE 'سایر'
    END AS [دسته_بندی],
    COUNT(*) AS [تعداد],
    TrafficType AS [نوع_دقیق]
FROM TrafficLogs
WHERE TrafficType IS NOT NULL
GROUP BY TrafficType
ORDER BY COUNT(*) DESC

-- 5. بررسی ترددهای یک کارمند خاص (اولین کارمند)
DECLARE @FirstEmployeeId INT
SELECT TOP 1 @FirstEmployeeId = EmployeeId FROM Employees ORDER BY EmployeeId

IF @FirstEmployeeId IS NOT NULL
BEGIN
    PRINT '5. ترددهای کارمند شماره ' + CAST(@FirstEmployeeId AS VARCHAR) + ':'
    
    SELECT 
        e.FirstName + ' ' + e.LastName AS [نام_کارمند],
        CONVERT(DATE, ISNULL(tl.EntryTime, tl.ExitTime)) AS [تاریخ],
        CASE 
            WHEN tl.EntryTime IS NOT NULL THEN 'ورود - ' + FORMAT(tl.EntryTime, 'HH:mm')
            WHEN tl.ExitTime IS NOT NULL THEN 'خروج - ' + FORMAT(tl.ExitTime, 'HH:mm')
            ELSE 'نامشخص'
        END AS [عملیات],
        ISNULL(tl.TrafficType, 'بدون نوع') AS [نوع_تردد],
        ISNULL(tl.Description, 'بدون توضیح') AS [توضیحات]
    FROM TrafficLogs tl
    INNER JOIN Employees e ON tl.EmployeeId = e.EmployeeId
    WHERE tl.EmployeeId = @FirstEmployeeId
        AND (tl.EntryTime >= DATEADD(DAY, -30, GETDATE()) OR tl.ExitTime >= DATEADD(DAY, -30, GETDATE()))
    ORDER BY ISNULL(tl.EntryTime, tl.ExitTime) DESC
END

-- 6. بررسی وضعیت‌های کارمندان
PRINT '6. آمار وضعیت‌های کارمندان:'
SELECT 
    COUNT(*) AS [کل_وضعیت_ها],
    COUNT(CASE WHEN EntryTime IS NOT NULL THEN 1 END) AS [دارای_ورود],
    COUNT(CASE WHEN FinalExitTime IS NOT NULL THEN 1 END) AS [دارای_خروج_نهایی],
    COUNT(CASE WHEN Date >= DATEADD(DAY, -7, GETDATE()) THEN 1 END) AS [هفته_اخیر]
FROM EmployeeStatuses

-- 7. بررسی ترددهای خودرو
PRINT '7. آمار ترددهای خودرو:'
SELECT 
    COUNT(*) AS [کل_ترددهای_خودرو],
    COUNT(CASE WHEN EntryTime IS NOT NULL THEN 1 END) AS [ورودهای_خودرو],
    COUNT(CASE WHEN ExitTime IS NOT NULL THEN 1 END) AS [خروجهای_خودرو],
    COUNT(CASE WHEN ExitTime >= DATEADD(DAY, -7, GETDATE()) OR EntryTime >= DATEADD(DAY, -7, GETDATE()) THEN 1 END) AS [هفته_اخیر]
FROM CarTrafficLogs

-- 8. نمونه داده‌های تست برای بررسی
PRINT '8. پیشنهاد داده‌های تست:'
PRINT 'برای تست خروج ساعتی، یک رکورد با TrafficType = "خروج ساعتی" اضافه کنید:'
PRINT 'INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description) VALUES (1, 1, GETDATE(), ''خروج ساعتی'', ''تست خروج ساعتی'')'
PRINT ''
PRINT 'برای تست ماموریت، یک رکورد با TrafficType = "ماموریت" اضافه کنید:'
PRINT 'INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description) VALUES (1, 1, GETDATE(), ''ماموریت'', ''تست ماموریت'')'

PRINT '=========================================='
PRINT 'پایان بررسی'
PRINT '=========================================='
