﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class initialentry : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "HasInitialEntry",
                schema: "EmployeeTrafficControl",
                table: "EmployeeStatuses",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsPresentInBuilding",
                schema: "EmployeeTrafficControl",
                table: "EmployeeStatuses",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasInitialEntry",
                schema: "EmployeeTrafficControl",
                table: "EmployeeStatuses");

            migrationBuilder.DropColumn(
                name: "IsPresentInBuilding",
                schema: "EmployeeTrafficControl",
                table: "EmployeeStatuses");
        }
    }
}
