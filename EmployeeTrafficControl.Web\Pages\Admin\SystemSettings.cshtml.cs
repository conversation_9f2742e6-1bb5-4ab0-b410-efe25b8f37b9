using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Core.Services;
using Microsoft.AspNetCore.Identity;

namespace EmployeeTrafficControl.Web.Pages.Admin
{
    [AuthorizePermission("MANAGE_SETTINGS")]
    public class SystemSettingsModel : PageModel
    {
        private readonly SystemSettingsService _systemSettingsService;
        private readonly AuthenticationService _authService;
        private readonly UserManager<ApplicationUser> _userManager;

        public SystemSettingsModel(SystemSettingsService systemSettingsService, AuthenticationService authService, UserManager<ApplicationUser> userManager)
        {
            _systemSettingsService = systemSettingsService;
            _authService = authService;
            _userManager = userManager;
        }

        [BindProperty]
        public SystemSettings Settings { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            Settings = await _systemSettingsService.GetSystemSettingsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // اعتبارسنجی سفارشی
            if (Settings.WorkStartTime >= Settings.WorkEndTime)
            {
                ModelState.AddModelError("Settings.WorkEndTime", "ساعت پایان کار (شنبه تا چهارشنبه) باید بعد از ساعت شروع کار باشد.");
            }

            if (Settings.ThursdayWorkStartTime >= Settings.ThursdayWorkEndTime)
            {
                ModelState.AddModelError("Settings.ThursdayWorkEndTime", "ساعت پایان کار پنج‌شنبه باید بعد از ساعت شروع کار باشد.");
            }

            if (string.IsNullOrEmpty(Settings.WorkingDays))
            {
                ModelState.AddModelError("Settings.WorkingDays", "حداقل یک روز کاری باید انتخاب شود.");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // دریافت شناسه کاربر فعلی با استفاده از Microsoft Identity
                var userId = _userManager.GetUserId(User);
                if (string.IsNullOrEmpty(userId))
                {
                    TempData["ErrorMessage"] = "خطا در شناسایی کاربر. لطفاً دوباره وارد شوید.";
                    return RedirectToPage("/Account/Login");
                }

                // به‌روزرسانی تنظیمات
                bool result = await _systemSettingsService.UpdateSystemSettingsAsync(Settings, Convert.ToInt32(userId));

                if (result)
                {
                    TempData["SuccessMessage"] = "تنظیمات سیستم با موفقیت به‌روزرسانی شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "خطا در ذخیره تنظیمات.";
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره تنظیمات: " + ex.Message);
                return Page();
            }
        }
    }
}
