using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Web.Extensions
{
    /// <summary>
    /// راه‌اندازی اولیه Identity
    /// </summary>
    public static class IdentityInitializer
    {
        /// <summary>
        /// ایجاد نقش‌ها و کاربر ادمین پیش‌فرض
        /// </summary>
        public static async Task InitializeAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole<int>>>();

            // ایجاد نقش‌ها
            await CreateRolesAsync(roleManager);

            // ایجاد کاربر ادمین
            await CreateAdminUserAsync(userManager);
        }

        private static async Task CreateRolesAsync(RoleManager<IdentityRole<int>> roleManager)
        {
            string[] roles = { "Admin", "Manager", "User" };

            foreach (var role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role))
                {
                    await roleManager.CreateAsync(new IdentityRole<int> { Name = role });
                }
            }
        }

        private static async Task CreateAdminUserAsync(UserManager<ApplicationUser> userManager)
        {
            const string adminUsername = "admin";
            const string adminPassword = "admin123";
            const string adminEmail = "<EMAIL>";

            var adminUser = await userManager.FindByNameAsync(adminUsername);
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminUsername,
                    Email = adminEmail,
                    FullName = "مدیر سیستم",
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(adminUser, adminPassword);
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }
        }
    }
}

