@page "/Account/AccessDenied"
@model EmployeeTrafficControl.Web.Pages.Account.AccessDeniedModel
@{
    ViewData["Title"] = "دسترسی غیرمجاز";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header text-center">
            <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
            <h2 class="mt-3">دسترسی غیرمجاز</h2>
            <p class="text-muted">شما دسترسی لازم برای مشاهده این صفحه را ندارید.</p>
        </div>

        <div class="text-center mt-4">
            <a href="/Dashboard" class="btn btn-primary">
                <i class="bi bi-house"></i>
                بازگشت به داشبورد
            </a>
            <a href="/login" class="btn btn-outline-secondary ms-2">
                <i class="bi bi-box-arrow-in-right"></i>
                ورود مجدد
            </a>
        </div>
    </div>
</div>
