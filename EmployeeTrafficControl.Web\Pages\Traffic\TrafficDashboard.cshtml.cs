
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Identity;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class TrafficDashboardModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly TrafficLogService _trafficLogService;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly UserManager<ApplicationUser> _userManager;

        public TrafficDashboardModel(ApplicationDbContext context, TrafficLogService trafficLogService, EmployeeStatusService employeeStatusService, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _trafficLogService = trafficLogService;
            _employeeStatusService = employeeStatusService;
            _userManager = userManager;
        }

        [BindProperty(SupportsGet = true)]
        public string SearchTerm { get; set; }

        public SearchResultModel SearchResult { get; set; }

        public List<Employee> AvailableEmployees { get; set; } = new List<Employee>();

        public void OnGet()
        {
        }

        public async Task<IActionResult> OnPostSearchAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                return Page();
            }

            SearchResult = new SearchResultModel();

            // Search for a car by name, plate or model
            SearchResult.Car = await _context.Cars
                .FirstOrDefaultAsync(c => c.Type.Contains(SearchTerm) || c.PlateNumber.Contains(SearchTerm) || c.Model.Contains(SearchTerm));

            // Search for an employee
            SearchResult.Employee = await _context.Employees
                .FirstOrDefaultAsync(e => (e.FirstName + " " + e.LastName).Contains(SearchTerm) || e.PersonnelCode.Contains(SearchTerm));

            if (SearchResult.Employee != null)
            {
                SearchResult.EmployeeStatus = await _employeeStatusService.GetEmployeeStatusAsync(SearchResult.Employee.EmployeeId);
            }

            if (SearchResult.Car != null && SearchResult.Car.IsInParking)
            {
                // Load available employees for exit registration
                var presentStatuses = await _employeeStatusService.GetPresentEmployeesAsync();
                if(presentStatuses?.Any() ?? false)
                    AvailableEmployees = presentStatuses.Select(es => es.Employee).ToList();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostRegisterCarExitAsync(int carId, int driverId, List<int> passengerIds, string notes)
        {
            var user = await _userManager.GetUserAsync(User);
            var building = await _context.Buildings.FirstOrDefaultAsync();
            if (building != null && user != null) {
                await _trafficLogService.RegisterCarExitAsync(carId, driverId, passengerIds, building.BuildingId, user.Id, notes);
            }
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterCarEntryAsync(int carId, List<int> passengerIds)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user != null) {
                await _trafficLogService.RegisterCarEntryAsync(carId, user.Id, passengerIds);
            }
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterIndividualExitAsync(int employeeId)
        {
            var user = await _userManager.GetUserAsync(User);
            var building = await _context.Buildings.FirstOrDefaultAsync();
            await _employeeStatusService.RegisterHourlyExitAsync(employeeId, user.Id, null, "خروج ساعتی", null);
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterIndividualEntryAsync(int employeeId)
        {
            var user = await _userManager.GetUserAsync(User);
            await _employeeStatusService.RegisterEmployeeEntryAsync(employeeId, user.Id);
            return RedirectToPage();
        }
    }

    public class SearchResultModel
    {
        public Car Car { get; set; }
        public Employee Employee { get; set; }
        public EmployeeStatus EmployeeStatus { get; set; }
    }
}
