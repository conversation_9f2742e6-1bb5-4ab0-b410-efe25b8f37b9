@page
@model EmployeeTrafficControl.Web.Pages.Permissions.IndexModel
@{
    ViewData["Title"] = "مدیریت مجوزها";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-shield-lock"></i>
                        مدیریت مجوزها
                    </h4>
                    <a asp-page="./Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        مجوز جدید
                    </a>
                </div>
                <div class="card-body">
                    @if (Model.Permissions.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>نام مجوز</th>
                                        <th>کد مجوز</th>
                                        <th>توضیحات</th>
                                        <th>وضعیت</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var permission in Model.Permissions)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@permission.Name</strong>
                                            </td>
                                            <td>
                                                <code class="text-muted">@permission.Code</code>
                                            </td>
                                            <td>
                                                @(permission.Description ?? "-")
                                            </td>
                                            <td>
                                                @if (permission.IsActive)
                                                {
                                                    <span class="badge bg-success">فعال</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غیرفعال</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Details" asp-route-id="@permission.PermissionId" 
                                                       class="btn btn-sm btn-outline-info" title="جزئیات">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a asp-page="./Edit" asp-route-id="@permission.PermissionId" 
                                                       class="btn btn-sm btn-outline-warning" title="ویرایش">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    @if (!permission.IsActive)
                                                    {
                                                        <a asp-page="./Delete" asp-route-id="@permission.PermissionId" 
                                                           class="btn btn-sm btn-outline-danger" title="حذف">
                                                            <i class="bi bi-trash"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-shield-lock display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">هیچ مجوزی یافت نشد</h4>
                            <p class="text-muted">برای شروع، مجوز جدیدی ایجاد کنید.</p>
                            <a asp-page="./Create" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i>
                                ایجاد اولین مجوز
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-refresh functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add any JavaScript functionality here if needed
        });
    </script>
} 