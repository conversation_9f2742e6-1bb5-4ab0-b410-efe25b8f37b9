using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using EmployeeTrafficControl.Core.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class CreateModel : PageModel
    {
        private readonly CarService _carService;
        private readonly BuildingService _buildingService;

        public CreateModel(CarService carService, BuildingService buildingService)
        {
            _carService = carService;
            _buildingService = buildingService;
        }

        [BindProperty]
        public Car Car { get; set; } = default!;

        public List<SelectListItem> Buildings { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            Car = new Car
            {
                PassengerCapacity = 5, // Default capacity
                IsInParking = true // Default to in parking
            };
            await LoadBuildingsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // اعتبارسنجی سفارشی
            if (Car.BuildingId <= 0)
            {
                ModelState.AddModelError("Car.BuildingId", "انتخاب ساختمان اجباری است.");
            }

            if (!ModelState.IsValid)
            {
                await LoadBuildingsAsync();
                return Page();
            }

            // Check if car plate number already exists
            bool carExists = await _carService.CarExistsAsync(Car.PlateNumber, null);
            if (carExists)
            {
                ModelState.AddModelError("Car.PlateNumber", "شماره پلاک وارد شده قبلاً ثبت شده است.");
                await LoadBuildingsAsync();
                return Page();
            }

            try
            {
                await _carService.AddCarAsync(Car);
                TempData["SuccessMessage"] = "خودرو جدید با موفقیت اضافه شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadBuildingsAsync();
                return Page();
            }
        }

        private async Task LoadBuildingsAsync()
        {
            var buildings = await _buildingService.GetAllBuildingsAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name
            }).ToList();
        }
    }
}
