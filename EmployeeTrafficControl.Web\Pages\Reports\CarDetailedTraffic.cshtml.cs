using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Models;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    [AuthorizePermission("VIEW_REPORTS")]
    public class CarDetailedTrafficModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly ReportService _reportService;
        private readonly UserManager<ApplicationUser> _userManager;

        public CarDetailedTrafficModel(
            ApplicationDbContext context,
            ReportService reportService,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _reportService = reportService;
            _userManager = userManager;
        }

        [BindProperty(SupportsGet = true)]
        public int? CarId { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? FromDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? ToDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        public CarDetailedTrafficReportData? ReportData { get; set; }
        public List<Car> AvailableCars { get; set; } = [];
        public List<Building> AvailableBuildings { get; set; } = [];

        public async Task OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return;

            // تنظیم مقادیر پیش‌فرض
            FromDate ??= DateTime.Today.AddDays(-30);
            ToDate ??= DateTime.Today;

            // بارگذاری لیست خودروها و ساختمان‌ها
            await LoadAvailableData(user);

            // تولید گزارش در صورت انتخاب خودرو
            if (CarId.HasValue)
            {
                ReportData = await _reportService.GetCarDetailedTrafficReportAsync(
                    CarId.Value, FromDate.Value, ToDate.Value);
            }
        }

        private async Task LoadAvailableData(ApplicationUser user)
        {
            // دسترسی به ساختمان‌ها
            var accessibleBuildingIds = user.BuildingId.HasValue 
                ? new[] { user.BuildingId.Value } 
                : null;

            // بارگذاری ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }
            AvailableBuildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();

            // بارگذاری خودروها
            var carsQuery = _context.Cars
                .Include(c => c.Building)
                .AsQueryable();

            if (accessibleBuildingIds != null)
            {
                carsQuery = carsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                carsQuery = carsQuery.Where(c =>
                    c.PlateNumber.ToLower().Contains(searchLower) ||
                    (c.Model != null && c.Model.ToLower().Contains(searchLower)) ||
                    (c.Type != null && c.Type.ToLower().Contains(searchLower)));
            }

            AvailableCars = await carsQuery
                .OrderBy(c => c.PlateNumber)
                .ToListAsync();
        }

        public async Task<IActionResult> OnPostExportAsync()
        {
            if (!CarId.HasValue || !FromDate.HasValue || !ToDate.HasValue)
            {
                TempData["ErrorMessage"] = "لطفاً خودرو و بازه زمانی را انتخاب کنید.";
                return RedirectToPage();
            }

            var reportData = await _reportService.GetCarDetailedTrafficReportAsync(
                CarId.Value, FromDate.Value, ToDate.Value);

            // اینجا می‌توانید export به Excel یا PDF اضافه کنید
            TempData["SuccessMessage"] = "گزارش آماده شد.";
            return RedirectToPage();
        }
    }
}
