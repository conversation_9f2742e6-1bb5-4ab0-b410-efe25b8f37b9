﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class Job
    {
        [Key]
        [Display(Name = "شناسه شغل")] // Display name might also need update
        public int JobId { get; set; } // Renamed from Id

        [Required(ErrorMessage = "نام شغل اجباری است.")]
        [StringLength(100, ErrorMessage = "نام شغل حداکثر 100 کاراکتر باشد.")]
        [Display(Name = "نام شغل")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "کد شغل اجباری است.")]
        [StringLength(50, ErrorMessage = "کد شغل حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "کد شغل")]
        public string Code { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "توضیحات حداکثر 500 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Description { get; set; }

        // Foreign key for Building
        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }
        [ForeignKey("BuildingId")]
        public Building Building { get; set; } = null!; // Made non-nullable as BuildingId is non-nullable

        // Navigation property for Employees
        public ICollection<Employee> Employees { get; set; } = new List<Employee>();

        [Display(Name = "مالک (کارمند)")]
public int? OwnerEmployeeId { get; set; }

[Display(Name = "فعال است؟")]
public bool IsActive { get; set; } = true;
    }
}