using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;


namespace EmployeeTrafficControl.Web.Pages.Jobs
{
    [AuthorizePermission("EDIT_JOB")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class EditModel : PageModel
    {
        private readonly JobService _jobService;
        private readonly BuildingService _buildingService;

        public EditModel(JobService jobService, BuildingService buildingService)
        {
            _jobService = jobService;
            _buildingService = buildingService;
        }

        [BindProperty]
        public Job Job { get; set; } = default!;

        public SelectList Buildings { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Job = await _jobService.GetJobByIdAsync(id);

            if (Job == null)
            {
                TempData["ErrorMessage"] = "شغل مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            await LoadBuildingsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // Remove Building validation errors since we only need BuildingId
            ModelState.Remove("Job.Building");

            if (!ModelState.IsValid)
            {
                await LoadBuildingsAsync();
                return Page();
            }

            // Check if job title already exists in the same building (excluding current job)
            bool jobExists = await _jobService.JobExistsInBuildingAsync(Job.Title, Job.BuildingId, Job.JobId);
            if (jobExists)
            {
                ModelState.AddModelError("Job.Title", "عنوان شغل وارد شده در این ساختمان قبلاً ثبت شده است.");
                await LoadBuildingsAsync();
                return Page();
            }

            try
            {
                await _jobService.UpdateJobAsync(Job);
                TempData["SuccessMessage"] = "اطلاعات شغل با موفقیت به‌روزرسانی شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadBuildingsAsync();
                return Page();
            }
        }

        private async Task LoadBuildingsAsync()
        {
            var buildings = await _buildingService.GetAllBuildingsAsync();
            Buildings = new SelectList(buildings, "BuildingId", "Name", Job.BuildingId);
        }
    }
}
