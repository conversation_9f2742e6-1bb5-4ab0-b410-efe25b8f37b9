using EmployeeTrafficControl.Data.Models; // Corrected namespace

using Microsoft.AspNetCore.Identity;

namespace EmployeeTrafficControl.Data.Models
{
    /// <summary>
    /// کلاس کاربر سفارشی برای Microsoft Identity
    /// </summary>
    public class ApplicationUser : IdentityUser<int>
    {
        /// <summary>
        /// نام کامل کاربر
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// آیا کاربر فعال است
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاریخ ایجاد
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// شناسه کارمند مرتبط (اختیاری)
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// کارمند مرتبط
        /// </summary>
        public virtual Employee? Employee { get; set; }

        /// <summary>
        /// شناسه ساختمان (برای محدود کردن دسترسی)
        /// </summary>
        public int? BuildingId { get; set; }

        /// <summary>
        /// ساختمان مرتبط
        /// </summary>
        public virtual Building? Building { get; set; }

        /// <summary>
        /// شناسه نقش کاربر (اختیاری)
        /// </summary>
        public int? RoleId { get; set; }

        /// <summary>
        /// نقش کاربر
        /// </summary>
        public virtual Role? Role { get; set; }

        /// <summary>
        /// نام کاربری (Username)
        /// </summary>
        public override string UserName { get; set; } = string.Empty; // Initialized to avoid null warning

        // New property for UserPermissions
        public ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }
}