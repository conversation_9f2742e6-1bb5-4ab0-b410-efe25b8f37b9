using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    [AuthorizePermission("EDIT_BUILDING")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class EditModel : PageModel
    {
        private readonly BuildingService _buildingService;

        public EditModel(BuildingService buildingService)
        {
            _buildingService = buildingService;
        }

        [BindProperty]
        public Building Building { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Building = await _buildingService.GetBuildingByIdAsync(id);

            if (Building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Check if building name already exists (excluding current building)
            bool buildingExists = await _buildingService.BuildingExistsAsync(Building.Name, Building.BuildingId);
            if (buildingExists)
            {
                ModelState.AddModelError("Building.Name", "نام ساختمان وارد شده قبلاً ثبت شده است.");
                return Page();
            }

            try
            {
                await _buildingService.UpdateBuildingAsync(Building);
                TempData["SuccessMessage"] = "اطلاعات ساختمان با موفقیت به‌روزرسانی شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                return Page();
            }
        }
    }
}
