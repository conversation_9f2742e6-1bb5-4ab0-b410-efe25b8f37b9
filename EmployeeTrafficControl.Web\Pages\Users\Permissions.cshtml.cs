 using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    public class PermissionsModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public PermissionsModel(ApplicationDbContext context)
        {
            _context = context;
        }

        [BindProperty]
        public int UserId { get; set; }

        public new ApplicationUser User { get; set; } = null!;

        public IList<Permission> AllPermissions { get; set; } = new List<Permission>();
        public Dictionary<string, List<Permission>> RolePermissionsByCategory { get; set; } = new();
        public Dictionary<string, List<Permission>> AllPermissionsByCategory { get; set; } = new();
        public List<UserPermission> UserPermissions { get; set; } = new();
        public List<int> RolePermissionIds { get; set; } = new();

        [BindProperty]
        public List<string> SelectedPermissions { get; set; } = new List<string>();

        public async Task<IActionResult> OnGetAsync(int userId)
        {
            if (userId == 0)
            {
                return NotFound();
            }

            User = await _context.Users
                .Include(u => u.Role)
                .Include(u => u.Employee)
                .FirstOrDefaultAsync(u => u.Id == userId);
            
            if (User == null)
            {
                return NotFound();
            }

            // بارگذاری مجوزهای نقش
            if (User.Role != null)
            {
                var rolePermissions = await _context.RolePermissions
                    .Include(rp => rp.Permission)
                    .Where(rp => rp.RoleId == User.Role.RoleId)
                    .Select(rp => rp.Permission)
                    .ToListAsync();

                RolePermissionsByCategory = rolePermissions
                    .GroupBy(p => p.Category ?? "عمومی")
                    .ToDictionary(g => g.Key, g => g.ToList());

                RolePermissionIds = rolePermissions.Select(p => p.PermissionId).ToList();
            }

            // بارگذاری همه مجوزها
            AllPermissions = await _context.Permissions.ToListAsync();
            AllPermissionsByCategory = AllPermissions
                .GroupBy(p => p.Category ?? "عمومی")
                .ToDictionary(g => g.Key, g => g.ToList());

            // بارگذاری مجوزهای خاص کاربر
            UserPermissions = await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .ToListAsync();

            SelectedPermissions = UserPermissions
                .Select(up => up.PermissionId.ToString())
                .ToList();

            UserId = userId;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (UserId == 0)
            {
                return NotFound();
            }

            var user = await _context.Users.FindAsync(UserId);
            if (user == null)
            {
                return NotFound();
            }

            // Remove existing permissions
            var userPermissions = _context.UserPermissions.Where(up => up.UserId == UserId);
            _context.UserPermissions.RemoveRange(userPermissions);

            // Add selected permissions
            if (SelectedPermissions != null)
            {
                foreach (var permissionId in SelectedPermissions)
                {
                    if (int.TryParse(permissionId, out int permId))
                    {
                        _context.UserPermissions.Add(new UserPermission
                        {
                            UserId = UserId,
                            PermissionId = permId
                        });
                    }
                }
            }

            await _context.SaveChangesAsync();

            return RedirectToPage("./Index");
        }
    }
}