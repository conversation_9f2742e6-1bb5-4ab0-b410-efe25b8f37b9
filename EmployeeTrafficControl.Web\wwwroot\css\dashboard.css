/* Dashboard specific styles */

/* Dashboard header */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px 0;
  margin-bottom: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

.dashboard-date {
  text-align: left;
  font-size: 1rem;
  opacity: 0.9;
}

/* Stats cards */
.stats-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  text-decoration: none;
  color: inherit;
}

.stats-card .stats-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.8;
}

.stats-card h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stats-card p {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.stats-card small {
  opacity: 0.7;
}

.stats-present {
  border-right: 5px solid #28a745;
}

.stats-present .stats-icon {
  color: #28a745;
}

.stats-absent {
  border-right: 5px solid #dc3545;
}

.stats-absent .stats-icon {
  color: #dc3545;
}

.stats-hourly-exit {
  border-right: 5px solid #ffc107;
}

.stats-hourly-exit .stats-icon {
  color: #ffc107;
}

.stats-late {
  border-right: 5px solid #fd7e14;
}

.stats-late .stats-icon {
  color: #fd7e14;
}

/* Quick operations */
.quick-operations-column {
  background: white;
  border-radius: 15px;
  padding: 20px;
  height: 100%;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.column-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.column-header i {
  font-size: 1.5rem;
  margin-left: 10px;
}

.column-header h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
}

.column-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.quick-action-btn i {
  font-size: 1.2rem;
  margin-left: 10px;
  min-width: 20px;
}

.quick-action-btn:hover {
  transform: translateX(-3px);
  text-decoration: none;
}

/* Daily operations */
.daily-entry {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.daily-entry:hover {
  background: linear-gradient(135deg, #c3e6cb 0%, #b1dfbb 100%);
  color: #155724;
  border-color: #28a745;
}

.final-exit {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.final-exit:hover {
  background: linear-gradient(135deg, #f5c6cb 0%, #f1b0b7 100%);
  color: #721c24;
  border-color: #dc3545;
}

/* Hourly operations */
.hourly-exit {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
}

.hourly-exit:hover {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #856404;
  border-color: #ffc107;
}

.return-entry {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
}

.return-entry:hover {
  background: linear-gradient(135deg, #bee5eb 0%, #abdde5 100%);
  color: #0c5460;
  border-color: #17a2b8;
}

/* Vehicle operations */
.vehicle-exit {
  background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
  color: #383d41;
}

.vehicle-exit:hover {
  background: linear-gradient(135deg, #d6d8db 0%, #c6c8ca 100%);
  color: #383d41;
  border-color: #6c757d;
}

.vehicle-entry {
  background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
  color: #004085;
}

.vehicle-entry:hover {
  background: linear-gradient(135deg, #b3d9ff 0%, #99ccff 100%);
  color: #004085;
  border-color: #007bff;
}

/* Reports */
.present-report, .out-report, .vehicle-status, .attendance-report {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #4a148c;
}

.present-report:hover, .out-report:hover, .vehicle-status:hover, .attendance-report:hover {
  background: linear-gradient(135deg, #e1bee7 0%, #ce93d8 100%);
  color: #4a148c;
  border-color: #9c27b0;
}