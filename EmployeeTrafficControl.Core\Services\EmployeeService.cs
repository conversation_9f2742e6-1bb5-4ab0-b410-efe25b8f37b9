﻿using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Services
{
    public class EmployeeService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EmployeeService> _logger;

        public EmployeeService(ApplicationDbContext context, ILogger<EmployeeService> logger)
        {
            _context = context;
            _logger = logger;
        }

        // --- CRUD Operations for Employees ---

        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            return await _context.Employees
                                 .Include(e => e.Building) // Eager load related Building data
                                 .Include(e => e.Job)      // Eager load related Job data
                                 .ToListAsync();
        }

        public async Task<Employee?> GetEmployeeByIdAsync(int employeeId)
        {
            return await _context.Employees
                                 .Include(e => e.Building)
                                 .Include(e => e.Job)
                                 .FirstOrDefaultAsync(e => e.EmployeeId == employeeId);
        }

        public async Task<Employee> AddEmployeeAsync(Employee employee)
        {
            _context.Employees.Add(employee);
            await _context.SaveChangesAsync();
            return employee;
        }

        public async Task<bool> UpdateEmployeeAsync(Employee employee)
        {
            _logger.LogInformation("شروع به‌روزرسانی کارمند با شناسه {EmployeeId}", employee.EmployeeId);

            var existingEmployee = await _context.Employees.FindAsync(employee.EmployeeId);
            if (existingEmployee == null)
            {
                _logger.LogWarning("کارمند با شناسه {EmployeeId} یافت نشد", employee.EmployeeId);
                return false;
            }

            // Update properties
            existingEmployee.FirstName = employee.FirstName;
            existingEmployee.LastName = employee.LastName;
            existingEmployee.NationalCode = employee.NationalCode;
            existingEmployee.PersonnelCode = employee.PersonnelCode;
            existingEmployee.PhoneNumber = employee.PhoneNumber;
            existingEmployee.BuildingId = employee.BuildingId; // Transfer employee
            existingEmployee.JobId = employee.JobId;
            existingEmployee.IsActive = employee.IsActive;
            existingEmployee.HasDrivingLicense = employee.HasDrivingLicense; // اضافه شده: به‌روزرسانی مجوز گواهینامه

            try
            {
                _logger.LogInformation("ذخیره تغییرات کارمند {EmployeeId} - نام: {FirstName} {LastName}, گواهینامه: {HasDrivingLicense}",
                    employee.EmployeeId, employee.FirstName, employee.LastName, employee.HasDrivingLicense);

                await _context.SaveChangesAsync();

                _logger.LogInformation("به‌روزرسانی کارمند {EmployeeId} با موفقیت انجام شد", employee.EmployeeId);
                return true;
            }
            catch (DbUpdateConcurrencyException ex)
            {
                _logger.LogError(ex, "خطای concurrency در به‌روزرسانی کارمند {EmployeeId}", employee.EmployeeId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطای عمومی در به‌روزرسانی کارمند {EmployeeId}", employee.EmployeeId);
                throw;
            }
        }

        public async Task<bool> DeleteEmployeeAsync(int employeeId)
        {
            var employeeToDelete = await _context.Employees.FindAsync(employeeId);
            if (employeeToDelete == null)
            {
                return false;
            }
            // Check for related data (e.g., traffic logs, car passages, user accounts)
            // If there are related records and DeleteBehavior.Restrict is set in DbContext,
            // this delete will fail. You'd need to handle those dependencies first.
            // For simplicity here, we assume if DeleteBehavior.Restrict is on, it will throw error.
            // In a real app, check related entities before deleting.
            var hasTrafficLogs = await _context.TrafficLogs.AnyAsync(tl => tl.EmployeeId == employeeId);
            var hasCarTrafficLogsAsDriver = await _context.CarTrafficLogs.AnyAsync(ctl => ctl.DriverEmployeeId == employeeId);
            var hasCarPassengers = await _context.CarPassengers.AnyAsync(cp => cp.EmployeeId == employeeId);
            var hasUserAccount = await _context.Users.AnyAsync(u => u.EmployeeId == employeeId);

            if (hasTrafficLogs || hasCarTrafficLogsAsDriver || hasCarPassengers || hasUserAccount)
            {
                Console.WriteLine("Cannot delete employee: Related records exist. Please manage them first (e.g., set IsActive to false).");
                return false; // Or throw a specific exception to be caught in UI
            }

            _context.Employees.Remove(employeeToDelete);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Checks if an employee with the given National Code or Personnel Code already exists.
        /// </summary>
        /// <param name="nationalCode">The national code to check.</param>
        /// <param name="personnelCode">The personnel code to check.</param>
        /// <param name="excludeEmployeeId">Optional: An ID to exclude during the check (useful for updates).</param>
        /// <returns>True if an employee with either code exists, otherwise false.</returns>
        public async Task<bool> EmployeeCodeExistsAsync(string personnelCode, int? excludeEmployeeId = null)
        {
            if (excludeEmployeeId.HasValue)
            {
                return await _context.Employees.AnyAsync(e =>
                    ( e.PersonnelCode.ToLower() == personnelCode.ToLower()) &&
                    e.EmployeeId != excludeEmployeeId.Value);
            }
            return await _context.Employees.AnyAsync(e =>
                e.PersonnelCode.ToLower() == personnelCode.ToLower());
        }
    }
}