@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Cars.EditModel
@{
    ViewData["Title"] = "ویرایش خودرو";
}

<div class="page-header">
    <h1>ویرایش خودرو</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <input type="hidden" asp-for="Car.CarId" />
                    
                    <div class="form-section">
                        <div class="mb-3">
                            <label asp-for="Car.PlateNumber" class="form-label">شماره پلاک <span class="text-danger">*</span></label>
                            <input asp-for="Car.PlateNumber" class="form-control" placeholder="مثال: 12ط345-23" />
                            <span asp-validation-for="Car.PlateNumber" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Car.Model" class="form-label">مدل خودرو</label>
                                    <input asp-for="Car.Model" class="form-control" placeholder="مثال: پژو پارس" />
                                    <span asp-validation-for="Car.Model" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Car.Color" class="form-label">رنگ</label>
                                    <input asp-for="Car.Color" class="form-control" placeholder="مثال: سفید" />
                                    <span asp-validation-for="Car.Color" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Car.Type" class="form-label">نوع خودرو</label>
                                    <select asp-for="Car.Type" class="form-select">
                                        <option value="">انتخاب کنید...</option>
                                        <option value="سدان">سدان</option>
                                        <option value="هاچ‌بک">هاچ‌بک</option>
                                        <option value="SUV">SUV</option>
                                        <option value="وانت">وانت</option>
                                        <option value="کوپه">کوپه</option>
                                        <option value="کراس‌اور">کراس‌اور</option>
                                    </select>
                                    <span asp-validation-for="Car.Type" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Car.BuildingId" class="form-label">ساختمان <span class="text-danger">*</span></label>
                                    <select asp-for="Car.BuildingId" asp-items="Model.Buildings" class="form-select">
                                        <option value="0">انتخاب ساختمان...</option>
                                    </select>
                                    <span asp-validation-for="Car.BuildingId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Car.PassengerCapacity" class="form-label">ظرفیت سرنشین</label>
                                    <input asp-for="Car.PassengerCapacity" class="form-control" type="number" min="1" max="50" />
                                    <span asp-validation-for="Car.PassengerCapacity" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input asp-for="Car.IsMoneyTransport" class="form-check-input" type="checkbox" />
                                        <label asp-for="Car.IsMoneyTransport" class="form-check-label">
                                            خودرو پولرسان
                                        </label>
                                        <span asp-validation-for="Car.IsMoneyTransport" class="text-danger"></span>
                                    </div>
                                    <small class="text-muted">خودروهای پولرسان نیاز به ثبت کیلومتر دارند</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره تغییرات
                        </button>
                        <a asp-page="Details" asp-route-id="@Model.Car.CarId" class="btn btn-info">
                            <i class="bi bi-eye"></i> مشاهده جزئیات
                        </a>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">اطلاعات خودرو</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">شناسه:</dt>
                    <dd class="col-sm-7">@Model.Car.CarId</dd>
                    
                    <dt class="col-sm-5">پلاک:</dt>
                    <dd class="col-sm-7">
                        <strong class="text-primary">@Model.Car.PlateNumber</strong>
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        شماره پلاک باید منحصر به فرد باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
