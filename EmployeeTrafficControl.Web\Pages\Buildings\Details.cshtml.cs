using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    [AuthorizePermission("VIEW_BUILDINGS")]
    public class DetailsModel : PageModel
    {
        private readonly BuildingService _buildingService;
        private readonly EmployeeService _employeeService;

        public DetailsModel(BuildingService buildingService, EmployeeService employeeService)
        {
            _buildingService = buildingService;
            _employeeService = employeeService;
        }

        public Building Building { get; set; } = default!;
        public IList<Employee> Employees { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Building = await _buildingService.GetBuildingByIdAsync(id);

            if (Building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Get employees in this building
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            Employees = allEmployees.Where(e => e.BuildingId == id).ToList();

            return Page();
        }


    }
}
