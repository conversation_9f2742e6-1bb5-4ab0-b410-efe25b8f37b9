using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services; // For DailyAttendanceService (if needed, though TrafficLogService should handle it)

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    //[AuthorizePermission("REGISTER_CAR_ENTRY")]
    public class CarEntryModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService; // New service

        public CarEntryModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            TrafficLogService trafficLogService) // Inject new service
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
        }

        public List<EmployeeStatus> EmployeesCanEnter { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();
        public List<SelectListItem> Cars { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? CarId { get; set; }

        public int OutOfOfficeCount { get; set; }
        public int HourlyExitCount { get; set; }
        public int MissionCount { get; set; }

        public List<Employee> Drivers { get; set; } = new();
        public string? CarType { get; set; }
        public string? Status { get; set; }
        public int TotalCarsInside { get; set; }
        public int EmployeeCarsInside { get; set; }
        public int GuestCarsInside { get; set; }
        public int TodayEntries { get; set; }
        public List<CarTrafficLog> CarsInside { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری لیست خودروها
            var cars = await _context.Cars.Where(c => c.IsActive).OrderBy(c => c.PlateNumber).ToListAsync();
            Cars = cars.Select(c => new SelectListItem
            {
                Value = c.CarId.ToString(),
                Text = $"{c.PlateNumber} - {c.Model}",
                Selected = c.CarId == CarId
            }).ToList();

            // دریافت کارمندان خارج از ساختمان
            EmployeesCanEnter = await _context.EmployeeStatuses
                                             .Include(es => es.Employee)
                                             .ThenInclude(e => e.Building)
                                             .Include(es => es.Employee)
                                             .ThenInclude(e => e.Job)
                                             .Where(es => es.Date.Date == DateTime.Today &&
                                                        es.HasInitialEntry &&
                                                        !es.IsPresentInBuilding &&
                                                        !es.FinalExitTime.HasValue &&
                                                        (BuildingId == null || es.Employee.BuildingId == BuildingId.Value))
                                             .OrderBy(es => es.Employee.FirstName)
                                             .ThenBy(es => es.Employee.LastName)
                                             .ToListAsync();

            // فیلتر کارمندانی که گواهینامه دارند
            EmployeesCanEnter = EmployeesCanEnter.Where(es => es.Employee.HasDrivingLicense).ToList();

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                EmployeesCanEnter = EmployeesCanEnter.Where(es =>
                    es.Employee.FirstName.ToLower().Contains(searchLower) ||
                    es.Employee.LastName.ToLower().Contains(searchLower) ||
                    es.Employee.PersonnelCode.ToLower().Contains(searchLower)).ToList();
            }

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);

            // بارگذاری خودروهای موجود در پارکینگ
            var carsInsideQuery = _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                .Include(ctl => ctl.Building)
                .Where(ctl => ctl.IsInParking && ctl.EntryTime.HasValue);

            if (accessibleBuildingIds != null)
            {
                carsInsideQuery = carsInsideQuery.Where(ctl => accessibleBuildingIds.Contains(ctl.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carsInsideQuery = carsInsideQuery.Where(ctl => ctl.BuildingId == BuildingId.Value);
            }

            CarsInside = await carsInsideQuery.OrderByDescending(ctl => ctl.EntryTime).ToListAsync();

            // محاسبه آمار خودروهای داخل پارکینگ
            TotalCarsInside = CarsInside.Count;
            EmployeeCarsInside = CarsInside.Count(c => c.DriverEmployeeId > 0);
            GuestCarsInside = CarsInside.Count(c => c.DriverEmployeeId == 0);
            TodayEntries = await _context.CarTrafficLogs
                .Where(ctl => ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date == DateTime.Today)
                .CountAsync();
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == today && es.Employee.HasDrivingLicense);

            if (accessibleBuildingIds != null)
            {
                query = query.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == BuildingId.Value);
            }

            var statuses = await query.ToListAsync();

            OutOfOfficeCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice);
            HourlyExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.HourlyExit);
            MissionCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OfficialMission);
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") ||
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }

        public async Task<IActionResult> OnPostRegisterCarEntryAsync([FromBody] RegisterCarEntryRequest request)
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return StatusCode(401, new { message = "احراز هویت نشده" });
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return StatusCode(401, new { message = "کاربر یافت نشد" });
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                return Forbid("دسترسی غیرمجاز");
            }

            if (string.IsNullOrWhiteSpace(request.PlateNumber))
            {
                return BadRequest(new { message = "شماره پلاک اجباری است." });
            }

            try
            {
                // Find the car by PlateNumber
                var car = await _context.Cars.FirstOrDefaultAsync(c => c.PlateNumber == request.PlateNumber);

                if (car == null)
                {
                    return BadRequest(new { message = "خودرو با این شماره پلاک یافت نشد." });
                }

                // بررسی وجود خودرو در پارکینگ
                var existingCarLog = await _context.CarTrafficLogs
                    .FirstOrDefaultAsync(ctl => ctl.CarId == car.CarId && ctl.IsInParking);

                if (existingCarLog != null)
                {
                    return BadRequest(new { message = "این خودرو قبلاً در پارکینگ ثبت شده است." });
                }

                var carTrafficLogId = await _trafficLogService.RegisterCarEntryLogAsync(
                    car.CarId,
                    request.DriverId,
                    request.PassengerIds,
                    currentUser.BuildingId ?? 1, // Assuming a default building ID
                    currentUser.Id,
                    DateTime.Now,
                    request.Notes
                );

                if (carTrafficLogId == null)
                {
                    return BadRequest(new { message = "خطا در ثبت ورود خودرو." });
                }

                return new JsonResult(new
                {
                    success = true,
                    message = "ورود خودرو با موفقیت ثبت شد",
                    carTrafficLogId = carTrafficLogId
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnPostRegisterCarEntryAsync: {ex.Message}");
                return BadRequest(new { message = "خطا در ثبت ورود خودرو: " + ex.Message });
            }
        }
    }

    public class RegisterCarEntryRequest
    {
        public string PlateNumber { get; set; } = "";
        public string CarType { get; set; } = "employee"; // employee, guest
        public int? DriverId { get; set; }
        public string? GuestName { get; set; }
        public string? Notes { get; set; }
        public List<int> PassengerIds { get; set; } = new();
    }
}
