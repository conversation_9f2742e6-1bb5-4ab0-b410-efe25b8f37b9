/* Persian DatePicker Styles */
.persian-datepicker-wrapper {
    position: relative;
}

.persian-datepicker-wrapper input[type="text"] {
    direction: rtl;
    text-align: right;
    font-family: 'Vazirmatn', sans-serif;
}

.persian-datepicker-wrapper input[type="text"]:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.persian-datepicker-wrapper input[type="text"].is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.persian-datepicker-wrapper input[type="text"]::placeholder {
    color: #6c757d;
    opacity: 1;
}

/* Calendar Icon */
.persian-datepicker-wrapper::after {
    content: '\F1EC';
    font-family: 'bootstrap-icons';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    z-index: 5;
}

/* RTL Support */
.persian-datepicker-wrapper input[type="text"] {
    padding-left: 35px;
    padding-right: 12px;
}

/* Validation Messages */
.persian-datepicker-wrapper + .text-danger {
    font-size: 0.875em;
    margin-top: 0.25rem;
}

/* Focus State */
.persian-datepicker-wrapper input[type="text"]:focus::placeholder {
    opacity: 0.5;
}

/* Disabled State */
.persian-datepicker-wrapper input[type="text"]:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

/* Animation */
.persian-datepicker-wrapper input[type="text"] {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Error State Animation */
.persian-datepicker-wrapper input[type="text"].is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Responsive */
@media (max-width: 576px) {
    .persian-datepicker-wrapper input[type="text"] {
        font-size: 16px;
    }
}
