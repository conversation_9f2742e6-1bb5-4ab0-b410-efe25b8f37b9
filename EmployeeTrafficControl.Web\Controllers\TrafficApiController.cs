
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace EmployeeTrafficControl.Web.Controllers
{
    [ApiController]
    [Route("api/traffic")]
    public class TrafficApiController : ControllerBase
    {
        private readonly TrafficLogService _trafficLogService;
        private readonly UserManager<ApplicationUser> _userManager;

        public TrafficApiController(TrafficLogService trafficLogService, UserManager<ApplicationUser> userManager)
        {
            _trafficLogService = trafficLogService;
            _userManager = userManager;
        }

        [HttpPost("register-car-entry")]
        public async Task<IActionResult> RegisterCarEntry([FromBody] RegisterCarEntryRequest request)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return Unauthorized();

            var result = await _trafficLogService.RegisterCarEntryAsync(request.CarId, user.Id, new List<int>());
            if (result)
            {
                return Ok(new { success = true, message = "ورود خودرو با موفقیت ثبت شد" });
            }
            return BadRequest(new { success = false, message = "خطا در ثبت ورود خودرو" });
        }

        [HttpPost("register-car-exit")]
        public async Task<IActionResult> RegisterCarExit([FromBody] RegisterCarExitRequest request)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return Unauthorized();

            var buildingId = user.BuildingId ?? 1;
            var result = await _trafficLogService.RegisterCarExitAsync(request.CarId, request.DriverId, request.PassengerIds, buildingId, user.Id, request.Notes);
            if (result)
            {
                return Ok(new { success = true, message = "خروج خودرو با موفقیت ثبت شد" });
            }
            return BadRequest(new { success = false, message = "خطا در ثبت خروج خودرو" });
        }
    }

    public class RegisterCarEntryRequest
    {
        public int CarId { get; set; }
    }

    public class RegisterCarExitRequest
    {
        public int CarId { get; set; }
        public int DriverId { get; set; }
        public List<int> PassengerIds { get; set; } = new List<int>();
        public string? Notes { get; set; }
    }
}
