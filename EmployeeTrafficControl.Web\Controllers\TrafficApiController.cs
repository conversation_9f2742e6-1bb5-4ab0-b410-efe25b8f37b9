
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Web.Controllers
{
    [ApiController]
    [Route("api/traffic")]
    public class TrafficApiController : ControllerBase
    {
        private readonly TrafficLogService _trafficLogService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;
        private readonly EmployeeStatusService _employeeStatusService;

        public TrafficApiController(
            TrafficLogService trafficLogService,
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            EmployeeStatusService employeeStatusService)
        {
            _trafficLogService = trafficLogService;
            _userManager = userManager;
            _context = context;
            _employeeStatusService = employeeStatusService;
        }

        [HttpPost("register-car-entry")]
        public async Task<IActionResult> RegisterCarEntry([FromBody] RegisterCarEntryRequest request)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return Unauthorized();

            var result = await _trafficLogService.RegisterCarEntryAsync(request.CarId, user.Id, new List<int>());
            if (result)
            {
                return Ok(new { success = true, message = "ورود خودرو با موفقیت ثبت شد" });
            }
            return BadRequest(new { success = false, message = "خطا در ثبت ورود خودرو" });
        }

        [HttpPost("register-car-exit")]
        public async Task<IActionResult> RegisterCarExit([FromBody] RegisterCarExitRequest request)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return Unauthorized();

            var buildingId = user.BuildingId ?? 1;
            var result = await _trafficLogService.RegisterCarExitAsync(request.CarId, request.DriverId, request.PassengerIds, buildingId, user.Id, request.Notes);
            if (result)
            {
                return Ok(new { success = true, message = "خروج خودرو با موفقیت ثبت شد" });
            }
            return BadRequest(new { success = false, message = "خطا در ثبت خروج خودرو" });
        }

        [HttpGet("available-drivers")]
        public async Task<IActionResult> GetAvailableDrivers()
        {
            try
            {
                var availableDrivers = await _context.EmployeeStatuses
                    .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                    .Where(es => es.Date.Date == DateTime.Today &&
                               es.HasInitialEntry &&
                               es.IsPresentInBuilding &&
                               !es.FinalExitTime.HasValue &&
                               (es.Employee.HasDrivingLicense ||
                                (es.Employee.Job != null && es.Employee.Job.Title.Contains("راننده"))))
                    .Select(es => new
                    {
                        employeeId = es.EmployeeId,
                        firstName = es.Employee.FirstName,
                        lastName = es.Employee.LastName,
                        personnelCode = es.Employee.PersonnelCode
                    })
                    .ToListAsync();

                return Ok(availableDrivers);
            }
            catch (Exception)
            {
                return BadRequest(new { success = false, message = "خطا در بارگذاری لیست رانندگان" });
            }
        }

        [HttpGet("available-passengers")]
        public async Task<IActionResult> GetAvailablePassengers(string? exitType = null)
        {
            try
            {
                var query = _context.EmployeeStatuses
                    .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                    .Where(es => es.Date.Date == DateTime.Today &&
                               es.HasInitialEntry &&
                               es.IsPresentInBuilding &&
                               !es.FinalExitTime.HasValue);

                // فیلتر بر اساس نوع خروج برای خودروهای پولرسان
                if (!string.IsNullOrEmpty(exitType))
                {
                    if (exitType == "treasury")
                    {
                        // فقط کارمندان خزانه‌داری و نگهبانی
                        query = query.Where(es => es.Employee.Job != null &&
                                                (es.Employee.Job.Title.Contains("خزانه") ||
                                                 es.Employee.Job.Title.Contains("نگهبان")));
                    }
                    // برای نوع عمومی، همه کارمندان موجود نمایش داده می‌شوند
                }

                var availablePassengers = await query
                    .Select(es => new
                    {
                        employeeId = es.EmployeeId,
                        firstName = es.Employee.FirstName,
                        lastName = es.Employee.LastName,
                        personnelCode = es.Employee.PersonnelCode
                    })
                    .ToListAsync();

                return Ok(availablePassengers);
            }
            catch (Exception)
            {
                return BadRequest(new { success = false, message = "خطا در بارگذاری لیست سرنشینان" });
            }
        }
    }

    public class RegisterCarEntryRequest
    {
        public int CarId { get; set; }
    }

    public class RegisterCarExitRequest
    {
        public int CarId { get; set; }
        public int DriverId { get; set; }
        public List<int> PassengerIds { get; set; } = [];
        public string? Notes { get; set; }
    }
}
