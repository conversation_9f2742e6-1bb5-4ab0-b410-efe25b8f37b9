using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Linq;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    [AuthorizePermission("DELETE_BUILDING")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class DeleteModel : PageModel
    {
        private readonly BuildingService _buildingService;
        private readonly EmployeeService _employeeService;

        public DeleteModel(BuildingService buildingService, EmployeeService employeeService)
        {
            _buildingService = buildingService;
            _employeeService = employeeService;
        }

        [BindProperty]
        public int BuildingId { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            BuildingId = id;

            var building = await _buildingService.GetBuildingByIdAsync(id);
            if (building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var building = await _buildingService.GetBuildingByIdAsync(BuildingId);
            if (building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Check if there are employees in this building
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            var employeesInBuilding = allEmployees.Where(e => e.BuildingId == BuildingId).ToList();

            if (employeesInBuilding.Any())
            {
                TempData["ErrorMessage"] = $"امکان حذف این ساختمان وجود ندارد زیرا {employeesInBuilding.Count} کارمند در این ساختمان کار می‌کنند.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _buildingService.DeleteBuildingAsync(BuildingId);
                TempData["SuccessMessage"] = "ساختمان با موفقیت حذف شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف ساختمان: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
