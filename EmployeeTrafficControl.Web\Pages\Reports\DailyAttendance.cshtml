@page 
@model EmployeeTrafficControl.Web.Pages.Reports.DailyAttendanceModel
@{
    ViewData["Title"] = "گزارش حضور و غیاب روزانه";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="bi bi-calendar-check"></i> گزارش حضور و غیاب روزانه
            </h2>
            <p class="text-muted mb-0">گزارش حضور و غیاب کارمندان در تاریخ @Model.ReportDate?.ToString("yyyy/MM/dd")</p>
        </div>
        <div>
            <a href="/dashboard" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>

    <!-- فرم فیلتر -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel"></i> فیلتر گزارش
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">تاریخ</label>
                                <input type="date" name="ReportDate" value="@(Model.ReportDate?.ToString("yyyy-MM-dd"))" class="form-control" />
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">ساختمان</label>
                                <select name="BuildingId" asp-items="Model.Buildings" class="form-select">
                                    <option value="">همه ساختمان‌ها</option>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> جستجو
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- آمار کلی -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.TotalEmployees</h3>
                    <p class="mb-0">کل کارمندان</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.PresentCount</h3>
                    <p class="mb-0">حاضر</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.AbsentCount</h3>
                    <p class="mb-0">غایب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.LateCount</h3>
                    <p class="mb-0">تأخیر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول حضور و غیاب -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table"></i> جزئیات حضور و غیاب
                    </h5>
                    <span class="badge bg-info">@Model.Attendances.Count رکورد</span>
                </div>
                <div class="card-body">
                    @if (Model.Attendances.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>کارمند</th>
                                        <th>ساختمان</th>
                                        <th>شغل</th>
                                        <th>ورود</th>
                                        <th>خروج</th>
                                        <th>مدت کار</th>
                                        <th>تأخیر</th>
                                        <th>وضعیت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var record in Model.Attendances)
                                    {
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>@record.Employee.FirstName @record.Employee.LastName</strong>
                                                    <br>
                                                    <small class="text-muted">@record.Employee.PersonnelCode</small>
                                                </div>
                                            </td>
                                            <td>@record.Employee.Building?.Name</td>
                                            <td>@record.Employee.Job?.Title</td>
                                            <td>
                                                @if (record.CheckInTime.HasValue)
                                                {
                                                    <span class="text-success">@record.CheckInTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (record.CheckOutTime.HasValue)
                                                {
                                                    <span class="text-info">@record.CheckOutTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (record.TotalWorkHours != null)
                                                {
                                                    <span>@record.TotalWorkHours.Value.ToString(@"hh\:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (record.LateMinutes > 0)
                                                {
                                                    <span class="badge bg-warning">@record.LateMinutes دقیقه</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (record.CheckInTime.HasValue)
                                                {
                                                    <span class="badge bg-success">حاضر</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غایب</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">هیچ رکورد حضور و غیابی یافت نشد</h4>
                            <p class="text-muted">برای تاریخ و ساختمان انتخابی، هیچ اطلاعات حضور و غیابی ثبت نشده است.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        background-color: #f8f9fa;
    }

    .badge {
        font-size: 0.75em;
    }

    .btn {
        border-radius: 0.375rem;
    }
</style> 