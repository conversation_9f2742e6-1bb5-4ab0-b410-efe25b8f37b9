using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services; // For DailyAttendanceService (if needed, though TrafficLogService should handle it)

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    [AuthorizePermission("REGISTER_HOURLY_EXIT")]
    public class IndividualExitModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService; // New service

        public IndividualExitModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            TrafficLogService trafficLogService) // Inject new service
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
        }

        public List<EmployeeStatus> PresentEmployees { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? ExitType { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? ExitPermitNumber { get; set; }

        public int HourlyExitCount { get; set; }
        public int MissionCount { get; set; }
        public int FinalExitCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // دریافت کارمندان حاضر در ساختمان
            // Note: This still uses EmployeeStatusService directly for fetching, which is fine for display purposes.
            PresentEmployees = await _context.EmployeeStatuses
                                             .Include(es => es.Employee)
                                             .ThenInclude(e => e.Building)
                                             .Include(es => es.Employee)
                                             .ThenInclude(e => e.Job)
                                             .Where(es => es.Date.Date == DateTime.Today &&
                                                        es.HasInitialEntry &&
                                                        es.IsPresentInBuilding &&
                                                        !es.FinalExitTime.HasValue &&
                                                        (BuildingId == null || es.Employee.BuildingId == BuildingId.Value))
                                             .OrderBy(es => es.Employee.FirstName)
                                             .ThenBy(es => es.Employee.LastName)
                                             .ToListAsync();

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                PresentEmployees = PresentEmployees.Where(es =>
                    es.Employee.FirstName.ToLower().Contains(searchLower) ||
                    es.Employee.LastName.ToLower().Contains(searchLower) ||
                    es.Employee.PersonnelCode.ToLower().Contains(searchLower)).ToList();
            }

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == today);

            if (accessibleBuildingIds != null)
            {
                query = query.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == BuildingId.Value);
            }

            var statuses = await query.ToListAsync();

            HourlyExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.HourlyExit);
            MissionCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OfficialMission);
            FinalExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice);
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") || 
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }

        public async Task<IActionResult> OnPostRegisterExitAsync(int employeeId)
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                string trafficType = "خروج نهایی"; // Default
                if (ExitType?.ToLower() == "hourly")
                {
                    trafficType = "خروج ساعتی";
                }
                else if (ExitType?.ToLower() == "mission")
                {
                    trafficType = "ماموریت";
                }

                var success = await _trafficLogService.RegisterExitAsync(
                    employeeId,
                    currentUser.BuildingId ?? 1, // Assuming a default building ID
                    currentUser.Id,
                    DateTime.Now,
                    trafficType,
                    notes: $"خروج از طریق فرم IndividualExit - نوع: {trafficType}",
                    exitPermitNumber: ExitPermitNumber
                );

                if (success)
                {
                    TempData["SuccessMessage"] = "خروج کارمند با موفقیت ثبت شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "خطا در ثبت خروج کارمند.";
                }
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت خروج: " + ex.Message;
                return RedirectToPage();
            }
        }
    }
}
