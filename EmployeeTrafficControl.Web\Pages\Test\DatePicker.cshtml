@page "/test/datepicker"
@model EmployeeTrafficControl.Web.Pages.Test.DatePickerModel
@{
    ViewData["Title"] = "تست تقویم شمسی";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-calendar-date"></i> تست تقویم شمسی
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">تاریخ شروع</label>
                                <input type="date" name="StartDate" value="@Model.StartDate?.ToString("yyyy-MM-dd")" class="form-control" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاریخ پایان</label>
                                <input type="date" name="EndDate" value="@Model.EndDate?.ToString("yyyy-MM-dd")" class="form-control" />
                            </div>
                            <div class="col-12">
                                <label class="form-label">تاریخ تولد</label>
                                <input type="date" name="BirthDate" value="@Model.BirthDate?.ToString("yyyy-MM-dd")" class="form-control" required />
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check"></i> ثبت
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="window.location.href='/Dashboard'">
                                    <i class="bi bi-arrow-right"></i> بازگشت
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    @if (Model.StartDate.HasValue || Model.EndDate.HasValue || Model.BirthDate.HasValue)
                    {
                        <hr />
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> نتایج:</h6>
                            @if (Model.StartDate.HasValue)
                            {
                                <p><strong>تاریخ شروع:</strong> @Model.StartDate.Value.ToString("yyyy/MM/dd")</p>
                            }
                            @if (Model.EndDate.HasValue)
                            {
                                <p><strong>تاریخ پایان:</strong> @Model.EndDate.Value.ToString("yyyy/MM/dd")</p>
                            }
                            @if (Model.BirthDate.HasValue)
                            {
                                <p><strong>تاریخ تولد:</strong> @Model.BirthDate.Value.ToString("yyyy/MM/dd")</p>
                            }
                        </div>
                    }
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-question-circle"></i> راهنمای استفاده
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success"></i> تاریخ‌ها به صورت خودکار به تقویم شمسی تبدیل می‌شوند</li>
                        <li><i class="bi bi-check-circle text-success"></i> فرمت ورودی: ۱۴۰۳/۰۱/۰۱</li>
                        <li><i class="bi bi-check-circle text-success"></i> می‌توانید اعداد فارسی یا انگلیسی تایپ کنید</li>
                        <li><i class="bi bi-check-circle text-success"></i> فرمت خودکار اعمال می‌شود</li>
                        <li><i class="bi bi-check-circle text-success"></i> اعتبارسنجی تاریخ انجام می‌شود</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
