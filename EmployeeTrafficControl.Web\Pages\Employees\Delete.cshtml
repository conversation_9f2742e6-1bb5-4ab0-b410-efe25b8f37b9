@page
@model EmployeeTrafficControl.Web.Pages.Employees.DeleteModel
@{
    ViewData["Title"] = "حذف کارمند";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-trash text-danger"></i>
                        حذف کارمند
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>هشدار:</strong> این عملیات قابل بازگشت نیست!
                    </div>
                    
                    <p>آیا مطمئن هستید که می‌خواهید این کارمند را حذف کنید؟</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <form method="post">
                                <input type="hidden" asp-for="@Model.EmployeeId" />
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="bi bi-trash"></i> بله، حذف کن
                                    </button>
                                    <a asp-page="./Index" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left"></i> انصراف
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
