@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Jobs.DetailsModel
@{
    ViewData["Title"] = "جزئیات شغل";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>جزئیات شغل</h1>
        <div>
            <a asp-page="Edit" asp-route-id="@Model.Job.JobId" class="btn btn-primary">
                <i class="bi bi-pencil"></i> ویرایش
            </a>
            <a asp-page="Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به لیست
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">@Model.Job.Title</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">شناسه:</dt>
                    <dd class="col-sm-9">@Model.Job.JobId</dd>

                    <dt class="col-sm-3">ساختمان:</dt>
                    <dd class="col-sm-9">
                        <span class="badge bg-info">@Model.Job.Building?.Name</span>
                    </dd>

                    <dt class="col-sm-3">عنوان شغل:</dt>
                    <dd class="col-sm-9">
                        <strong>@Model.Job.Title</strong>
                    </dd>
                </dl>
            </div>
        </div>

        @if (Model.Employees.Any())
        {
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">کارمندان این شغل (@Model.Employees.Count نفر)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>نام و نام خانوادگی</th>
                                    <th>کد ملی</th>
                                    <th>ساختمان</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var employee in Model.Employees)
                                {
                                    <tr>
                                        <td>@employee.FirstName @employee.LastName</td>
                                        <td>@employee.NationalCode</td>
                                        <td>@employee.Building?.Name</td>
                                        <td>
                                            <a asp-page="/Employees/Details" asp-route-id="@employee.EmployeeId" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i> مشاهده
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">عملیات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-page="Edit" asp-route-id="@Model.Job.JobId" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> ویرایش شغل
                    </a>
                    <a asp-page="/Employees/Create" class="btn btn-success">
                        <i class="bi bi-person-plus"></i> افزودن کارمند با این شغل
                    </a>
                    <hr>
                    <a asp-page="Delete" asp-route-id="@Model.Job.JobId" class="btn btn-danger w-100">
                        <i class="bi bi-trash"></i> حذف شغل
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">آمار</h5>
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-12">
                        <h3 class="text-primary">@Model.Employees.Count</h3>
                        <p class="mb-0">کارمند با این شغل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
