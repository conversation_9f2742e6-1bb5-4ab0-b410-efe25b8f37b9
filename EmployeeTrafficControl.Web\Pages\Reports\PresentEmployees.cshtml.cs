﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Web.Attributes;
using System.Linq;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    [AuthorizePermission("VIEW_REPORTS")]
    public class PresentEmployeesModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public PresentEmployeesModel(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public List<EmployeeStatus> EmployeesPresent { get; set; } = new();
        public List<DailyAttendance> TodayAttendances { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();
        public List<SelectListItem> Jobs { get; set; } = new();

        public int OnTimeCount { get; set; }
        public int LateCount { get; set; }
        public int OvertimeCount { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }
        [BindProperty(SupportsGet = true)]
        public int? JobId { get; set; }
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        public int TotalPresent { get; set; }
        public int TotalEmployees { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!User.Identity?.IsAuthenticated == true)
                return RedirectToPage("/Account/Login");

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return RedirectToPage("/Account/Login");

            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanViewReports(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            await LoadDataAsync(currentUser);
            return Page();
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            var today = DateTime.Today;
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            Buildings = await buildingsQuery
                .OrderBy(b => b.Name)
                .Select(b => new SelectListItem { Value = b.BuildingId.ToString(), Text = b.Name, Selected = b.BuildingId == BuildingId })
                .ToListAsync();

            // بارگذاری مشاغل
            var jobsQuery = _context.Jobs.AsQueryable();
            if (BuildingId.HasValue)
                jobsQuery = jobsQuery.Where(j => j.BuildingId == BuildingId.Value);
            Jobs = await jobsQuery
                .OrderBy(j => j.Title)
                .Select(j => new SelectListItem { Value = j.JobId.ToString(), Text = j.Title, Selected = j.JobId == JobId })
                .ToListAsync();

            // وضعیت‌های امروز (حاضر)
            var statusQuery = _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Where(es => es.Date.Date == today &&
                             (es.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding ||
                              es.CurrentStatus == EmployeeCurrentStatus.HourlyExit ||
                              es.CurrentStatus == EmployeeCurrentStatus.OfficialMission));

            if (accessibleBuildingIds != null)
                statusQuery = statusQuery.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            if (BuildingId.HasValue)
                statusQuery = statusQuery.Where(es => es.Employee.BuildingId == BuildingId.Value);
            if (JobId.HasValue)
                statusQuery = statusQuery.Where(es => es.Employee.JobId == JobId.Value);
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                statusQuery = statusQuery.Where(es =>
                    es.Employee.FirstName.ToLower().Contains(searchLower) ||
                    es.Employee.LastName.ToLower().Contains(searchLower) ||
                    es.Employee.PersonnelCode.ToLower().Contains(searchLower));
            }

            EmployeesPresent = await statusQuery.ToListAsync();
            
            // بارگذاری حضور و غیاب امروز
            var employeeIds = EmployeesPresent.Select(es => es.EmployeeId).ToList();
            TodayAttendances = await _context.DailyAttendances
                .Where(da => da.Date.Date == today && employeeIds.Contains(da.EmployeeId))
                .ToListAsync();
            
            OnTimeCount = EmployeesPresent.Count(es => es.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding);
            LateCount = EmployeesPresent.Count(es => es.CurrentStatus == EmployeeCurrentStatus.HourlyExit);
            OvertimeCount = EmployeesPresent.Count(es => es.CurrentStatus == EmployeeCurrentStatus.OfficialMission);

            TotalPresent = EmployeesPresent.Count;
            TotalEmployees = await _context.Employees.CountAsync(e => e.IsActive &&
                (!BuildingId.HasValue || e.BuildingId == BuildingId) &&
                (!JobId.HasValue || e.JobId == JobId));
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            if (user.Role?.Code == "ADMIN")
                return null;
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };
            return null;
        }

        private bool CanViewReports(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") ||
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }
    }
}