using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Core.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    [AuthorizePermission("VIEW_CARS")]
    public class IndexModel : PageModel
    {
        private readonly CarService _carService;

        public IndexModel(CarService carService)
        {
            _carService = carService;
        }

        public IList<Car> Cars { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Cars = await _carService.GetAllCarsAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var car = await _carService.GetCarByIdAsync(id);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage();
            }

            try
            {
                bool deleteResult = await _carService.DeleteCarAsync(id);
                if (deleteResult)
                {
                    TempData["SuccessMessage"] = "خودرو با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این خودرو وجود ندارد زیرا دارای سوابق تردد است.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف خودرو: " + ex.Message;
            }

            return RedirectToPage();
        }
    }
}
