using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Models;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    [AuthorizePermission("VIEW_REPORTS")]
    public class EmployeeDetailedTrafficModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly ReportService _reportService;
        private readonly UserManager<ApplicationUser> _userManager;

        public EmployeeDetailedTrafficModel(
            ApplicationDbContext context,
            ReportService reportService,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _reportService = reportService;
            _userManager = userManager;
        }

        [BindProperty(SupportsGet = true)]
        public int? EmployeeId { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? FromDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? ToDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        public EmployeeDetailedTrafficReportData? ReportData { get; set; }
        public List<Employee> AvailableEmployees { get; set; } = [];
        public List<Building> AvailableBuildings { get; set; } = [];

        public async Task OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return;

            // تنظیم مقادیر پیش‌فرض
            FromDate ??= DateTime.Today.AddDays(-30);
            ToDate ??= DateTime.Today;

            // بارگذاری لیست کارمندان و ساختمان‌ها
            await LoadAvailableData(user);

            // تولید گزارش در صورت انتخاب کارمند
            if (EmployeeId.HasValue)
            {
                ReportData = await _reportService.GetEmployeeDetailedTrafficReportAsync(
                    EmployeeId.Value, FromDate.Value, ToDate.Value);
            }
        }

        private async Task LoadAvailableData(ApplicationUser user)
        {
            // دسترسی به ساختمان‌ها
            var accessibleBuildingIds = user.BuildingId.HasValue 
                ? new[] { user.BuildingId.Value } 
                : null;

            // بارگذاری ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }
            AvailableBuildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();

            // بارگذاری کارمندان
            var employeesQuery = _context.Employees
                .Include(e => e.Job)
                .Include(e => e.Building)
                .AsQueryable();

            if (accessibleBuildingIds != null)
            {
                employeesQuery = employeesQuery.Where(e => accessibleBuildingIds.Contains(e.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                employeesQuery = employeesQuery.Where(e => e.BuildingId == BuildingId.Value);
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                employeesQuery = employeesQuery.Where(e =>
                    e.FirstName.ToLower().Contains(searchLower) ||
                    e.LastName.ToLower().Contains(searchLower) ||
                    e.PersonnelCode.ToLower().Contains(searchLower));
            }

            AvailableEmployees = await employeesQuery
                .OrderBy(e => e.FirstName)
                .ThenBy(e => e.LastName)
                .ToListAsync();
        }

        public async Task<IActionResult> OnPostExportAsync()
        {
            if (!EmployeeId.HasValue || !FromDate.HasValue || !ToDate.HasValue)
            {
                TempData["ErrorMessage"] = "لطفاً کارمند و بازه زمانی را انتخاب کنید.";
                return RedirectToPage();
            }

            var reportData = await _reportService.GetEmployeeDetailedTrafficReportAsync(
                EmployeeId.Value, FromDate.Value, ToDate.Value);

            // اینجا می‌توانید export به Excel یا PDF اضافه کنید
            TempData["SuccessMessage"] = "گزارش آماده شد.";
            return RedirectToPage();
        }
    }
}
