@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Employees.DetailsModel
@{
    ViewData["Title"] = "جزئیات کارمند";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>جزئیات کارمند</h1>
        <div>
            <a asp-page="Edit" asp-route-id="@Model.Employee.EmployeeId" class="btn btn-primary">
                <i class="bi bi-pencil"></i> ویرایش
            </a>
            <a asp-page="Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به لیست
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">@Model.Employee.FirstName @Model.Employee.LastName</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">شناسه:</dt>
                    <dd class="col-sm-9">@Model.Employee.EmployeeId</dd>
                    
                    <dt class="col-sm-3">نام و نام خانوادگی:</dt>
                    <dd class="col-sm-9">
                        <strong>@Model.Employee.FirstName @Model.Employee.LastName</strong>
                    </dd>
                    
                    <dt class="col-sm-3">کد ملی:</dt>
                    <dd class="col-sm-9">
                        @if (!string.IsNullOrEmpty(Model.Employee.NationalCode))
                        {
                            <span>@Model.Employee.NationalCode</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">کد پرسنلی:</dt>
                    <dd class="col-sm-9">
                        <code>@Model.Employee.PersonnelCode</code>
                    </dd>
                    
                    <dt class="col-sm-3">شماره تماس:</dt>
                    <dd class="col-sm-9">
                        @if (!string.IsNullOrEmpty(Model.Employee.PhoneNumber))
                        {
                            <span>@Model.Employee.PhoneNumber</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">شغل:</dt>
                    <dd class="col-sm-9">
                        @if (Model.Employee.Job != null)
                        {
                            <span class="badge bg-secondary">@Model.Employee.Job.Title</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">ساختمان:</dt>
                    <dd class="col-sm-9">
                        @if (Model.Employee.Building != null)
                        {
                            <span class="badge bg-info">@Model.Employee.Building.Name</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">وضعیت:</dt>
                    <dd class="col-sm-9">
                        @if (Model.Employee.IsActive)
                        {
                            <span class="badge bg-success">فعال</span>
                        }
                        else
                        {
                            <span class="badge bg-danger">غیرفعال</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">عملیات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-page="Edit" asp-route-id="@Model.Employee.EmployeeId" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> ویرایش کارمند
                    </a>
                    
                    @if (Model.Employee.HasDrivingLicense)
                    {
                        <a asp-page="/Cars/Create" class="btn btn-success">
                            <i class="bi bi-car-front"></i> ثبت خودرو برای این راننده
                        </a>
                    }
                    
                    <a asp-page="/Users/<USER>" asp-route-employeeId="@Model.Employee.EmployeeId" class="btn btn-info">
                        <i class="bi bi-person-plus"></i> ایجاد حساب کاربری
                    </a>
                    
                    <hr>
                    <a asp-page="Delete" asp-route-id="@Model.Employee.EmployeeId" class="btn btn-danger w-100">
                        <i class="bi bi-trash"></i> حذف کارمند
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">خلاصه اطلاعات</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">@Model.Employee.EmployeeId</h4>
                        <p class="mb-0">شناسه</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">@Model.Employee.PersonnelCode</h4>
                        <p class="mb-0">کد پرسنلی</p>
                    </div>
                </div>
                
                @if (Model.Employee.Building != null || Model.Employee.Job != null)
                {
                    <hr>
                    <ul class="list-unstyled">
                        @if (Model.Employee.Building != null)
                        {
                            <li><i class="bi bi-building text-info"></i> @Model.Employee.Building.Name</li>
                        }
                        @if (Model.Employee.Job != null)
                        {
                            <li><i class="bi bi-person-badge text-secondary"></i> @Model.Employee.Job.Title</li>
                        }
                    </ul>
                }
            </div>
        </div>
    </div>
</div>
