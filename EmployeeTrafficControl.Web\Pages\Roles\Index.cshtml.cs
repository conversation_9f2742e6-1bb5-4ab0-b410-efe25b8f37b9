using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Roles
{
    public class IndexModel : PageModel
    {
        private readonly RoleService _roleService;
        private readonly UserManager<ApplicationUser> _userManager;

       private readonly ApplicationDbContext _context;
public IndexModel(RoleService roleService, UserManager<ApplicationUser> userManager, ApplicationDbContext context)
{
    _roleService = roleService;
    _userManager = userManager;
    _context = context;
}

        public List<Role> Roles { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanManageRoles(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync();
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync()
        {
            Roles = await _roleService.GetAllRolesAsync();
        }

        private bool CanManageRoles(IList<string> userRoles)
        {
            return userRoles.Contains("Admin");
        }

        public int GetUserCountForRole(int roleId)
{
    return _context.Users.Count(u => u.RoleId == roleId);
}
    }
} 