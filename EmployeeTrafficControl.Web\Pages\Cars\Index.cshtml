@page
@using Microsoft.AspNetCore.Identity
@using EmployeeTrafficControl.Data.Models
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@model EmployeeTrafficControl.Web.Pages.Cars.IndexModel
@{
    ViewData["Title"] = "لیست خودروها";
    var currentUser = await UserManager.GetUserAsync(User);
    var userRoles = currentUser != null ? await UserManager.GetRolesAsync(currentUser) : new List<string>();
    bool canCreate = userRoles.Contains("Admin") || userRoles.Contains("Manager");
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>لیست خودروها</h1>
        @if (canCreate)
        {
            <a asp-page="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> افزودن خودرو جدید
            </a>
        }
    </div>
</div>

@if (Model.Cars == null || !Model.Cars.Any())
{
    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle"></i>
        <p class="mb-0">هیچ خودرویی یافت نشد.</p>
        @if (canCreate)
        {
            <a asp-page="Create" class="btn btn-primary mt-2">افزودن اولین خودرو</a>
        }
    </div>
}
else
{
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>شماره پلاک</th>
                            <th>مدل</th>
                            <th>رنگ</th>
                            <th>نوع</th>
                            <th>ساختمان</th>
                            <th>ظرفیت</th>
                            <th>پولرسان</th>
                            <th class="text-center">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var car in Model.Cars)
                        {
                            <tr>
                                <td>
                                    <strong class="text-primary">@car.PlateNumber</strong>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(car.Model))
                                    {
                                        <span>@car.Model</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">نامشخص</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(car.Color))
                                    {
                                        <span>@car.Color</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">نامشخص</span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(car.Type))
                                    {
                                        <span>@car.Type</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">نامشخص</span>
                                    }
                                </td>
                                <td>
                                    @if (car.Building != null)
                                    {
                                        <span class="badge bg-primary">@car.Building.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">نامشخص</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@car.PassengerCapacity نفر</span>
                                </td>
                                <td>
                                    @if (car.IsMoneyTransport)
                                    {
                                        <span class="badge bg-warning text-dark">
                                            <i class="bi bi-shield-check"></i> بله
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">خیر</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a asp-page="Edit" asp-route-id="@car.CarId" class="btn btn-sm btn-outline-primary" title="ویرایش">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-page="Details" asp-route-id="@car.CarId" class="btn btn-sm btn-outline-info" title="جزئیات">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-page="Delete" asp-route-id="@car.CarId" class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <p class="text-muted">
                    نمایش @Model.Cars.Count مورد
                </p>
            </div>
            <div class="col-md-6 text-end">
                @if (canCreate)
                {
                    <a asp-page="Create" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> افزودن خودرو جدید
                    </a>
                }
            </div>
        </div>
    </div>
}
