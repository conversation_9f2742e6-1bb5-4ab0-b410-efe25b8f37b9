using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Test
{
    public class DatePickerModel : PageModel
    {
        [BindProperty]
        public DateTime? StartDate { get; set; }
        
        [BindProperty]
        public DateTime? EndDate { get; set; }
        
        [BindProperty]
        public DateTime? BirthDate { get; set; }

        public void OnGet()
        {
            // مقادیر پیش‌فرض برای تست
            StartDate = DateTime.Today;
            EndDate = DateTime.Today.AddDays(30);
        }

        public IActionResult OnPost()
        {
            if (ModelState.IsValid)
            {
                TempData["SuccessMessage"] = "تاریخ‌ها با موفقیت ثبت شدند.";
            }
            
            return Page();
        }
    }
}
