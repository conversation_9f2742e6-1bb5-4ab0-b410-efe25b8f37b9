using EmployeeTrafficControl.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace EmployeeTrafficControl.Web.Attributes
{
    /// <summary>
    /// Attribute برای بررسی مجوز دسترسی به صفحات
    /// </summary>
    public class AuthorizePermissionAttribute : Attribute, IAsyncActionFilter
    {
        private readonly string _permissionCode;

        public AuthorizePermissionAttribute(string permissionCode)
        {
            _permissionCode = permissionCode;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var authService = context.HttpContext.RequestServices.GetRequiredService<AuthenticationService>();
            var authorizationService = context.HttpContext.RequestServices.GetRequiredService<AuthorizationService>();

            // بررسی احراز هویت
            var sessionToken = context.HttpContext.Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                context.Result = new RedirectToPageResult("/Account/Login");
                return;
            }

            var session = await authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                context.HttpContext.Response.Cookies.Delete("SessionToken");
                context.Result = new RedirectToPageResult("/Account/Login");
                return;
            }

            // بررسی مجوز
            var hasPermission = await authorizationService.HasPermissionAsync(session.UserId, _permissionCode);
            if (!hasPermission)
            {
                // اگر کاربر مجوز ندارد، به صفحه خطای دسترسی هدایت شود
                context.HttpContext.Items["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                context.Result = new RedirectToPageResult("/Dashboard/Index");
                return;
            }

            // اگر همه چیز درست بود، ادامه دهید
            await next();
        }
    }
}
