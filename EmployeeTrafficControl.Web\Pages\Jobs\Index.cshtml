@page
@using Microsoft.AspNetCore.Identity
@using EmployeeTrafficControl.Data.Models
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@model EmployeeTrafficControl.Web.Pages.Jobs.IndexModel
@{
    ViewData["Title"] = "لیست مشاغل";
    var currentUser = await UserManager.GetUserAsync(User);
    var userRoles = currentUser != null ? await UserManager.GetRolesAsync(currentUser) : new List<string>();
    bool canCreate = userRoles.Contains("Admin") || userRoles.Contains("Manager");
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>لیست مشاغل</h1>
        @if (canCreate)
        {
            <a asp-page="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> افزودن شغل جدید
            </a>
        }
    </div>
</div>

@if (Model.Jobs == null || !Model.Jobs.Any())
{
    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle"></i>
        <p class="mb-0">هیچ شغلی یافت نشد.</p>
        @if (canCreate)
        {
            <a asp-page="Create" class="btn btn-primary mt-2">افزودن اولین شغل</a>
        }
    </div>
}
else
{
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ساختمان</th>
                            <th>عنوان شغل</th>
                            <th class="text-center">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var job in Model.Jobs)
                        {
                            <tr>
                                <td>
                                    <span class="badge bg-info">@job.Building?.Name</span>
                                </td>
                                <td>
                                    <strong>@job.Title</strong>
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a asp-page="Edit" asp-route-id="@job.JobId" class="btn btn-sm btn-outline-primary" title="ویرایش">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-page="Details" asp-route-id="@job.JobId" class="btn btn-sm btn-outline-info" title="جزئیات">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-page="Delete" asp-route-id="@job.JobId" class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <p class="text-muted">
                    نمایش @Model.Jobs.Count مورد
                </p>
            </div>
            <div class="col-md-6 text-end">
                @if (canCreate)
                {
                    <a asp-page="Create" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> افزودن شغل جدید
                    </a>
                }
            </div>
        </div>
    </div>
}
