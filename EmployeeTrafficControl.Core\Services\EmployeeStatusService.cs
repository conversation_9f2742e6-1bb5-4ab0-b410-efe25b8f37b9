using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Core.Services
{
    public class EmployeeStatusService
    {
        private readonly ApplicationDbContext _context;

        public EmployeeStatusService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// دریافت وضعیت فعلی کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>وضعیت کارمند</returns>
        public async Task<EmployeeStatus?> GetEmployeeStatusAsync(int employeeId, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            return await _context.EmployeeStatuses
                                 .Include(es => es.Employee)
                                 .Include(es => es.CurrentVehicle)
                                 .Include(es => es.UpdatedByUser)
                                 .FirstOrDefaultAsync(es => es.EmployeeId == employeeId && es.Date.Date == targetDate.Date);
        }

        /// <summary>
        /// دریافت لیست کارمندان حاضر در ساختمان
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست کارمندان حاضر</returns>
        public async Task<List<EmployeeStatus>> GetPresentEmployeesAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(es => es.CurrentVehicle)
                               .Where(es => es.Date.Date == targetDate.Date && 
                                          es.HasInitialEntry &&
                                          es.IsPresentInBuilding &&
                                          !es.FinalExitTime.HasValue);

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست کارمندان خارج از ساختمان (خروج ساعتی یا ماموریت)
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست کارمندان خارج از ساختمان</returns>
        public async Task<List<EmployeeStatus>> GetEmployeesOutOfBuildingAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;

            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(es => es.CurrentVehicle)
                               .Where(es => es.Date.Date == targetDate.Date &&
                                          es.HasInitialEntry && // ورود اولیه انجام شده
                                          !es.IsPresentInBuilding && // خارج از ساختمان
                                          !es.FinalExitTime.HasValue); // خروج نهایی انجام نشده

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست کارمندان خارج از ساختمان (فقط خروج ساعتی و ماموریت - بدون خروج نهایی)
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست کارمندان خارج از ساختمان بدون خروج نهایی</returns>
        public async Task<List<EmployeeStatus>> GetEmployeesOutOfBuildingExcludingFinalExitAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;

            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(es => es.CurrentVehicle)
                               .Where(es => es.Date.Date == targetDate.Date &&
                                          es.HasInitialEntry && // ورود اولیه انجام شده
                                          !es.IsPresentInBuilding && // خارج از ساختمان
                                          !es.FinalExitTime.HasValue);

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            // فقط کارمندانی که یا خروج فردی خورده‌اند یا با خودرو خارج شده‌اند
            query = query.Where(es => es.CurrentStatus == EmployeeCurrentStatus.HourlyExit ||
                                     es.CurrentStatus == EmployeeCurrentStatus.OfficialMission ||
                                     (es.IsInVehicle && es.CurrentVehicleId != null));

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// بررسی اینکه آیا کارمندانی ورود اولیه داشته‌اند یا نه (بدون خروج نهایی)
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>true اگر کارمندانی ورود اولیه داشته باشند و هنوز خروج نهایی نداشته باشند</returns>
        public async Task<bool> HasAnyEmployeeEnteredTodayAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;

            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == targetDate.Date &&
                                          es.HasInitialEntry); // ورود اولیه انجام شده

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// دریافت لیست رانندگان موجود
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست رانندگان موجود</returns>
        public async Task<List<EmployeeStatus>> GetAvailableDriversAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Where(es => es.Date.Date == targetDate.Date &&
                                          es.HasInitialEntry &&
                                          es.IsPresentInBuilding &&
                                          !es.FinalExitTime.HasValue &&
                                          (es.Employee.HasDrivingLicense || (es.Employee.Job != null && es.Employee.Job.Title.Contains("راننده"))));

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// به‌روزرسانی وضعیت کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="newStatus">وضعیت جدید</param>
        /// <param name="userId">شناسه کاربر انجام‌دهنده</param>
        /// <param name="notes">توضیحات</param>
        /// <param name="exitPermitNumber">شماره برگه خروج</param>
        /// <param name="vehicleId">شناسه خودرو (در صورت استفاده)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> UpdateEmployeeStatusAsync(int employeeId, EmployeeCurrentStatus newStatus, 
            int userId, string? notes = null, string? exitPermitNumber = null, int? vehicleId = null)
        {
            try
            {
                var today = DateTime.Today;
                var existingStatus = await GetEmployeeStatusAsync(employeeId, today);

                if (existingStatus == null)
                {
                    // ایجاد وضعیت جدید
                    existingStatus = new EmployeeStatus
                    {
                        EmployeeId = employeeId,
                        Date = today,
                        CurrentStatus = newStatus,
                        UpdatedByUserId = userId,
                        Notes = notes,
                        ExitPermitNumber = exitPermitNumber,
                        CurrentVehicleId = vehicleId,
                        IsInVehicle = vehicleId.HasValue,
                        LastUpdated = DateTime.Now
                    };

                    // تنظیم فیلدهای جدید بر اساس وضعیت
                    switch (newStatus)
                    {
                        case EmployeeCurrentStatus.PresentInBuilding:
                            existingStatus.HasInitialEntry = true;
                            existingStatus.IsPresentInBuilding = true;
                            existingStatus.FinalExitTime = null; // Ensure final exit time is cleared on entry
                            existingStatus.EntryTime = DateTime.Now;
                            break;
                        case EmployeeCurrentStatus.HourlyExit:
                        case EmployeeCurrentStatus.OfficialMission:
                            existingStatus.HasInitialEntry = true;
                            existingStatus.IsPresentInBuilding = false;
                            break;
                        case EmployeeCurrentStatus.OutOfOffice:
                            existingStatus.HasInitialEntry = true;
                            existingStatus.IsPresentInBuilding = false;
                            existingStatus.FinalExitTime = DateTime.Now;
                            break;
                        default:
                            existingStatus.HasInitialEntry = false;
                            existingStatus.IsPresentInBuilding = false;
                            break;
                    }

                    _context.EmployeeStatuses.Add(existingStatus);
                }
                else
                {
                    // به‌روزرسانی وضعیت موجود
                    existingStatus.CurrentStatus = newStatus;
                    existingStatus.UpdatedByUserId = userId;
                    existingStatus.LastUpdated = DateTime.Now;
                    existingStatus.Notes = notes;
                    existingStatus.ExitPermitNumber = exitPermitNumber;
                    existingStatus.CurrentVehicleId = vehicleId;
                    existingStatus.IsInVehicle = vehicleId.HasValue;

                    // تنظیم فیلدهای جدید بر اساس وضعیت جدید
                    switch (newStatus)
                    {
                        case EmployeeCurrentStatus.PresentInBuilding:
                            existingStatus.HasInitialEntry = true;
                            existingStatus.IsPresentInBuilding = true;
                            existingStatus.FinalExitTime = null; // Ensure final exit time is cleared on re-entry
                            if (!existingStatus.EntryTime.HasValue)
                            {
                                existingStatus.EntryTime = DateTime.Now;
                            }
                            break;
                        case EmployeeCurrentStatus.HourlyExit:
                        case EmployeeCurrentStatus.OfficialMission:
                            existingStatus.HasInitialEntry = true;
                            existingStatus.IsPresentInBuilding = false;
                            break;
                        case EmployeeCurrentStatus.OutOfOffice:
                            existingStatus.HasInitialEntry = true;
                            existingStatus.IsPresentInBuilding = false;
                            existingStatus.FinalExitTime = DateTime.Now;
                            break;
                        default:
                            // برای سایر وضعیت‌ها تغییری نمی‌دهیم
                            break;
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating employee status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ثبت ورود اولیه کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterEmployeeEntryAsync(int employeeId, int userId)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.PresentInBuilding, userId, "ورود اولیه روزانه");
        }

        /// <summary>
        /// ثبت خروج نهایی کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="notes">توضیحات خروج</param>
        /// <param name="vehicleId">شناسه خودرو (در صورت خروج با خودرو)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterEmployeeExitAsync(int employeeId, int userId, string? notes = null, int? vehicleId = null)
        {
            var exitNotes = string.IsNullOrEmpty(notes) ? "خروج ساعتی" : notes;
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.HourlyExit, userId, exitNotes, null, vehicleId);
        }

        public async Task<bool> RegisterFinalExitAsync(int employeeId, int userId, string? notes = null)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.OutOfOffice, userId, notes);
        }

        /// <summary>
        /// ثبت خروج ساعتی کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="exitPermitNumber">شماره برگه خروج</param>
        /// <param name="notes">توضیحات</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterHourlyExitAsync(int employeeId, int userId, string? exitPermitNumber = null, string? notes = null, int? vehicleId = null)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.HourlyExit, userId, notes, exitPermitNumber, vehicleId);
        }

        /// <summary>
        /// ثبت ماموریت اداری کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="notes">توضیحات ��اموریت</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterOfficialMissionAsync(int employeeId, int userId, string? notes = null)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.OfficialMission, userId, notes);
        }

        /// <summary>
        /// دریافت آمار وضعیت کارمندان
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>آمار وضعیت کارمندان</returns>
        public async Task<Dictionary<EmployeeCurrentStatus, int>> GetEmployeeStatusStatsAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == targetDate.Date);

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            var stats = await query.GroupBy(es => es.CurrentStatus)
                                  .Select(g => new { Status = g.Key, Count = g.Count() })
                                  .ToListAsync();

            var result = new Dictionary<EmployeeCurrentStatus, int>();
            foreach (EmployeeCurrentStatus status in Enum.GetValues<EmployeeCurrentStatus>())
            {
                result[status] = stats.FirstOrDefault(s => s.Status == status)?.Count ?? 0;
            }

            return result;
        }

        /// <summary>
        /// بررسی امکان خروج کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>true اگر امکان خروج وجود داشته باشد</returns>
        public async Task<bool> CanEmployeeExitAsync(int employeeId, DateTime? date = null)
        {
            var status = await GetEmployeeStatusAsync(employeeId, date);
            return status?.CanExitBuilding() ?? false;
        }

        /// <summary>
        /// بررسی امکان ورود کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>true اگر امکان ورود وج��د داشته باشد</returns>
        public async Task<bool> CanEmployeeEnterAsync(int employeeId, DateTime? date = null)
        {
            var status = await GetEmployeeStatusAsync(employeeId, date);
            return status?.CanEnterBuilding() ?? true; // اگر وضعیتی ثبت نشده، می‌تواند وارد شود
        }
    }
}
