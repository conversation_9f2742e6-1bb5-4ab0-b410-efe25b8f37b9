using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Data.Models; // Added
namespace EmployeeTrafficControl.Data.Models
{
    /// <summary>
    /// مدل گزارش ترافیک کارمندان
    /// </summary>
    public class EmployeeTrafficReportData
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int? BuildingId { get; set; }
        public int? EmployeeId { get; set; }
        public List<EmployeeTrafficReportItem> Items { get; set; } = new();
        public int TotalRecords { get; set; }
        public int TotalEmployees { get; set; }
    }

    public class EmployeeTrafficReportItem
    {
        public Employee Employee { get; set; } = default!;
        public DateTime Date { get; set; }
        public DateTime? EntryTime { get; set; }
        public DateTime? FinalExitTime { get; set; }
        public EmployeeCurrentStatus CurrentStatus { get; set; }
        public string? Notes { get; set; }
        public bool IsInVehicle { get; set; }
        public int? CurrentVehicleId { get; set; }

        public TimeSpan? WorkDuration => EntryTime.HasValue && FinalExitTime.HasValue 
            ? FinalExitTime.Value - EntryTime.Value 
            : null;

        public string StatusDisplayName => CurrentStatus switch
        {
            EmployeeCurrentStatus.PresentInBuilding => "حاضر در ساختمان",
            EmployeeCurrentStatus.HourlyExit => "خروج ساعتی",
            EmployeeCurrentStatus.OfficialMission => "ماموریت اداری",
            EmployeeCurrentStatus.OutOfOffice => "خارج از اداره",
            EmployeeCurrentStatus.OnLeave => "مرخصی",
            _ => "نامشخص"
        };

        public string StatusBadgeClass => CurrentStatus switch
        {
            EmployeeCurrentStatus.PresentInBuilding => "bg-success",
            EmployeeCurrentStatus.HourlyExit => "bg-warning",
            EmployeeCurrentStatus.OfficialMission => "bg-info",
            EmployeeCurrentStatus.OutOfOffice => "bg-danger",
            EmployeeCurrentStatus.OnLeave => "bg-secondary",
            _ => "bg-light"
        };
    }

    /// <summary>
    /// مدل گزارش ترافیک خودروها
    /// </summary>
    public class CarTrafficReportData
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int? BuildingId { get; set; }
        public int? CarId { get; set; }
        public List<CarTrafficReportItem> Items { get; set; } = new();
        public int TotalRecords { get; set; }
        public int TotalCars { get; set; }
    }

    public class CarTrafficReportItem
    {
        public Car Car { get; set; } = default!;
        public Employee? DriverEmployee { get; set; }
        public Building Building { get; set; } = default!;
        public DateTime? EntryTime { get; set; }
        public DateTime? ExitTime { get; set; }
        public string CurrentStatus { get; set; } = "";
        public string? ExitType { get; set; }
        public string? Notes { get; set; }
        public int PassengerCount { get; set; }
        public List<Employee> Passengers { get; set; } = new();
        public TimeSpan? Duration { get; set; }

        public string StatusBadgeClass => CurrentStatus switch
        {
            "در پارکینگ" => "bg-success",
            "خارج از پارکینگ" => "bg-warning",
            _ => "bg-secondary"
        };

        public string ExitTypeBadgeClass => ExitType switch
        {
            "اداری" => "bg-info",
            "ماموریت" => "bg-primary",
            "پولرسانی" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    /// <summary>
    /// مدل گزارش حضور و غیاب
    /// </summary>
    public class AttendanceReportData
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int? BuildingId { get; set; }
        public int? EmployeeId { get; set; }
        public List<AttendanceReportItem> Items { get; set; } = new();
        public int TotalRecords { get; set; }
        public int TotalEmployees { get; set; }
        public int TotalDays { get; set; }
        public int TotalPresent { get; set; }
        public int TotalAbsent { get; set; }
        public double AttendancePercentage { get; set; }
    }

    public class AttendanceReportItem
    {
        public Employee Employee { get; set; } = default!;
        public DateTime Date { get; set; }
        public DateTime? CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }
        public bool IsPresent { get; set; }
        public TimeSpan? TotalWorkHours { get; set; }
        public int LateMinutes { get; set; }
        public int EarlyLeaveMinutes { get; set; }
        public int HourlyExitCount { get; set; }
        public TimeSpan? TotalHourlyExitTime { get; set; }
        public string? Notes { get; set; }
        public bool IsApproved { get; set; }

        public string AttendanceStatusBadgeClass => IsPresent switch
        {
            true => "bg-success",
            false => "bg-danger"
        };

        public string AttendanceStatusText => IsPresent ? "حاضر" : "غایب";

        public string ApprovalStatusBadgeClass => IsApproved switch
        {
            true => "bg-success",
            false => "bg-warning"
        };

        public string ApprovalStatusText => IsApproved ? "تایید شده" : "در انتظار تایید";
    }

    /// <summary>
    /// مدل گزارش تفصیلی روزانه
    /// </summary>
    public class DetailedDailyReportData
    {
        public DateTime Date { get; set; }
        public int? BuildingId { get; set; }
        public List<EmployeeStatus> EmployeeStatuses { get; set; } = new();
        public List<CarTrafficLog> CarTrafficLogs { get; set; } = new();
        public int TotalEmployeeMovements { get; set; }
        public int TotalCarMovements { get; set; }
    }

    /// <summary>
    /// مدل فیلتر گزارشات
    /// </summary>
    public class ReportFilterModel
    {
        [Display(Name = "از تاریخ")]
        [DataType(DataType.Date)]
        public DateTime FromDate { get; set; } = DateTime.Today.AddDays(-7);

        [Display(Name = "تا تاریخ")]
        [DataType(DataType.Date)]
        public DateTime ToDate { get; set; } = DateTime.Today;

        [Display(Name = "ساختمان")]
        public int? BuildingId { get; set; }

        [Display(Name = "کارمند")]
        public int? EmployeeId { get; set; }

        [Display(Name = "خودرو")]
        public int? CarId { get; set; }

        [Display(Name = "نوع گزارش")]
        public string ReportType { get; set; } = "employee";

        [Display(Name = "فرمت خروجی")]
        public string ExportFormat { get; set; } = "html";
    }

    /// <summary>
    /// انواع گزارش
    /// </summary>
    public enum ReportType
    {
        [Display(Name = "ترافیک کارمندان")]
        EmployeeTraffic,

        [Display(Name = "ترافیک خودروها")]
        CarTraffic,

        [Display(Name = "حضور و غیاب")]
        Attendance,

        [Display(Name = "گزارش تفصیلی روزانه")]
        DetailedDaily
    }

    /// <summary>
    /// فرمت‌های خروجی گزارش
    /// </summary>
    public enum ExportFormat
    {
        [Display(Name = "HTML")]
        Html,

        [Display(Name = "Excel")]
        Excel,

        [Display(Name = "PDF")]
        Pdf,

        [Display(Name = "CSV")]
        Csv
    }
}
