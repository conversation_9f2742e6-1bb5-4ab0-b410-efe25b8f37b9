﻿using EmployeeTrafficControl.Data.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Data.Models
{
    public class CarPassenger
    {
        [Key]
        [Display(Name = "شناسه سرنشین")]
        public int CarPassengerId { get; set; }

        [Display(Name = "شناسه تردد خودرو")]
        public int CarTrafficLogId { get; set; }

        [Display(Name = "کارمند")]
        public int EmployeeId { get; set; }

        [Display(Name = "وارد شده است؟")]
        public bool IsEntered { get; set; } = false;

        [Display(Name = "تردد خودرو")]
        public CarTrafficLog CarTrafficLog { get; set; } = default!;

        [Display(Name = "کارمند")]
        public Employee Employee { get; set; } = default!;
    }
}