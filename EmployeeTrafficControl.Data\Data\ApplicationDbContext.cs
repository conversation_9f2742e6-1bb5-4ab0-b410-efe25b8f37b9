using EmployeeTrafficControl.Data.Models;

using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Data.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser, IdentityRole<int>, int>(options)
    {
        // DbSets اصلی
        public DbSet<Building> Buildings { get; set; }
        public DbSet<Job> Jobs { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Car> Cars { get; set; }
        public DbSet<DailyAttendance> DailyAttendances { get; set; }
        public DbSet<CarTrafficLog> CarTrafficLogs { get; set; }
        public DbSet<CarPassenger> CarPassengers { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }
        public DbSet<CarKilometer> CarKilometers { get; set; }
        public DbSet<GuestCarTrafficLog> GuestCarTrafficLogs { get; set; }
        public DbSet<EmployeeWorkingHours> EmployeeWorkingHours { get; set; }

        // DbSets اضافی مورد نیاز
        public new DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<EmployeeStatus> EmployeeStatuses { get; set; }
        public DbSet<DailyWorkSchedule> DailyWorkSchedules { get; set; }
        public DbSet<TrafficLog> TrafficLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("EmployeeTrafficControl");
            base.OnModelCreating(modelBuilder);

            // Call the configuration methods to apply model configurations
            ConfigureIdentity(modelBuilder);
            ConfigureMainEntities(modelBuilder);
            // ConfigureUserPermissionSystem is called from ConfigureMainEntities, so no direct call here.
        }

        private void ConfigureIdentity(ModelBuilder modelBuilder)
        {
            // تنظیمات ApplicationUser
            modelBuilder.Entity<ApplicationUser>(entity =>
            {
                entity.ToTable("AspNetUsers");
                entity.Property(e => e.FullName).HasMaxLength(200);

                // رابطه با Employee (One-to-One, ApplicationUser is dependent)
                entity.HasOne(u => u.Employee)
                      .WithOne(e => e.User) // Employee has a 'User' property back to ApplicationUser
                      .HasForeignKey<ApplicationUser>(u => u.EmployeeId) // FK is on ApplicationUser
                      .IsRequired(false) // Optional relationship because EmployeeId is nullable
                      .OnDelete(DeleteBehavior.SetNull); // When Employee is deleted, set EmployeeId in ApplicationUser to null

                // رابطه با Building (One-to-Many: Building has many ApplicationUsers, ApplicationUser has one Building)
                entity.HasOne(u => u.Building)
                      .WithMany(b => b.Users) // Assuming Building has public ICollection<ApplicationUser> Users
                      .HasForeignKey(u => u.BuildingId)
                      .OnDelete(DeleteBehavior.SetNull); // Valid with nullable FK

                // رابطه با Role (One-to-Many: Role has many ApplicationUsers, ApplicationUser has one Role)
                entity.HasOne(u => u.Role)
                      .WithMany(r => r.Users) // Assuming Role has public ICollection<ApplicationUser> Users
                      .HasForeignKey(u => u.RoleId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureMainEntities(ModelBuilder modelBuilder)
        {
            // Employee
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.NationalCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.PersonnelCode)
                .IsUnique();

            // Car
            modelBuilder.Entity<Car>()
                .HasIndex(c => c.PlateNumber)
                .IsUnique();

            // روابط اصلی
            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Building)
                .WithMany(b => b.Employees)
                .HasForeignKey(e => e.BuildingId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Job)
                .WithMany(j => j.Employees)
                .HasForeignKey(e => e.JobId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Job>()
                .HasOne(j => j.Building)
                .WithMany(b => b.Jobs)
                .HasForeignKey(j => j.BuildingId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Car>()
                .HasOne(c => c.Building)
                .WithMany(b => b.Cars)
                .HasForeignKey(c => c.BuildingId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<CarTrafficLog>()
                .HasOne(ctl => ctl.DriverEmployee)
                .WithMany(e => e.CarTrafficLogsAsDriver)
                .HasForeignKey(ctl => ctl.DriverEmployeeId)
                .OnDelete(DeleteBehavior.Restrict);

            // تنظیمات User و Permission system
            ConfigureUserPermissionSystem(modelBuilder);
        }

        private void ConfigureUserPermissionSystem(ModelBuilder modelBuilder)
        {
            // UserPermission relationships
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany(u => u.UserPermissions) // Using the new UserPermissions collection in ApplicationUser
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany()
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.GrantedByUser)
                .WithMany()
                .HasForeignKey(up => up.GrantedByUserId)
                .OnDelete(DeleteBehavior.NoAction);

            // RolePermission relationships
            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany()
                .HasForeignKey(rp => rp.PermissionId)
                .OnDelete(DeleteBehavior.Restrict);

            // UserSession relationships
            modelBuilder.Entity<UserSession>()
                .HasOne(us => us.User)
                .WithMany()
                .HasForeignKey(us => us.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            modelBuilder.Entity<ApplicationUser>()
                .HasIndex(u => u.UserName)
                .IsUnique();

            modelBuilder.Entity<UserPermission>()
                .HasIndex(up => new { up.UserId, up.PermissionId })
                .IsUnique();

            modelBuilder.Entity<RolePermission>()
                .HasIndex(rp => new { rp.RoleId, rp.PermissionId })
                .IsUnique();
        }
    }
}