using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Web.Pages
{
    public class IndexModel : PageModel
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public IndexModel(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }

        public ApplicationUser? CurrentUser { get; set; }
        public IList<string> UserRoles { get; set; } = new List<string>();
        public int TotalBuildings { get; set; }
        public int TotalJobs { get; set; }
        public int TotalEmployees { get; set; }
        public int TotalCars { get; set; }
        public async Task<IActionResult> OnGetAsync()
        {
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            CurrentUser = await _userManager.GetUserAsync(User);
            if (CurrentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            UserRoles = await _userManager.GetRolesAsync(CurrentUser);
            return Page();

            TotalBuildings = 0;
            TotalJobs = 0;
            TotalEmployees = 0;
            TotalCars = 0;

        }
    }
} 