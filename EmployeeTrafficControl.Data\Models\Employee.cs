﻿using EmployeeTrafficControl.Data.Models; // Corrected namespace
using EmployeeTrafficControl.Data.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Data.Models // Corrected namespace
{
    public class Employee
    {
        [Key]
        [Display(Name = "شناسه کارمند")]
        public int EmployeeId { get; set; }


        [StringLength(10, MinimumLength = 10, ErrorMessage = "کد ملی باید 10 رقم باشد.")]
        [Display(Name = "کد ملی")]
        public string? NationalCode { get; set; }

        [Required(ErrorMessage = "نام اجباری است.")]
        [StringLength(50, ErrorMessage = "نام حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نام")]
        public string FirstName { get; set; } = string.Empty; // Initialized to avoid null warning

        [Required(ErrorMessage = "نام خانوادگی اجباری است.")]
        [StringLength(50, ErrorMessage = "نام خانوادگی حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نام خانوادگی")]
        public string LastName { get; set; } = string.Empty; // Initialized to avoid null warning

        [Required(ErrorMessage = "کد پرسنلی اجباری است.")]
        [StringLength(20, ErrorMessage = "کد پرسنلی حداکثر 20 کاراکتر باشد.")]
        [Display(Name = "کد پرسنلی")]
        public string PersonnelCode { get; set; } = string.Empty; // Initialized to avoid null warning

        [StringLength(20, ErrorMessage = "شماره تلفن حداکثر 20 کاراکتر باشد.")]
        [Display(Name = "شماره تماس")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Display(Name = "شغل")]
        public int JobId { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "دارای گواهینامه رانندگی")]
        public bool HasDrivingLicense { get; set; } = false;

        // Navigation properties
        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = null!; // Made non-nullable as BuildingId is non-nullable
        [Display(Name = "شغل")]
        public Job? Job { get; set; }
        public ApplicationUser? User { get; set; } // Made nullable for consistent one-to-one relationship
        public ICollection<EmployeeWorkingHours> EmployeeWorkingHours { get; set; } = new List<EmployeeWorkingHours>();
        public ICollection<TrafficLog> TrafficLogs { get; set; } = new List<TrafficLog>();
        public ICollection<CarTrafficLog> CarTrafficLogsAsDriver { get; set; } = new List<CarTrafficLog>();
        public ICollection<CarPassenger> CarPassengers { get; set; } = new List<CarPassenger>();
    }
}