using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    [AuthorizePermission("CREATE_EMPLOYEE")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class CreateModel : PageModel
    {
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;
        private readonly JobService _jobService;

        public CreateModel(EmployeeService employeeService, BuildingService buildingService, JobService jobService)
        {
            _employeeService = employeeService;
            _buildingService = buildingService;
            _jobService = jobService;
        }

        [BindProperty]
        public Employee Employee { get; set; } = default!;

        public SelectList Buildings { get; set; } = default!;
        public SelectList Jobs { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? buildingId)
        {
            await LoadSelectLists();
            
            Employee = new Employee { IsActive = true };
            
            // Pre-select building if provided
            if (buildingId.HasValue)
            {
                Employee.BuildingId = buildingId.Value;
            }
            
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // Remove validation errors for navigation properties
            ModelState.Remove("Employee.Building");
            ModelState.Remove("Employee.Job");

            if (!ModelState.IsValid)
            {
                await LoadSelectLists();
                return Page();
            }

            // Check if personnel code already exists
            bool personnelCodeExists = await _employeeService.EmployeeCodeExistsAsync(Employee.PersonnelCode, null);
            if (personnelCodeExists)
            {
                ModelState.AddModelError("Employee.PersonnelCode", "کد پرسنلی وارد شده قبلاً ثبت شده است.");
                await LoadSelectLists();
                return Page();
            }

            try
            {
                await _employeeService.AddEmployeeAsync(Employee);
                TempData["SuccessMessage"] = "کارمند جدید با موفقیت اضافه شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var buildings = await _buildingService.GetAllBuildingsAsync();
            var jobs = await _jobService.GetAllJobsAsync();

            Buildings = new SelectList(buildings, "BuildingId", "Name");
            Jobs = new SelectList(jobs, "JobId", "Title");
        }
    }
}
