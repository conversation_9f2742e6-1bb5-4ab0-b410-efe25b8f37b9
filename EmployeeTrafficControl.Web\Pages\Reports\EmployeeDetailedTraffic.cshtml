@page "/Reports/EmployeeDetailedTraffic"
@model EmployeeTrafficControl.Web.Pages.Reports.EmployeeDetailedTrafficModel
@{
    ViewData["Title"] = "گزارش تفصیلی تردد کارمند";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-person-lines-fill"></i> گزارش تفصیلی تردد کارمند
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/Dashboard">داشبورد</a></li>
                        <li class="breadcrumb-item"><a href="/Reports">گزارشات</a></li>
                        <li class="breadcrumb-item active">تردد تفصیلی کارمند</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- فیلترها -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel"></i> فیلترهای جستجو
            </h5>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" onchange="this.form.submit()">
                            <option value="">همه ساختمان‌ها</option>
                            @foreach (var building in Model.AvailableBuildings)
                            {
                                <option value="@building.BuildingId" selected="@(Model.BuildingId == building.BuildingId)">
                                    @building.Name
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">جستجوی کارمند</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="نام، نام خانوادگی یا کد پرسنلی">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">از تاریخ</label>
                        <input type="date" name="FromDate" value="@Model.FromDate?.ToString("yyyy-MM-dd")" class="form-control">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">تا تاریخ</label>
                        <input type="date" name="ToDate" value="@Model.ToDate?.ToString("yyyy-MM-dd")" class="form-control">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="bi bi-search"></i> جستجو
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- انتخاب کارمند -->
    @if (Model.AvailableEmployees.Any())
    {
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people"></i> انتخاب کارمند
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach (var employee in Model.AvailableEmployees.Take(20))
                    {
                        <div class="col-md-6 col-lg-4 mb-2">
                            <a href="?EmployeeId=@employee.EmployeeId&FromDate=@Model.FromDate?.ToString("yyyy-MM-dd")&ToDate=@Model.ToDate?.ToString("yyyy-MM-dd")&BuildingId=@Model.BuildingId&SearchTerm=@Model.SearchTerm" 
                               class="btn btn-outline-primary w-100 text-start @(Model.EmployeeId == employee.EmployeeId ? "active" : "")">
                                <div>
                                    <strong>@employee.FirstName @employee.LastName</strong>
                                    <small class="d-block text-muted">@employee.PersonnelCode - @employee.Job?.Title</small>
                                </div>
                            </a>
                        </div>
                    }
                </div>
                @if (Model.AvailableEmployees.Count > 20)
                {
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle"></i>
                        @(Model.AvailableEmployees.Count - 20) کارمند دیگر یافت شد. لطفاً جستجو را محدودتر کنید.
                    </div>
                }
            </div>
        </div>
    }

    <!-- نتایج گزارش -->
    @if (Model.ReportData != null)
    {
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> گزارش تفصیلی - @Model.ReportData.Employee?.FirstName @Model.ReportData.Employee?.LastName
                </h5>
                <form method="post" asp-page-handler="Export" class="d-inline">
                    <input type="hidden" name="EmployeeId" value="@Model.EmployeeId" />
                    <input type="hidden" name="FromDate" value="@Model.FromDate?.ToString("yyyy-MM-dd")" />
                    <input type="hidden" name="ToDate" value="@Model.ToDate?.ToString("yyyy-MM-dd")" />
                    <button type="submit" class="btn btn-success btn-sm">
                        <i class="bi bi-download"></i> خروجی Excel
                    </button>
                </form>
            </div>
            <div class="card-body">
                <!-- آمار کلی -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.TotalDays</h4>
                                <small>روز حضور</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.TotalWorkHours.ToString("F1")</h4>
                                <small>ساعت کار</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.TotalHourlyExits</h4>
                                <small>خروج ساعتی</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.TotalMissions</h4>
                                <small>ماموریت</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول تفصیلی روزانه -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>تاریخ</th>
                                <th>ورود</th>
                                <th>خروج نهایی</th>
                                <th>ساعت کار</th>
                                <th>خروج ساعتی</th>
                                <th>ماموریت</th>
                                <th>سفر خودرو</th>
                                <th>جزئیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var daily in Model.ReportData.DailyReports.OrderByDescending(d => d.Date))
                            {
                                <tr>
                                    <td>
                                        <strong>@daily.Date.ToPersianDate().ToPersianNumbers()</strong>
                                        <small class="d-block text-muted">@daily.Date.GetPersianDayOfWeek()</small>
                                    </td>
                                    <td>
                                        @if (daily.EntryTime.HasValue)
                                        {
                                            <span class="badge bg-success">@daily.EntryTime.Value.ToString("HH:mm")</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غایب</span>
                                        }
                                    </td>
                                    <td>
                                        @if (daily.FinalExitTime.HasValue)
                                        {
                                            <span class="badge bg-danger">@daily.FinalExitTime.Value.ToString("HH:mm")</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">در ساختمان</span>
                                        }
                                    </td>
                                    <td>
                                        @if (daily.TotalWorkHours.HasValue)
                                        {
                                            <span>@daily.TotalWorkHours.Value.ToString(@"hh\:mm")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (daily.HourlyExitCount > 0)
                                        {
                                            <span class="badge bg-warning">@daily.HourlyExitCount</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                        <!-- Debug Info -->
                                        <small class="d-block text-muted">
                                            کل ترددها: @daily.TrafficLogs.Count
                                        </small>
                                    </td>
                                    <td>
                                        @if (daily.MissionCount > 0)
                                        {
                                            <span class="badge bg-info">@daily.MissionCount</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                        <!-- Debug Info -->
                                        <small class="d-block text-muted">
                                            انواع: @string.Join(", ", daily.TrafficLogs.Select(t => t.TrafficType ?? "null").Distinct())
                                        </small>
                                    </td>
                                    <td>
                                        @if (daily.CarTripCount > 0)
                                        {
                                            <span class="badge bg-primary">@daily.CarTripCount</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#<EMAIL>("yyyyMMdd")"
                                                aria-expanded="false">
                                            <i class="bi bi-eye"></i> جزئیات
                                        </button>
                                    </td>
                                </tr>

                                <!-- ردیف جزئیات ترددهای روزانه -->
                                <tr class="collapse" id="<EMAIL>("yyyyMMdd")">
                                    <td colspan="8" class="p-0">
                                        <div class="bg-light p-3">
                                            <h6 class="mb-3">
                                                <i class="bi bi-list-ul"></i>
                                                جزئیات ترددهای @daily.Date.ToPersianDate().ToPersianNumbers()
                                            </h6>

                                            @if (daily.TrafficLogs.Any() || daily.CarTrafficLogs.Any())
                                            {
                                                <div class="row">
                                                    <!-- ترددهای پیاده -->
                                                    @if (daily.TrafficLogs.Any())
                                                    {
                                                        <div class="col-md-6">
                                                            <h6 class="text-primary">
                                                                <i class="bi bi-person-walking"></i> ترددهای پیاده
                                                            </h6>
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-bordered">
                                                                    <thead class="table-secondary">
                                                                        <tr>
                                                                            <th>زمان</th>
                                                                            <th>نوع</th>
                                                                            <th>توضیحات</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @foreach (var log in daily.TrafficLogs.OrderBy(l => l.EntryTime ?? l.ExitTime))
                                                                        {
                                                                            <tr>
                                                                                <td>
                                                                                    @if (log.EntryTime.HasValue)
                                                                                    {
                                                                                        <span class="badge bg-success">
                                                                                            ورود @log.EntryTime.Value.ToString("HH:mm")
                                                                                        </span>
                                                                                    }
                                                                                    @if (log.ExitTime.HasValue)
                                                                                    {
                                                                                        <span class="badge bg-danger">
                                                                                            خروج @log.ExitTime.Value.ToString("HH:mm")
                                                                                        </span>
                                                                                    }
                                                                                </td>
                                                                                <td>
                                                                                    @{
                                                                                        var trafficTypeClass = "bg-secondary";
                                                                                        var trafficTypeName = "عادی";

                                                                                        if (!string.IsNullOrEmpty(log.TrafficType))
                                                                                        {
                                                                                            var lowerType = log.TrafficType.ToLower();
                                                                                            if (lowerType.Contains("خروج ساعتی") || lowerType.Contains("hourly") || lowerType.Contains("مرخصی"))
                                                                                            {
                                                                                                trafficTypeClass = "bg-warning";
                                                                                                trafficTypeName = "خروج ساعتی";
                                                                                            }
                                                                                            else if (lowerType.Contains("ماموریت") || lowerType.Contains("mission"))
                                                                                            {
                                                                                                trafficTypeClass = "bg-info";
                                                                                                trafficTypeName = "ماموریت";
                                                                                            }
                                                                                            else if (lowerType.Contains("ورود") || lowerType.Contains("entry"))
                                                                                            {
                                                                                                trafficTypeClass = "bg-success";
                                                                                                trafficTypeName = "ورود";
                                                                                            }
                                                                                            else if (lowerType.Contains("خروج") || lowerType.Contains("exit"))
                                                                                            {
                                                                                                trafficTypeClass = "bg-danger";
                                                                                                trafficTypeName = "خروج";
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                                trafficTypeName = log.TrafficType;
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                    <span class="badge @trafficTypeClass">@trafficTypeName</span>
                                                                                </td>
                                                                                <td>
                                                                                    @if (!string.IsNullOrEmpty(log.Description))
                                                                                    {
                                                                                        <small>@log.Description</small>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        <span class="text-muted">-</span>
                                                                                    }
                                                                                </td>
                                                                            </tr>
                                                                        }
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    }

                                                    <!-- ترددهای خودرو -->
                                                    @if (daily.CarTrafficLogs.Any())
                                                    {
                                                        <div class="col-md-6">
                                                            <h6 class="text-success">
                                                                <i class="bi bi-car-front"></i> ترددهای خودرو
                                                            </h6>
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-bordered">
                                                                    <thead class="table-secondary">
                                                                        <tr>
                                                                            <th>زمان</th>
                                                                            <th>خودرو</th>
                                                                            <th>نقش</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @foreach (var carLog in daily.CarTrafficLogs.OrderBy(cl => cl.ExitTime ?? cl.EntryTime))
                                                                        {
                                                                            <tr>
                                                                                <td>
                                                                                    @if (carLog.ExitTime.HasValue)
                                                                                    {
                                                                                        <span class="badge bg-danger">
                                                                                            خروج @carLog.ExitTime.Value.ToString("HH:mm")
                                                                                        </span>
                                                                                    }
                                                                                    @if (carLog.EntryTime.HasValue)
                                                                                    {
                                                                                        <span class="badge bg-success">
                                                                                            ورود @carLog.EntryTime.Value.ToString("HH:mm")
                                                                                        </span>
                                                                                    }
                                                                                </td>
                                                                                <td>
                                                                                    @if (carLog.Car != null)
                                                                                    {
                                                                                        <small>@carLog.Car.PlateNumber</small>
                                                                                    }
                                                                                </td>
                                                                                <td>
                                                                                    @if (carLog.DriverEmployeeId == daily.Employee?.EmployeeId)
                                                                                    {
                                                                                        <span class="badge bg-primary">راننده</span>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        <span class="badge bg-secondary">سرنشین</span>
                                                                                    }
                                                                                </td>
                                                                            </tr>
                                                                        }
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i>
                                                    هیچ تردد اضافی در این روز ثبت نشده است.
                                                </div>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else if (Model.EmployeeId.HasValue)
    {
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            هیچ رکورد ترددی برای کارمند انتخاب شده در بازه زمانی مشخص شده یافت نشد.
        </div>
    }
    else if (!Model.AvailableEmployees.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            لطفاً ابتدا فیلترهای جستجو را تنظیم کنید تا لیست کارمندان نمایش داده شود.
        </div>
    }
</div>

<script>
function showDailyDetails(date) {
    // اینجا می‌توانید مودال جزئیات روزانه را نمایش دهید
    alert('جزئیات روز ' + date + ' - این قابلیت در نسخه بعدی اضافه خواهد شد.');
}

// اطمینان از اجرای تقویم شمسی
document.addEventListener('DOMContentLoaded', function() {
    if (typeof initPersianDatePickers === 'function') {
        initPersianDatePickers();
    }
});
</script>
