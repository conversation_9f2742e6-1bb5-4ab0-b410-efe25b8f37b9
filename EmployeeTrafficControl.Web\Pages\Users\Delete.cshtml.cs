using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Data.Models;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    [AuthorizePermission("DELETE_USER")]
    public class DeleteModel : PageModel
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public DeleteModel(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }

        [BindProperty]
        public int UserId { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            UserId = id;
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var user = await _userManager.FindByIdAsync(UserId.ToString());
            if (user == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }
            try
            {
                var result = await _userManager.DeleteAsync(user);
                if (result.Succeeded)
                {
                TempData["SuccessMessage"] = "کاربر با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این کاربر وجود ندارد.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف کاربر: " + ex.Message;
            }
            return RedirectToPage("./Index");
        }
    }
}
