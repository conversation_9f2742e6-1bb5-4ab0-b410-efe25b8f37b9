@page
@model EmployeeTrafficControl.Web.Pages.Admin.DatabaseStatusModel
@{
    ViewData["Title"] = "وضعیت دیتابیس";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="bi bi-database"></i> وضعیت دیتابیس
            </h2>
            <p class="text-muted mb-0">نمای کلی از داده‌های موجود در سیستم</p>
        </div>
        <div>
            <a href="/admin/create-sample-data" class="btn btn-success me-2">
                <i class="bi bi-database-add"></i> ایجاد داده‌های نمونه
            </a>
            <a href="/dashboard" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>

    <!-- آمار کلی -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="bi bi-building display-4 mb-2"></i>
                    <h3 class="mb-1">@Model.Stats.BuildingsCount</h3>
                    <p class="mb-0">ساختمان</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="bi bi-people display-4 mb-2"></i>
                    <h3 class="mb-1">@Model.Stats.EmployeesCount</h3>
                    <p class="mb-0">کارمند</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="bi bi-car-front display-4 mb-2"></i>
                    <h3 class="mb-1">@Model.Stats.CarsCount</h3>
                    <p class="mb-0">خودرو</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="bi bi-briefcase display-4 mb-2"></i>
                    <h3 class="mb-1">@Model.Stats.JobsCount</h3>
                    <p class="mb-0">شغل</p>
                </div>
            </div>
        </div>
    </div>

    <!-- جزئیات خودروها -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-car-front-fill"></i> وضعیت خودروها
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">@Model.Stats.CarsInParkingCount</h4>
                                <p class="text-muted mb-0">در پارکینگ</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-danger">@Model.Stats.CarsOutOfParkingCount</h4>
                                <p class="text-muted mb-0">خارج از پارکینگ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-shield-check"></i> کاربران و دسترسی‌ها
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="text-primary">@Model.Stats.UsersCount</h4>
                                <p class="text-muted mb-0">کاربر</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="text-info">@Model.Stats.RolesCount</h4>
                                <p class="text-muted mb-0">نقش</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="text-secondary">@Model.Stats.PermissionsCount</h4>
                                <p class="text-muted mb-0">مجوز</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آمار امروز -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-day"></i> آمار امروز (@DateTime.Today.ToString("yyyy/MM/dd"))
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                                <h4 class="mt-2">@Model.Stats.TodayAttendanceCount</h4>
                                <p class="text-muted mb-0">رکورد حضور و غیاب</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-person-walking text-primary" style="font-size: 2rem;"></i>
                                <h4 class="mt-2">@Model.Stats.TodayEmployeeTrafficCount</h4>
                                <p class="text-muted mb-0">تردد کارمندان</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-car-front text-info" style="font-size: 2rem;"></i>
                                <h4 class="mt-2">@Model.Stats.TodayCarTrafficCount</h4>
                                <p class="text-muted mb-0">تردد خودروها</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- راهنمای حل مشکل -->
    @if (Model.Stats.CarsCount == 0)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning" role="alert">
                    <h5 class="alert-heading">
                        <i class="bi bi-exclamation-triangle"></i> هیچ خودرویی در سیستم وجود ندارد!
                    </h5>
                    <p>
                        برای استفاده از صفحه خروج خودرو، ابتدا باید خودروهایی در سیستم ثبت شوند.
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>راه‌حل:</strong>
                        <a href="/admin/create-sample-data" class="btn btn-success btn-sm ms-2">
                            <i class="bi bi-database-add"></i> ایجاد داده‌های نمونه
                        </a>
                        یا
                        <a href="/cars/create" class="btn btn-primary btn-sm ms-2">
                            <i class="bi bi-plus-circle"></i> افزودن خودرو جدید
                        </a>
                    </p>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }

    .btn {
        border-radius: 0.375rem;
    }

    .alert {
        border-radius: 0.5rem;
    }
</style>
