using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Services
{
    /// <summary>
    /// سرویس بررسی مجوزها و دسترسی‌ها
    /// </summary>
    public class AuthorizationService
    {
        private readonly ApplicationDbContext _context;

        public AuthorizationService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// بررسی دسترسی کاربر به مجوز مشخص
        /// </summary>
        public async Task<bool> HasPermissionAsync(int userId, string permissionCode)
        {
            var user = await _context.Users
                .Include(u => u.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .Include(u => u.UserPermissions)
                .ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.EmployeeId == userId && u.IsActive);

            if (user == null)
                return false;

            // بررسی مجوزهای خاص کاربر (اولویت بالاتر)
            var userPermission = user.UserPermissions
                .FirstOrDefault(up => up.Permission.Code == permissionCode && up.Permission.IsActive);

            if (userPermission != null)
            {
                return userPermission.PermissionType == PermissionType.Grant;
            }

            // بررسی مجوزهای نقش
            if (user.Role == null) return false;
            var hasRolePermission = user.Role.RolePermissions
                .Any(rp => rp.Permission.Code == permissionCode && rp.Permission.IsActive);

            return hasRolePermission;
        }

        /// <summary>
        /// دریافت تمام مجوزهای کاربر
        /// </summary>
        public async Task<List<string>> GetUserPermissionsAsync(int userId)
        {
            var user = await _context.Users
                .Include(u => u.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .Include(u => u.UserPermissions)
                .ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.EmployeeId == userId && u.IsActive);

            if (user == null)
                return new List<string>();

            var permissions = new HashSet<string>();

            // اضافه کردن مجوزهای نقش
            if (user.Role?.RolePermissions != null)
            {
                foreach (var rolePermission in user.Role.RolePermissions)
                {
                    if (rolePermission.Permission != null && rolePermission.Permission.IsActive)
                    {
                        permissions.Add(rolePermission.Permission.Code);
                    }
                }
            }

            // اعمال مجوزهای خاص کاربر (override)
            foreach (var userPermission in user.UserPermissions)
            {
                if (userPermission.Permission.IsActive)
                {
                    if (userPermission.PermissionType == PermissionType.Grant)
                    {
                        permissions.Add(userPermission.Permission.Code);
                    }
                    else if (userPermission.PermissionType == PermissionType.Deny)
                    {
                        permissions.Remove(userPermission.Permission.Code);
                    }
                }
            }

            return permissions.ToList();
        }

        /// <summary>
        /// اعطای مجوز خاص به کاربر
        /// </summary>
        public async Task<bool> GrantPermissionToUserAsync(int userId, int permissionId, int grantedByUserId, string? notes = null)
        {
            // بررسی وجود کاربر و مجوز
            var user = await _context.Users.FindAsync(userId);
            var permission = await _context.Permissions.FindAsync(permissionId);
            
            if (user == null || permission == null)
                return false;

            // حذف مجوز قبلی اگر وجود دارد
            var existingUserPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

            if (existingUserPermission != null)
            {
                _context.UserPermissions.Remove(existingUserPermission);
            }

            var userPermission = new UserPermission
            {
                UserId = userId,
                PermissionId = permissionId,
                PermissionType = PermissionType.Grant,
                GrantedByUserId = grantedByUserId,
                GrantedAt = DateTime.Now,
                Notes = notes
            };

            _context.UserPermissions.Add(userPermission);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// منع مجوز خاص از کاربر
        /// </summary>
        public async Task<bool> DenyPermissionToUserAsync(int userId, int permissionId, int grantedByUserId, string? notes = null)
        {
            // بررسی وجود کاربر و مجوز
            var user = await _context.Users.FindAsync(userId);
            var permission = await _context.Permissions.FindAsync(permissionId);
            
            if (user == null || permission == null)
                return false;

            // حذف مجوز قبلی اگر وجود دارد
            var existingUserPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

            if (existingUserPermission != null)
            {
                _context.UserPermissions.Remove(existingUserPermission);
            }

            var userPermission = new UserPermission
            {
                UserId = userId,
                PermissionId = permissionId,
                PermissionType = PermissionType.Deny,
                GrantedByUserId = grantedByUserId,
                GrantedAt = DateTime.Now,
                Notes = notes
            };

            _context.UserPermissions.Add(userPermission);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف مجوز خاص کاربر (بازگشت به مجوزهای نقش)
        /// </summary>
        public async Task<bool> RemoveUserPermissionAsync(int userId, int permissionId)
        {
            var userPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

            if (userPermission == null)
                return false;

            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// دریافت مجوزهای خاص کاربر
        /// </summary>
        public async Task<List<UserPermission>> GetUserSpecificPermissionsAsync(int userId)
        {
            return await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .Include(up => up.Permission)
                .Include(up => up.GrantedByUser)
                .OrderBy(up => up.Permission.Category)
                .ThenBy(up => up.Permission.Name)
                .ToListAsync();
        }
    }
}
