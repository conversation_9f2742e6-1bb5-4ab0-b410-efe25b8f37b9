
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;

namespace EmployeeTrafficControl.Core.Services
{
    public class TrafficLogService
    {
        private readonly ApplicationDbContext _context;
        private readonly EmployeeStatusService _employeeStatusService;

        public TrafficLogService(ApplicationDbContext context, EmployeeStatusService employeeStatusService)
        {
            _context = context;
            _employeeStatusService = employeeStatusService;
        }

        // Simplified Car Exit Registration
        public async Task<bool> RegisterCarExitAsync(int carId, int driverId, List<int> passengerIds, int buildingId, int userId, string? notes)
        {
            var car = await _context.Cars.FindAsync(carId);
            if (car == null || !car.IsInParking)
            {
                return false; // Car not found or already outside
            }

            var exitTime = DateTime.Now;

            // Create new exit log
            var newLog = new CarTrafficLog
            {
                CarId = carId,
                DriverEmployeeId = driverId,
                BuildingId = buildingId,
                ExitTime = exitTime,
                IsInParking = false, // The car is now exiting
                UserId = userId,
                Notes = notes
            };

            // Add passengers to the log
            foreach (var passengerId in passengerIds)
            {
                newLog.CarPassengers.Add(new CarPassenger { EmployeeId = passengerId });
            }

            _context.CarTrafficLogs.Add(newLog);

            // Update car status
            car.IsInParking = false;

            // Update employee statuses
            await _employeeStatusService.RegisterHourlyExitAsync(driverId, userId, null, notes, carId);
            foreach (var passengerId in passengerIds)
            {
                await _employeeStatusService.RegisterHourlyExitAsync(passengerId, userId, null, notes, carId);
            }

            await _context.SaveChangesAsync();
            return true;
        }

        // Simplified Car Entry Registration
        public async Task<bool> RegisterEntryAsync(int employeeId, int buildingId, int userId, DateTime? entryTime = null, string? notes = null)
        {
            var actualEntryTime = entryTime ?? DateTime.Now;
            var trafficLog = new TrafficLog
            {
                EmployeeId = employeeId,
                BuildingId = buildingId,
                EntryTime = actualEntryTime,
                TrafficType = "ورود",
                Description = notes,
                User = await _context.Users.FindAsync(userId)
            };
            _context.TrafficLogs.Add(trafficLog);
            await _employeeStatusService.RegisterEmployeeEntryAsync(employeeId, userId);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RegisterExitAsync(int employeeId, int buildingId, int userId, DateTime? exitTime = null, string trafficType = "خروج نهایی", string? notes = null, string? exitPermitNumber = null)
        {
            var actualExitTime = exitTime ?? DateTime.Now;
            var trafficLog = new TrafficLog
            {
                EmployeeId = employeeId,
                BuildingId = buildingId,
                ExitTime = actualExitTime,
                TrafficType = trafficType,
                Description = notes,
                User = await _context.Users.FindAsync(userId)
            };
            _context.TrafficLogs.Add(trafficLog);
            await _employeeStatusService.RegisterHourlyExitAsync(employeeId, userId, exitPermitNumber, notes);
            await _context.SaveChangesAsync();
            return true;
        }

        [Obsolete("Use RegisterCarExitAsync instead.")]
        public async Task<bool> RegisterCarExitLogAsync(int carTrafficLogId, int userId, DateTime? exitTime = null, string? notes = null)
        {
            // This is a legacy method and should be updated.
            var carTrafficLog = await _context.CarTrafficLogs.FindAsync(carTrafficLogId);
            if (carTrafficLog == null) return false;
            return await RegisterCarExitAsync(carTrafficLog.CarId, carTrafficLog.DriverEmployeeId, new List<int>(), carTrafficLog.BuildingId, userId, notes);
        }

        [Obsolete("Use RegisterCarEntryAsync instead.")]
        public async Task<int?> RegisterCarEntryLogAsync(int carId, int? driverEmployeeId, List<int> passengerIds, int buildingId, int userId, DateTime? entryTime = null, string? notes = null)
        {
            // This is a legacy method and should be updated.
            var result = await RegisterCarEntryAsync(carId, userId, passengerIds);
            var log = await _context.CarTrafficLogs.FirstOrDefaultAsync(l => l.CarId == carId && l.IsInParking);
            return log?.CarTrafficLogId;
        }

        public async Task<bool> RegisterCarEntryAsync(int carId, int userId, List<int> selectedPassengerIds)
        {
            var car = await _context.Cars.FindAsync(carId);
            if (car == null || car.IsInParking)
            {
                return false; // Car not found or already inside
            }

            var lastExitLog = await _context.CarTrafficLogs
                .Include(l => l.CarPassengers)
                .Where(l => l.CarId == carId && !l.IsInParking)
                .OrderByDescending(l => l.ExitTime)
                .FirstOrDefaultAsync();

            if (lastExitLog == null)
            {
                return false; // No open exit log found
            }

            // Close the exit log for the car itself
            lastExitLog.EntryTime = DateTime.Now;
            lastExitLog.IsInParking = true; // The car is now inside

            // Update car status
            car.IsInParking = true;

            // Update status for the driver
            await _employeeStatusService.RegisterEmployeeEntryAsync(lastExitLog.DriverEmployeeId, userId);

            // Update status ONLY for selected passengers
            foreach (var passenger in lastExitLog.CarPassengers)
            {
                if (selectedPassengerIds.Contains(passenger.EmployeeId))
                {
                    await _employeeStatusService.RegisterEmployeeEntryAsync(passenger.EmployeeId, userId);
                    passenger.IsEntered = true; // Mark as entered
                }
            }

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
