using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Linq;

namespace EmployeeTrafficControl.Web.Pages.Jobs
{
    [AuthorizePermission("DELETE_JOB")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class DeleteModel : PageModel
    {
        private readonly JobService _jobService;
        private readonly EmployeeService _employeeService;

        public DeleteModel(JobService jobService, EmployeeService employeeService)
        {
            _jobService = jobService;
            _employeeService = employeeService;
        }

        [BindProperty]
        public int JobId { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            JobId = id;

            var job = await _jobService.GetJobByIdAsync(id);
            if (job == null)
            {
                TempData["ErrorMessage"] = "شغل مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var job = await _jobService.GetJobByIdAsync(JobId);
            if (job == null)
            {
                TempData["ErrorMessage"] = "شغل مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Check if there are employees with this job
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            var employeesWithJob = allEmployees.Where(e => e.JobId == JobId).ToList();

            if (employeesWithJob.Any())
            {
                TempData["ErrorMessage"] = $"امکان حذف این شغل وجود ندارد زیرا {employeesWithJob.Count} کارمند با این شغل وجود دارد.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _jobService.DeleteJobAsync(JobId);
                TempData["SuccessMessage"] = "شغل با موفقیت حذف شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف شغل: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
