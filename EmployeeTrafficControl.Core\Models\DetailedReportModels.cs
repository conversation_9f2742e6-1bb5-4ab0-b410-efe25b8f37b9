using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Core.Models
{
    // مدل‌های گزارش تفصیلی کارمند
    public class EmployeeDetailedTrafficReportData
    {
        public int EmployeeId { get; set; }
        public Employee? Employee { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<EmployeeDailyTrafficItem> DailyReports { get; set; } = [];
        public int TotalDays { get; set; }
        public double TotalWorkHours { get; set; }
        public int TotalHourlyExits { get; set; }
        public int TotalMissions { get; set; }
        public int TotalCarTrips { get; set; }
    }

    public class EmployeeDailyTrafficItem
    {
        public DateTime Date { get; set; }
        public Employee? Employee { get; set; }
        public List<EmployeeStatus> DailyStatuses { get; set; } = [];
        public List<TrafficLog> TrafficLogs { get; set; } = [];
        public List<CarTrafficLog> CarTrafficLogs { get; set; } = [];
        public TimeSpan? TotalWorkHours { get; set; }
        public DateTime? EntryTime { get; set; }
        public DateTime? FinalExitTime { get; set; }
        public int HourlyExitCount { get; set; }
        public int MissionCount { get; set; }
        public int CarTripCount { get; set; }
    }

    // مدل‌های گزارش تفصیلی خودرو
    public class CarDetailedTrafficReportData
    {
        public int CarId { get; set; }
        public Car? Car { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<CarTrafficLog> TrafficLogs { get; set; } = [];
        public List<CarExitTypeStatistic> ExitTypeStatistics { get; set; } = [];
        public List<CarDriverStatistic> DriverStatistics { get; set; } = [];
        public int TotalTrips { get; set; }
        public TimeSpan TotalExitTime { get; set; }
        public double AveragePassengerCount { get; set; }
    }

    public class CarExitTypeStatistic
    {
        public string? ExitType { get; set; }
        public int Count { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public string DisplayName => ExitType switch
        {
            "general" => "عمومی",
            "treasury" => "خزانه‌داری",
            "mission" => "ماموریت",
            "administrative" => "اداری",
            _ => ExitType ?? "نامشخص"
        };
    }

    public class CarDriverStatistic
    {
        public Employee? DriverEmployee { get; set; }
        public int TripCount { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public string DriverName => DriverEmployee != null 
            ? $"{DriverEmployee.FirstName} {DriverEmployee.LastName}" 
            : "نامشخص";
    }
}
