﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class editdb : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                schema: "EmployeeTrafficControl",
                table: "Jobs",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                schema: "EmployeeTrafficControl",
                table: "Jobs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "OwnerEmployeeId",
                schema: "EmployeeTrafficControl",
                table: "Jobs",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                schema: "EmployeeTrafficControl",
                table: "Cars",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                schema: "EmployeeTrafficControl",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "IsActive",
                schema: "EmployeeTrafficControl",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "OwnerEmployeeId",
                schema: "EmployeeTrafficControl",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "IsActive",
                schema: "EmployeeTrafficControl",
                table: "Cars");
        }
    }
}
