using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Admin
{
    public class CreateSampleDataModel : PageModel
    {
        private readonly DatabaseInitializer _databaseInitializer;
        private readonly AuthenticationService _authService;

        public CreateSampleDataModel(
            DatabaseInitializer databaseInitializer,
            AuthenticationService authService)
        {
            _databaseInitializer = databaseInitializer;
            _authService = authService;
        }

        public string Message { get; set; } = "";
        public bool IsSuccess { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session?.User?.Role?.Code != "ADMIN")
            {
                TempData["ErrorMessage"] = "فقط ادمین‌ها به این بخش دسترسی دارند.";
                return RedirectToPage("/Dashboard");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session?.User?.Role?.Code != "ADMIN")
            {
                TempData["ErrorMessage"] = "فقط ادمین‌ها به این بخش دسترسی دارند.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await _databaseInitializer.CreateSampleDataAsync();
                Message = "داده‌های نمونه با موفقیت ایجاد شدند. شامل: ساختمان، شغل‌ها، کارمندان و خودروهای نمونه.";
                IsSuccess = true;
            }
            catch (Exception ex)
            {
                Message = $"خطا در ایجاد داده‌های نمونه: {ex.Message}";
                IsSuccess = false;
            }

            return Page();
        }
    }
}
