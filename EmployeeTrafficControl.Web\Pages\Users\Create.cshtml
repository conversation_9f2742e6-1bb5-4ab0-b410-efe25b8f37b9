@page "{employeeId:int?}"
@model EmployeeTrafficControl.Web.Pages.Users.CreateModel
@{
    ViewData["Title"] = "افزودن کاربر جدید";
}

<div class="page-header">
    <h1>افزودن کاربر جدید</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="form-section">
                        <h5 class="border-bottom pb-2 mb-3">اطلاعات کاربری</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="User.UserName" class="form-label">نام کاربری <span class="text-danger">*</span></label>
                                    <input asp-for="User.UserName" class="form-control" placeholder="نام کاربری منحصر به فرد" />
                                    <span asp-validation-for="User.UserName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="RoleName" class="form-label">نقش <span class="text-danger">*</span></label>
                                    <select asp-for="RoleName" class="form-select" asp-items="Model.Roles">
                                        <option value="">انتخاب کنید...</option>
                                    </select>
                                    <span asp-validation-for="RoleName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="User.FullName" class="form-label">نام کامل</label>
                                    <input asp-for="User.FullName" class="form-control" placeholder="نام کامل کاربر" />
                                    <span asp-validation-for="User.FullName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="User.Email" class="form-label">ایمیل</label>
                                    <input asp-for="User.Email" type="email" class="form-control" placeholder="آدرس ایمیل" />
                                    <span asp-validation-for="User.Email" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">رمز عبور <span class="text-danger">*</span></label>
                            <input type="password" id="password" name="Password" class="form-control" placeholder="رمز عبور" required />
                            <div class="form-text">رمز عبور باید حداقل 6 کاراکتر باشد</div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5 class="border-bottom pb-2 mb-3">اطلاعات تکمیلی</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="User.EmployeeId" class="form-label">کارمند مرتبط</label>
                                    <select asp-for="User.EmployeeId" class="form-select" asp-items="Model.Employees">
                                        <option value="">انتخاب کنید...</option>
                                    </select>
                                    <span asp-validation-for="User.EmployeeId" class="text-danger"></span>
                                    <div class="form-text">در صورت انتخاب، این کاربر به کارمند مشخص شده مرتبط می‌شود</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="User.BuildingId" class="form-label">ساختمان مرتبط</label>
                                    <select asp-for="User.BuildingId" class="form-select" asp-items="Model.Buildings">
                                        <option value="">همه ساختمان‌ها</option>
                                    </select>
                                    <span asp-validation-for="User.BuildingId" class="text-danger"></span>
                                    <div class="form-text">در صورت انتخاب، دسترسی کاربر به ساختمان مشخص محدود می‌شود</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره
                        </button>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        نام کاربری باید منحصر به فرد باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        رمز عبور حداقل 6 کاراکتر باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        مدیر سیستم به همه بخش‌ها دسترسی دارد
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
