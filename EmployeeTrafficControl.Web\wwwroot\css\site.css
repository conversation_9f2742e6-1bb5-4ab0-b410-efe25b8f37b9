/* RTL and Persian language styles */
html {
  font-size: 14px;
  direction: rtl;
  height: 100%;
  position: relative;
  min-height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Vazirmatn', '<PERSON><PERSON><PERSON>', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
}

/* Navigation styles */
.navbar {
  box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.navbar-brand {
  font-weight: 600;
  font-size: 1.2rem;
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: rgba(255,255,255,0.1);
  border-radius: 5px;
}

/* Card styles */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px 10px 0 0 !important;
  font-weight: 600;
}

/* Button styles */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  border: none;
}

.btn-success:hover {
  background: linear-gradient(135deg, #4e9a2a 0%, #96d4b8 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #e881f0 0%, #f04560 100%);
}

.btn-danger {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  border: none;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #ff2a5a 0%, #ff3419 100%);
}

/* Form styles */
.form-control, .form-select {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

/* Alert styles */
.alert {
  border-radius: 10px;
  border: none;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
}

.alert-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
}

/* Table styles */
.table {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  border: none;
}

.table tbody tr {
  transition: background-color 0.2s ease;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Footer styles */
.footer {
  background-color: #343a40;
  color: #ffffff;
  padding: 20px 0;
  margin-top: auto;
}

/* Utility classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-custom {
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Icon styles */
.icon-2x {
  font-size: 2rem;
}

.icon-3x {
  font-size: 3rem;
}

.text-gray-300 {
  color: #6c757d !important;
}

.text-gray-400 {
  color: #adb5bd !important;
}

.text-gray-500 {
  color: #6c757d !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  .card {
    margin-bottom: 20px;
  }

  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}



