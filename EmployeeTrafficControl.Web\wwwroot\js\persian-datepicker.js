/**
 * Persian DatePicker - تقویم شمسی
 */

// تبدیل تاریخ میلادی به شمسی
function gregorianToPersian(gDate) {
    if (!gDate) return '';
    
    const date = new Date(gDate);
    const gy = date.getFullYear();
    const gm = date.getMonth() + 1;
    const gd = date.getDate();
    
    const g_d_m = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];
    
    let jy = (gy <= 1600) ? 0 : 979;
    gy -= (gy <= 1600) ? 621 : 1600;
    
    let gy2 = (gm > 2) ? (gy + 1) : gy;
    let days = (365 * gy) + ((gy2 + 3) / 4) + g_d_m[gm - 1] + gd;
    
    if (gm > 2) {
        days += ((gy2 % 4 === 0) && ((gy2 % 100 !== 0) || (gy2 % 400 === 0))) ? 1 : 0;
    }
    
    jy += 33 * Math.floor(days / 12053);
    days %= 12053;
    
    jy += 4 * Math.floor(days / 1461);
    days %= 1461;
    
    if (days >= 366) {
        jy += Math.floor((days - 1) / 365);
        days = (days - 1) % 365;
    }
    
    let jp, jm, jd;
    if (days < 186) {
        jm = 1 + Math.floor(days / 31);
        jd = 1 + (days % 31);
    } else {
        jm = 7 + Math.floor((days - 186) / 30);
        jd = 1 + ((days - 186) % 30);
    }
    
    return `${jy}/${jm.toString().padStart(2, '0')}/${jd.toString().padStart(2, '0')}`;
}

// تبدیل تاریخ شمسی به میلادی
function persianToGregorian(pDate) {
    if (!pDate) return '';
    
    const parts = pDate.split('/');
    if (parts.length !== 3) return '';
    
    const jy = parseInt(parts[0]);
    const jm = parseInt(parts[1]);
    const jd = parseInt(parts[2]);
    
    if (jy < 1 || jy > 3178 || jm < 1 || jm > 12 || jd < 1 || jd > 31) return '';
    
    let gy = (jy <= 979) ? 1600 : 621;
    jy -= (jy <= 979) ? 0 : 979;
    
    let days = (365 * jy) + ((Math.floor(jy / 33)) * 8) + (Math.floor(((jy % 33) + 3) / 4));
    
    if (jm < 7) {
        days += (jm - 1) * 31;
    } else {
        days += ((jm - 7) * 30) + 186;
    }
    
    days += jd - 1;
    
    gy += 400 * Math.floor(days / 146097);
    days %= 146097;
    
    let leap = true;
    if (days >= 36525) {
        days--;
        gy += 100 * Math.floor(days / 36524);
        days %= 36524;
        if (days >= 365) days++;
        else leap = false;
    }
    
    gy += 4 * Math.floor(days / 1461);
    days %= 1461;
    
    if (days >= 366) {
        leap = false;
        days--;
        gy += Math.floor(days / 365);
        days = days % 365;
    }
    
    const sal_a = [0, 31, ((leap && (gy % 4 === 0) && ((gy % 100 !== 0) || (gy % 400 === 0))) ? 29 : 28), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    
    let gm = 0;
    while (gm < 13 && days >= sal_a[gm]) {
        days -= sal_a[gm];
        gm++;
    }
    
    if (gm > 12) {
        gy++;
        gm = 1;
    }
    
    const gd = days + 1;
    
    const date = new Date(gy, gm - 1, gd);
    return date.toISOString().split('T')[0];
}

// اعداد فارسی
const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

function toPersianNumbers(str) {
    return str.replace(/[0-9]/g, function(w) {
        return persianNumbers[+w];
    });
}

function toEnglishNumbers(str) {
    for (let i = 0; i < persianNumbers.length; i++) {
        str = str.replace(new RegExp(persianNumbers[i], 'g'), englishNumbers[i]);
    }
    return str;
}

// ایجاد تقویم شمسی
function createPersianDatePicker(input) {
    const wrapper = document.createElement('div');
    wrapper.className = 'persian-datepicker-wrapper position-relative';
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(input);
    
    const displayInput = document.createElement('input');
    displayInput.type = 'text';
    displayInput.className = input.className;
    displayInput.placeholder = 'مثال: ۱۴۰۳/۰۱/۰۱';
    displayInput.style.direction = 'rtl';
    
    input.style.display = 'none';
    wrapper.appendChild(displayInput);
    
    if (input.value) {
        displayInput.value = toPersianNumbers(gregorianToPersian(input.value));
    }
    
    displayInput.addEventListener('input', function() {
        let value = toEnglishNumbers(this.value);
        
        value = value.replace(/[^\d]/g, '');
        if (value.length >= 4) {
            value = value.substring(0, 4) + '/' + value.substring(4);
        }
        if (value.length >= 7) {
            value = value.substring(0, 7) + '/' + value.substring(7, 9);
        }
        
        this.value = toPersianNumbers(value);
        
        if (value.length === 10) {
            const gregorianDate = persianToGregorian(value);
            input.value = gregorianDate;
        } else {
            input.value = '';
        }
    });
    
    displayInput.addEventListener('blur', function() {
        const value = toEnglishNumbers(this.value);
        if (value && value.length === 10) {
            const parts = value.split('/');
            const year = parseInt(parts[0]);
            const month = parseInt(parts[1]);
            const day = parseInt(parts[2]);
            
            if (year < 1300 || year > 1500 || month < 1 || month > 12 || day < 1 || day > 31) {
                this.classList.add('is-invalid');
                input.value = '';
            } else {
                this.classList.remove('is-invalid');
            }
        }
    });
    
    if (input.required) displayInput.required = true;
    if (input.disabled) displayInput.disabled = true;
    if (input.name) displayInput.setAttribute('data-name', input.name);
}

// اعمال تقویم شمسی
function initPersianDatePickers() {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        if (!input.hasAttribute('data-persian-datepicker')) {
            createPersianDatePicker(input);
            input.setAttribute('data-persian-datepicker', 'true');
        }
    });
}

document.addEventListener('DOMContentLoaded', initPersianDatePickers);
window.initPersianDatePickers = initPersianDatePickers;
