using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    [AuthorizePermission("EDIT_EMPLOYEE")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class EditModel : PageModel
    {
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;
        private readonly JobService _jobService;
        private readonly ILogger<EditModel> _logger;

        public EditModel(EmployeeService employeeService, BuildingService buildingService, JobService jobService, ILogger<EditModel> logger)
        {
            _employeeService = employeeService;
            _buildingService = buildingService;
            _jobService = jobService;
            _logger = logger;
        }

        [BindProperty]
        public Employee Employee { get; set; } = default!;

        public SelectList Buildings { get; set; } = default!;
        public SelectList Jobs { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Employee = await _employeeService.GetEmployeeByIdAsync(id);

            if (Employee == null)
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            await LoadSelectLists();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            _logger.LogInformation("شروع OnPostAsync برای کارمند {EmployeeId}", Employee?.EmployeeId);
            _logger.LogInformation("وضعیت گواهینامه رانندگی: {HasDrivingLicense}", Employee?.HasDrivingLicense);

            // Remove validation errors for navigation properties
            ModelState.Remove("Employee.Building");
            ModelState.Remove("Employee.Job");

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("ModelState نامعتبر است. خطاها: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                await LoadSelectLists();
                return Page();
            }

            // Check if personnel code already exists (excluding current employee)
            bool personnelCodeExists = await _employeeService.EmployeeCodeExistsAsync(Employee.PersonnelCode, Employee.EmployeeId);
            if (personnelCodeExists)
            {
                ModelState.AddModelError("Employee.PersonnelCode", "کد پرسنلی وارد شده قبلاً ثبت شده است.");
                await LoadSelectLists();
                return Page();
            }

            try
            {
                _logger.LogInformation("فراخوانی UpdateEmployeeAsync برای کارمند {EmployeeId} با گواهینامه {HasDrivingLicense}",
                    Employee.EmployeeId, Employee.HasDrivingLicense);

                var result = await _employeeService.UpdateEmployeeAsync(Employee);

                if (result)
                {
                    _logger.LogInformation("به‌روزرسانی کارمند {EmployeeId} موفق بود", Employee.EmployeeId);
                    TempData["SuccessMessage"] = "اطلاعات کارمند با موفقیت به‌روزرسانی شد.";
                    return RedirectToPage("./Index");
                }
                else
                {
                    _logger.LogWarning("به‌روزرسانی کارمند {EmployeeId} ناموفق بود", Employee.EmployeeId);
                    ModelState.AddModelError(string.Empty, "خطا در به‌روزرسانی اطلاعات کارمند.");
                    await LoadSelectLists();
                    return Page();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در به‌روزرسانی کارمند {EmployeeId}", Employee.EmployeeId);
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var buildings = await _buildingService.GetAllBuildingsAsync();
            var jobs = await _jobService.GetAllJobsAsync();

            Buildings = new SelectList(buildings, "BuildingId", "Name");
            Jobs = new SelectList(jobs, "JobId", "Title");
        }
    }
}
