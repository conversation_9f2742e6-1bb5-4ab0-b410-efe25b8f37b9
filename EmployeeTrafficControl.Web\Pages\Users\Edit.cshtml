@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Users.EditModel
@{
    ViewData["Title"] = "ویرایش کاربر";
}

<div class="page-header">
    <h1>ویرایش کاربر</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <input type="hidden" asp-for="UserEdit.UserId" />

                    <div class="form-section">
                        <h5 class="border-bottom pb-2 mb-3">اطلاعات کاربری</h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="UserEdit.UserName" class="form-label">نام کاربری <span class="text-danger">*</span></label>
                                    <input asp-for="UserEdit.UserName" class="form-control" placeholder="نام کاربری منحصر به فرد" />
                                    <span asp-validation-for="UserEdit.UserName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="UserEdit.RoleId" class="form-label">نقش <span class="text-danger">*</span></label>
                                    <select asp-for="UserEdit.RoleId" asp-items="Model.Roles" class="form-select">
                                        <option value="">انتخاب کنید...</option>
                                    </select>
                                    <span asp-validation-for="UserEdit.RoleId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="password" class="form-label">رمز عبور جدید</label>
                                    <input type="password" id="password" name="Password" class="form-control" placeholder="در صورت عدم تغییر خالی بگذارید" />
                                    <div class="form-text">در صورت خالی گذاشتن، رمز عبور فعلی تغییر نخواهد کرد</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="UserEdit.IsActive" class="form-label">وضعیت</label>
                                    <div class="form-check form-switch">
                                        <input asp-for="UserEdit.IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="UserEdit.IsActive" class="form-check-label">فعال</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5 class="border-bottom pb-2 mb-3">اطلاعات تکمیلی</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="UserEdit.EmployeeId" class="form-label">کارمند مرتبط</label>
                                    <select asp-for="UserEdit.EmployeeId" class="form-select" asp-items="Model.Employees">
                                        <option value="">انتخاب کنید...</option>
                                    </select>
                                    <span asp-validation-for="UserEdit.EmployeeId" class="text-danger"></span>
                                    <div class="form-text">در صورت انتخاب، این کاربر به کارمند مشخص شده مرتبط می‌شود</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="UserEdit.BuildingId" class="form-label">ساختمان مرتبط</label>
                                    <select asp-for="UserEdit.BuildingId" class="form-select" asp-items="Model.Buildings">
                                        <option value="">همه ساختمان‌ها</option>
                                    </select>
                                    <span asp-validation-for="UserEdit.BuildingId" class="text-danger"></span>
                                    <div class="form-text">در صورت انتخاب، دسترسی کاربر به ساختمان مشخص محدود می‌شود</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره تغییرات
                        </button>
                        <a asp-page="Details" asp-route-id="@Model.UserEdit.UserId" class="btn btn-info">
                            <i class="bi bi-eye"></i> مشاهده جزئیات
                        </a>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">اطلاعات کاربر</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">شناسه:</dt>
                    <dd class="col-sm-7">@Model.UserEdit.UserId</dd>

                    <dt class="col-sm-5">نام کاربری:</dt>
                    <dd class="col-sm-7">
                        <strong>@Model.UserEdit.UserName</strong>
                    </dd>

                    <dt class="col-sm-5">نقش فعلی:</dt>
                    <dd class="col-sm-7">
                        @if (Model.UserEdit.Role?.Name == "ADMIN")
                        {
                            <span class="badge bg-danger">مدیر سیستم</span>
                        }
                        else if (Model.UserEdit.Role?.Name == "MANAGER")
                        {
                            <span class="badge bg-warning">مدیر ساختمان</span>
                        }
                        else if (Model.UserEdit.Role?.Name == "GUARD")
                        {
                            <span class="badge bg-warning">نگهبان</span>
                        }
                        else if (Model.UserEdit.Role?.Name == "USER")
                        {
                            <span class="badge bg-warning">کاربر عادی</span>
                        }

                        else
                        {
                            <span class="badge bg-info">@(Model.UserEdit.Role?.Name ?? "نامشخص")</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        نام کاربری باید منحصر به فرد باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        رمز عبور جدید حداقل 6 کاراکتر باشد
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
