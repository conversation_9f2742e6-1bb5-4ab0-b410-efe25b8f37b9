using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public enum EmployeeCurrentStatus
    {
        [Display(Name = "خارج از اداره")]
        OutOfOffice = 0,
        
        [Display(Name = "حضور در ساختمان")]
        PresentInBuilding = 1,
        
        [Display(Name = "خروج ساعتی")]
        HourlyExit = 2,
        
        [Display(Name = "ماموریت اداری")]
        OfficialMission = 3,
        
        [Display(Name = "مرخصی")]
        OnLeave = 4
    }

    public class EmployeeStatus
    {
        [Key]
        public int StatusId { get; set; }

        [Display(Name = "کارمند")]
        [Required]
        public int EmployeeId { get; set; }

        [Display(Name = "وضعیت فعلی")]
        [Required]
        public EmployeeCurrentStatus CurrentStatus { get; set; } = EmployeeCurrentStatus.OutOfOffice;

        [Display(Name = "تاریخ")]
        [Required]
        public DateTime Date { get; set; } = DateTime.Today;

        [Display(Name = "زمان ورود")]
        public DateTime? EntryTime { get; set; }

        [Display(Name = "زمان خروج نهایی")]
        public DateTime? FinalExitTime { get; set; }

        [Display(Name = "زمان آخرین به‌روزرسانی")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [Display(Name = "کاربر به‌روزرسانی‌کننده")]
        public int? UpdatedByUserId { get; set; }

        [Display(Name = "توضیحات")]
        [MaxLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "در خودرو")]
        public bool IsInVehicle { get; set; } = false;

        [Display(Name = "شناسه خودرو")]
        public int? CurrentVehicleId { get; set; }

        [Display(Name = "شماره برگه خروج")]
        [MaxLength(50)]
        public string? ExitPermitNumber { get; set; }

        // فیلدهای جدید برای بهبود منطق
        [Display(Name = "ورود اولیه انجام شده")]
        public bool HasInitialEntry { get; set; } = false;

        [Display(Name = "حاضر در ساختمان")]
        public bool IsPresentInBuilding { get; set; } = false;

        // Navigation properties
        [Display(Name = "کارمند")]
        public Employee Employee { get; set; } = default!;

        [Display(Name = "کاربر به‌روزرسانی‌کننده")]
        public ApplicationUser? UpdatedByUser { get; set; }

        [Display(Name = "خودرو فعلی")]
        public Car? CurrentVehicle { get; set; }

        // Helper methods - بهبود یافته
        public bool IsPresent()
        {
            return HasInitialEntry && IsPresentInBuilding;
        }

        public bool CanExitBuilding()
        {
            return HasInitialEntry && IsPresentInBuilding;
        }

        public bool CanEnterBuilding()
        {
            return !HasInitialEntry || (HasInitialEntry && !IsPresentInBuilding);
        }

        public bool CanUseVehicle()
        {
            return HasInitialEntry && IsPresentInBuilding;
        }

        public bool HasCompletedDay()
        {
            return HasInitialEntry && FinalExitTime.HasValue;
        }

        public string GetStatusDisplayName()
        {
            if (!HasInitialEntry)
                return "ورود نکرده";
            
            if (FinalExitTime.HasValue)
                return "خروج نهایی انجام شده";
            
            if (!IsPresentInBuilding)
                return "خارج از ساختمان";
            
            return CurrentStatus switch
            {
                EmployeeCurrentStatus.PresentInBuilding => "حضور در ساختمان",
                EmployeeCurrentStatus.HourlyExit => "خروج ساعتی",
                EmployeeCurrentStatus.OfficialMission => "ماموریت اداری",
                EmployeeCurrentStatus.OnLeave => "مرخصی",
                _ => "حضور در ساختمان"
            };
        }

        public string GetStatusBadgeClass()
        {
            if (!HasInitialEntry)
                return "bg-secondary";
            
            if (FinalExitTime.HasValue)
                return "bg-dark";
            
            if (!IsPresentInBuilding)
                return "bg-warning";
            
            return CurrentStatus switch
            {
                EmployeeCurrentStatus.PresentInBuilding => "bg-success",
                EmployeeCurrentStatus.HourlyExit => "bg-warning",
                EmployeeCurrentStatus.OfficialMission => "bg-info",
                EmployeeCurrentStatus.OnLeave => "bg-secondary",
                _ => "bg-success"
            };
        }

        // متدهای جدید برای مدیریت وضعیت
        public void RegisterInitialEntry()
        {
            HasInitialEntry = true;
            IsPresentInBuilding = true;
            EntryTime = DateTime.Now;
            CurrentStatus = EmployeeCurrentStatus.PresentInBuilding;
            LastUpdated = DateTime.Now;
        }

        public void RegisterBuildingExit()
        {
            IsPresentInBuilding = false;
            LastUpdated = DateTime.Now;
        }

        public void RegisterBuildingEntry()
        {
            IsPresentInBuilding = true;
            LastUpdated = DateTime.Now;
        }

        public void RegisterFinalExit()
        {
            FinalExitTime = DateTime.Now;
            IsPresentInBuilding = false;
            LastUpdated = DateTime.Now;
        }
    }
}
