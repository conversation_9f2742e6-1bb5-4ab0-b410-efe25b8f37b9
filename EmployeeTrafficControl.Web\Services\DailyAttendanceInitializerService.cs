using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using EmployeeTrafficControl.Services; // For DailyAttendanceService

namespace EmployeeTrafficControl.Web.Services
{
    public class DailyAttendanceInitializerService : IHostedService, IDisposable
    {
        private readonly ILogger<DailyAttendanceInitializerService> _logger;
        private readonly IServiceProvider _services;
        private Timer? _timer = null;

        public DailyAttendanceInitializerService(ILogger<DailyAttendanceInitializerService> logger, IServiceProvider services)
        {
            _logger = logger;
            _services = services;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Daily Attendance Initializer Service running.");

            // Calculate the delay until the next midnight
            var now = DateTime.Now;
            var nextMidnight = now.AddDays(1).Date;
            var initialDelay = nextMidnight - now;

            _timer = new Timer(DoWork, null, initialDelay, TimeSpan.FromDays(1)); // Run daily at midnight

            return Task.CompletedTask;
        }

        private async void DoWork(object? state)
        {
            _logger.LogInformation("Initializing daily attendance records...");

            using (var scope = _services.CreateScope())
            {
                var dailyAttendanceService = scope.ServiceProvider.GetRequiredService<DailyAttendanceService>();
                // Assuming a system user ID for automated tasks, or fetch a suitable one
                // For simplicity, let's use a placeholder ID (e.g., 1 for System User)
                // In a real application, you might have a dedicated system user or a more robust way to get the ID.
                int systemUserId = 1; 
                
                var success = await dailyAttendanceService.InitializeDailyAttendanceForEmployeesAsync(systemUserId);

                if (success)
                {
                    _logger.LogInformation("Daily attendance records initialized successfully.");
                }
                else
                {
                    _logger.LogError("Failed to initialize daily attendance records.");
                }
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Daily Attendance Initializer Service is stopping.");

            _timer?.Change(Timeout.Infinite, 0);

            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }
    }
}