using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Jobs
{
    [AuthorizePermission("VIEW_JOBS")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class DetailsModel : PageModel
    {
        private readonly JobService _jobService;
        private readonly EmployeeService _employeeService;

        public DetailsModel(JobService jobService, EmployeeService employeeService)
        {
            _jobService = jobService;
            _employeeService = employeeService;
        }

        public Job Job { get; set; } = default!;
        public IList<Employee> Employees { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Job = await _jobService.GetJobByIdAsync(id);

            if (Job == null)
            {
                TempData["ErrorMessage"] = "شغل مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Get employees with this job
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            Employees = allEmployees.Where(e => e.JobId == id).ToList();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var job = await _jobService.GetJobByIdAsync(id);
            if (job == null)
            {
                TempData["ErrorMessage"] = "شغل مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Check if there are employees with this job
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            var employeesWithJob = allEmployees.Where(e => e.JobId == id).ToList();

            if (employeesWithJob.Any())
            {
                TempData["ErrorMessage"] = $"امکان حذف این شغل وجود ندارد زیرا {employeesWithJob.Count} کارمند با این شغل وجود دارد.";
                return RedirectToPage("./Details", new { id });
            }

            try
            {
                await _jobService.DeleteJobAsync(id);
                TempData["SuccessMessage"] = "شغل با موفقیت حذف شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف شغل: " + ex.Message;
                return RedirectToPage("./Details", new { id });
            }
        }
    }
}
