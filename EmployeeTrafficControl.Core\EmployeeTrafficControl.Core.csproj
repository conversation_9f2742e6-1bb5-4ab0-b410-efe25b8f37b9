<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.6" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Interfaces\" />
    <Folder Include="DTOs\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EmployeeTrafficControl.Data\EmployeeTrafficControl.Data.csproj" />
  </ItemGroup>

</Project>
