using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class SystemSettings
    {
        [Key]
        public int SettingsId { get; set; }

        [Display(Name = "ساعت شروع کار (شنبه تا چهارشنبه)")]
        [Required(ErrorMessage = "ساعت شروع کار الزامی است.")]
        public TimeSpan WorkStartTime { get; set; } = new TimeSpan(7, 0, 0); // 07:00

        [Display(Name = "ساعت پایان کار (شنبه تا چهارشنبه)")]
        [Required(ErrorMessage = "ساعت پایان کار الزامی است.")]
        public TimeSpan WorkEndTime { get; set; } = new TimeSpan(14, 0, 0); // 14:00

        [Display(Name = "ساعت شروع کار پنج‌شنبه")]
        [Required(ErrorMessage = "ساعت شروع کار پنج‌شنبه الزامی است.")]
        public TimeSpan ThursdayWorkStartTime { get; set; } = new TimeSpan(7, 0, 0); // 07:00

        [Display(Name = "ساعت پایان کار پنج‌شنبه")]
        [Required(ErrorMessage = "ساعت پایان کار پنج‌شنبه الزامی است.")]
        public TimeSpan ThursdayWorkEndTime { get; set; } = new TimeSpan(13, 30, 0); // 13:30

        [Display(Name = "حداکثر تأخیر مجاز (دقیقه)")]
        [Range(0, 120, ErrorMessage = "حداکثر تأخیر باید بین 0 تا 120 دقیقه باشد.")]
        public int MaxLateMinutes { get; set; } = 15;

        [Display(Name = "حداکثر زودتر رفتن مجاز (دقیقه)")]
        [Range(0, 120, ErrorMessage = "حداکثر زودتر رفتن باید بین 0 تا 120 دقیقه باشد.")]
        public int MaxEarlyLeaveMinutes { get; set; } = 15;

        [Display(Name = "روزهای کاری")]
        [Required(ErrorMessage = "روزهای کاری الزامی است.")]
        [MaxLength(50)]
        public string WorkingDays { get; set; } = "Saturday,Sunday,Monday,Tuesday,Wednesday,Thursday"; // Default Persian work week

        [Display(Name = "نام سازمان")]
        [Required(ErrorMessage = "نام سازمان الزامی است.")]
        [MaxLength(200)]
        public string OrganizationName { get; set; } = "سازمان نمونه";

        [Display(Name = "آدرس سازمان")]
        [MaxLength(500)]
        public string? OrganizationAddress { get; set; }

        [Display(Name = "تلفن سازمان")]
        [MaxLength(20)]
        public string? OrganizationPhone { get; set; }

        [Display(Name = "ایمیل سازمان")]
        [MaxLength(100)]
        [EmailAddress(ErrorMessage = "فرمت ایمیل صحیح نیست.")]
        public string? OrganizationEmail { get; set; }

        [Display(Name = "فعال‌سازی ثبت خودکار ورود")]
        public bool AutoRegisterEntry { get; set; } = true;

        [Display(Name = "فعال‌سازی ثبت خودکار خروج")]
        public bool AutoRegisterExit { get; set; } = true;

        [Display(Name = "نیاز به تأیید برای خروج ساعتی")]
        public bool RequireApprovalForHourlyExit { get; set; } = false;

        [Display(Name = "حداکثر ساعت خروج ساعتی در روز")]
        [Range(1, 8, ErrorMessage = "حداکثر ساعت خروج ساعتی باید بین 1 تا 8 ساعت باشد.")]
        public int MaxHourlyExitHours { get; set; } = 4;

        [Display(Name = "تاریخ آخرین به‌روزرسانی")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [Display(Name = "کاربر آخرین به‌روزرسانی")]
        public int? LastUpdatedByUserId { get; set; }

        // Navigation property
        [Display(Name = "کاربر آخرین به‌روزرسانی")]
        public ApplicationUser? LastUpdatedByUser { get; set; }

        // Helper methods
        public bool IsWorkingDay(DayOfWeek dayOfWeek)
        {
            if (string.IsNullOrEmpty(WorkingDays))
                return false;

            var workingDaysList = WorkingDays.Split(',').Select(d => d.Trim()).ToList();
            return workingDaysList.Contains(dayOfWeek.ToString());
        }

        public bool IsWithinWorkingHours(TimeSpan time, DayOfWeek dayOfWeek = DayOfWeek.Saturday)
        {
            if (dayOfWeek == DayOfWeek.Thursday)
            {
                return time >= ThursdayWorkStartTime && time <= ThursdayWorkEndTime;
            }
            return time >= WorkStartTime && time <= WorkEndTime;
        }

        public bool IsLate(TimeSpan arrivalTime, DayOfWeek dayOfWeek = DayOfWeek.Saturday)
        {
            TimeSpan startTime = dayOfWeek == DayOfWeek.Thursday ? ThursdayWorkStartTime : WorkStartTime;
            var lateThreshold = startTime.Add(TimeSpan.FromMinutes(MaxLateMinutes));
            return arrivalTime > lateThreshold;
        }

        public bool IsEarlyLeave(TimeSpan leaveTime, DayOfWeek dayOfWeek = DayOfWeek.Saturday)
        {
            TimeSpan endTime = dayOfWeek == DayOfWeek.Thursday ? ThursdayWorkEndTime : WorkEndTime;
            var earlyLeaveThreshold = endTime.Subtract(TimeSpan.FromMinutes(MaxEarlyLeaveMinutes));
            return leaveTime < earlyLeaveThreshold;
        }

        public (TimeSpan startTime, TimeSpan endTime) GetWorkingHours(DayOfWeek dayOfWeek)
        {
            if (dayOfWeek == DayOfWeek.Thursday)
            {
                return (ThursdayWorkStartTime, ThursdayWorkEndTime);
            }
            return (WorkStartTime, WorkEndTime);
        }

        public string GetWorkingDaysDisplay()
        {
            if (string.IsNullOrEmpty(WorkingDays))
                return "تعریف نشده";

            var dayNames = new Dictionary<string, string>
            {
                {"Saturday", "شنبه"},
                {"Sunday", "یکشنبه"},
                {"Monday", "دوشنبه"},
                {"Tuesday", "سه‌شنبه"},
                {"Wednesday", "چهارشنبه"},
                {"Thursday", "پنج‌شنبه"},
                {"Friday", "جمعه"}
            };

            var workingDaysList = WorkingDays.Split(',').Select(d => d.Trim()).ToList();
            var persianDays = workingDaysList.Where(d => dayNames.ContainsKey(d))
                                           .Select(d => dayNames[d])
                                           .ToList();

            return string.Join("، ", persianDays);
        }
    }
}
