@page "/Account/Login"
@model EmployeeTrafficControl.Web.Pages.Account.LoginModel
@{
    ViewData["Title"] = "ورود به سیستم";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>ورود به سیستم</h2>
            <p class="text-muted">سیستم کنترل تردد کارمندان</p>
        </div>

        <form method="post" class="login-form">
            <div asp-validation-summary="All" class="text-danger mb-3"></div>

            <div class="form-group mb-3">
                <label asp-for="Input.Username" class="form-label">نام کاربری</label>
                <input asp-for="Input.Username" class="form-control" placeholder="نام کاربری خود را وارد کنید" />
                <span asp-validation-for="Input.Username" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Input.Password" class="form-label">رمز عبور</label>
                <input asp-for="Input.Password" class="form-control" placeholder="رمز عبور خود را وارد کنید" />
                <span asp-validation-for="Input.Password" class="text-danger"></span>
            </div>

            <div class="form-check mb-3">
                <input asp-for="Input.RememberMe" class="form-check-input" />
                <label asp-for="Input.RememberMe" class="form-check-label">
                    مرا به خاطر بسپار
                </label>
            </div>

            <button type="submit" class="btn btn-primary w-100">
                <i class="bi bi-box-arrow-in-right"></i>
                ورود
            </button>
        </form>

        @if (!string.IsNullOrEmpty(Model.ErrorMessage))
        {
            <div class="alert alert-danger mt-3">
                <i class="bi bi-exclamation-triangle"></i>
                @Model.ErrorMessage
                @if (Model.ErrorMessage.Contains("قفل"))
                {
                    <div class="mt-2">
                        <a href="/Account/UnlockAccount" class="btn btn-sm btn-outline-warning">
                            <i class="bi bi-unlock"></i>
                            بازگشایی حساب کاربری
                        </a>
                    </div>
                }
            </div>
        }
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('input[name="Input.Username"]')?.focus();
        });
    </script>
}
