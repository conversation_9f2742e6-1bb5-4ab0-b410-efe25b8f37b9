using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Services
{
    /// <summary>
    /// سرویس مدیریت برنامه کاری روزانه
    /// </summary>
    public class DailyWorkScheduleService
    {
        private readonly ApplicationDbContext _context;

        public DailyWorkScheduleService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// دریافت تمام برنامه‌های کاری روزانه
        /// </summary>
        public async Task<List<DailyWorkSchedule>> GetAllSchedulesAsync()
        {
            return await _context.DailyWorkSchedules
                                .Include(s => s.LastUpdatedByUser)
                                .OrderBy(s => s.DayOfWeek)
                                .ToListAsync();
        }

        /// <summary>
        /// دریافت برنامه کاری برای روز خاص
        /// </summary>
        public async Task<DailyWorkSchedule?> GetScheduleByDayAsync(DayOfWeek dayOfWeek)
        {
            return await _context.DailyWorkSchedules
                                .Include(s => s.LastUpdatedByUser)
                                .FirstOrDefaultAsync(s => s.DayOfWeek == dayOfWeek);
        }

        /// <summary>
        /// دریافت برنامه کاری برای امروز
        /// </summary>
        public async Task<DailyWorkSchedule?> GetTodayScheduleAsync()
        {
            return await GetScheduleByDayAsync(DateTime.Today.DayOfWeek);
        }

        /// <summary>
        /// ایجاد یا به‌روزرسانی برنامه کاری روز
        /// </summary>
        public async Task<bool> CreateOrUpdateScheduleAsync(DailyWorkSchedule schedule, int userId)
        {
            try
            {
                var existing = await _context.DailyWorkSchedules
                                           .FirstOrDefaultAsync(s => s.DayOfWeek == schedule.DayOfWeek);

                if (existing != null)
                {
                    // به‌روزرسانی
                    existing.DayNamePersian = schedule.DayNamePersian;
                    existing.IsWorkingDay = schedule.IsWorkingDay;
                    existing.WorkStartTime = schedule.WorkStartTime;
                    existing.WorkEndTime = schedule.WorkEndTime;
                    existing.MaxLateMinutes = schedule.MaxLateMinutes;
                    existing.MaxEarlyLeaveMinutes = schedule.MaxEarlyLeaveMinutes;
                    existing.LastUpdated = DateTime.Now;
                    existing.LastUpdatedByUserId = userId;
                }
                else
                {
                    // ایجاد جدید
                    schedule.LastUpdated = DateTime.Now;
                    schedule.LastUpdatedByUserId = userId;
                    schedule.DayNamePersian = DailyWorkSchedule.GetPersianDayName(schedule.DayOfWeek);
                    _context.DailyWorkSchedules.Add(schedule);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating/updating schedule: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ایجاد برنامه‌های کاری پیش‌فرض برای تمام روزهای هفته
        /// </summary>
        public async Task<bool> CreateDefaultSchedulesAsync(int userId)
        {
            try
            {
                var existingCount = await _context.DailyWorkSchedules.CountAsync();
                if (existingCount > 0)
                {
                    return true; // قبلاً ایجاد شده
                }

                var schedules = new List<DailyWorkSchedule>();

                // شنبه تا چهارشنبه: 7:00 - 14:00
                var regularWorkDays = new[] { DayOfWeek.Saturday, DayOfWeek.Sunday, DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday };
                foreach (var day in regularWorkDays)
                {
                    schedules.Add(new DailyWorkSchedule
                    {
                        DayOfWeek = day,
                        DayNamePersian = DailyWorkSchedule.GetPersianDayName(day),
                        IsWorkingDay = true,
                        WorkStartTime = new TimeSpan(7, 0, 0),   // 7:00
                        WorkEndTime = new TimeSpan(14, 0, 0),    // 14:00
                        MaxLateMinutes = 15,
                        MaxEarlyLeaveMinutes = 15,
                        LastUpdated = DateTime.Now,
                        LastUpdatedByUserId = userId
                    });
                }

                // پنج‌شنبه: 7:00 - 13:30 (متفاوت از سایر روزها)
                schedules.Add(new DailyWorkSchedule
                {
                    DayOfWeek = DayOfWeek.Thursday,
                    DayNamePersian = DailyWorkSchedule.GetPersianDayName(DayOfWeek.Thursday),
                    IsWorkingDay = true,
                    WorkStartTime = new TimeSpan(7, 0, 0),   // 7:00
                    WorkEndTime = new TimeSpan(13, 30, 0),   // 13:30
                    MaxLateMinutes = 15,
                    MaxEarlyLeaveMinutes = 15,
                    LastUpdated = DateTime.Now,
                    LastUpdatedByUserId = userId
                });

                // جمعه: تعطیل
                schedules.Add(new DailyWorkSchedule
                {
                    DayOfWeek = DayOfWeek.Friday,
                    DayNamePersian = DailyWorkSchedule.GetPersianDayName(DayOfWeek.Friday),
                    IsWorkingDay = false,
                    WorkStartTime = null,
                    WorkEndTime = null,
                    MaxLateMinutes = 0,
                    MaxEarlyLeaveMinutes = 0,
                    LastUpdated = DateTime.Now,
                    LastUpdatedByUserId = userId
                });

                _context.DailyWorkSchedules.AddRange(schedules);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating default schedules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// بررسی اینکه آیا روز مشخص شده کاری است
        /// </summary>
        public async Task<bool> IsWorkingDayAsync(DayOfWeek dayOfWeek)
        {
            var schedule = await GetScheduleByDayAsync(dayOfWeek);
            return schedule?.IsWorkingDay ?? false;
        }

        /// <summary>
        /// بررسی اینکه آیا امروز روز کاری است
        /// </summary>
        public async Task<bool> IsTodayWorkingDayAsync()
        {
            return await IsWorkingDayAsync(DateTime.Today.DayOfWeek);
        }

        /// <summary>
        /// دریافت ساعات کاری برای روز مشخص
        /// </summary>
        public async Task<(TimeSpan? startTime, TimeSpan? endTime)> GetWorkingHoursAsync(DayOfWeek dayOfWeek)
        {
            var schedule = await GetScheduleByDayAsync(dayOfWeek);
            if (schedule?.IsWorkingDay == true)
            {
                return (schedule.WorkStartTime, schedule.WorkEndTime);
            }
            return (null, null);
        }

        /// <summary>
        /// محاسبه دقایق تاخیر برای روز مشخص
        /// </summary>
        public async Task<int> CalculateLateMinutesAsync(DayOfWeek dayOfWeek, TimeSpan arrivalTime)
        {
            var schedule = await GetScheduleByDayAsync(dayOfWeek);
            return schedule?.CalculateLateMinutes(arrivalTime) ?? 0;
        }

        /// <summary>
        /// محاسبه دقایق خروج زودهنگام برای روز مشخص
        /// </summary>
        public async Task<int> CalculateEarlyLeaveMinutesAsync(DayOfWeek dayOfWeek, TimeSpan departureTime)
        {
            var schedule = await GetScheduleByDayAsync(dayOfWeek);
            return schedule?.CalculateEarlyLeaveMinutes(departureTime) ?? 0;
        }

        /// <summary>
        /// بررسی اینکه آیا ساعت مشخص در بازه کاری است
        /// </summary>
        public async Task<bool> IsWithinWorkingHoursAsync(DayOfWeek dayOfWeek, TimeSpan time)
        {
            var schedule = await GetScheduleByDayAsync(dayOfWeek);
            return schedule?.IsWithinWorkingHours(time) ?? false;
        }
    }
}
