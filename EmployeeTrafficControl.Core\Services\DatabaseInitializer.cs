using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeTrafficControl.Services
{
    public class DatabaseInitializer
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole<int>> _roleManager;
        private readonly ILogger<DatabaseInitializer> _logger;

        public DatabaseInitializer(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<int>> roleManager,
            ILogger<DatabaseInitializer> logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Ensuring database exists and is up to date...");
                
                // Apply any pending migrations
                await _context.Database.MigrateAsync();

                // Create default roles if they don't exist
                await EnsureRolesAsync();

                // Create default admin user if it doesn't exist
                await EnsureAdminUserAsync();

                _logger.LogInformation("Database initialization completed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while initializing the database");
                throw;
            }
        }

        public async Task CreateSampleDataAsync()
        {
            try
            {
                // Create sample buildings
                if (!await _context.Buildings.AnyAsync())
                {
                    var buildings = new[]
                    {
                        new Building { Name = "ساختمان مرکزی", Address = "خیابان اصلی، پلاک 1" },
                        new Building { Name = "ساختمان اداری", Address = "خیابان فرعی، پلاک 2" }
                    };
                    await _context.Buildings.AddRangeAsync(buildings);
                    await _context.SaveChangesAsync();

                    // Create sample jobs (after buildings are saved to get their IDs)
                    if (!await _context.Jobs.AnyAsync())
                    {
                        var mainBuilding = await _context.Buildings.FirstAsync();
                        var jobs = new[]
                        {
                            new Job { Title = "مدیر", BuildingId = mainBuilding.BuildingId },
                            new Job { Title = "کارمند", BuildingId = mainBuilding.BuildingId },
                            new Job { Title = "راننده", BuildingId = mainBuilding.BuildingId }
                        };
                        await _context.Jobs.AddRangeAsync(jobs);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Sample data created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while creating sample data");
                throw;
            }
        }

        private async Task EnsureRolesAsync()
        {
            var roles = new[] { "Admin", "Manager", "Guard", "User" };

            foreach (var roleName in roles)
            {
                if (!await _roleManager.RoleExistsAsync(roleName))
                {
                    await _roleManager.CreateAsync(new IdentityRole<int>(roleName));
                    _logger.LogInformation("Created role: {RoleName}", roleName);
                }
            }
        }

        private async Task EnsureAdminUserAsync()
        {
            const string adminUserName = "admin";
            const string adminEmail = "<EMAIL>";
            const string adminPassword = "Admin123!@#";

            var adminUser = await _userManager.FindByNameAsync(adminUserName);

            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminUserName,
                    Email = adminEmail,
                    EmailConfirmed = true,
                    IsActive = true,
                    FullName = "System Administrator"
                };

                var result = await _userManager.CreateAsync(adminUser, adminPassword);

                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                    _logger.LogInformation("Created admin user: {UserName}", adminUserName);
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    throw new Exception($"Failed to create admin user. Errors: {errors}");
                }
            }
        }
    }
}
