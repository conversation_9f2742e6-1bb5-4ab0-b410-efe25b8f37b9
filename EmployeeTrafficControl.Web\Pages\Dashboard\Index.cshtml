@page "/dashboard"
@model EmployeeTrafficControl.Web.Pages.Dashboard.IndexModel
@{
    ViewData["Title"] = "داشبورد";
}

<div class="dashboard-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="dashboard-title">
                <i class="bi bi-speedometer2"></i>
                داشبورد سیستم کنترل تردد
            </h1>
            <p class="dashboard-subtitle">
                خوش آمدید، @Model.CurrentUser.UserName
                <span class="badge bg-primary ms-2">@Model.GetUserRoleDisplay()</span>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <div class="dashboard-date">
                <i class="bi bi-calendar3"></i>
                @DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"))
                <br>
                <small class="text-muted">@DateTime.Now.ToString("HH:mm")</small>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <h1 class="h3 mb-4">داشبورد مدیریت</h1>
    
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                ساختمان‌ها</div>
                            <div class="h5 mb-0 font-weight-bold">@Model.TotalBuildings</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-building icon-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                کارمندان</div>
                            <div class="h5 mb-0 font-weight-bold">@Model.TotalEmployees</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people icon-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                مشاغل</div>
                            <div class="h5 mb-0 font-weight-bold">@Model.TotalJobs</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-briefcase icon-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                خودروها</div>
                            <div class="h5 mb-0 font-weight-bold">@Model.TotalCars</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-car-front icon-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

   
</div>

<!-- آمار کلی -->
<div class="row mb-4">
    <div class="col-md-3">
        <a href="/reports/presentemployees" class="text-decoration-none">
            <div class="stats-card stats-present">
                <div class="stats-icon">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="stats-content">
                    <h3>@Model.AttendanceStats.PresentEmployees</h3>
                    <p>کارمندان حاضر</p>
                    <small>از @Model.AttendanceStats.TotalEmployees نفر</small>
                </div>
            </div>
        </a>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card stats-absent">
            <div class="stats-icon">
                <i class="bi bi-person-x"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.AttendanceStats.AbsentEmployees</h3>
                <p>کارمندان غایب</p>
                <small>@Model.AttendanceStats.AttendanceRate.ToString("F1")% حضور</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card stats-hourly-exit">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeesOutOfBuilding.Count</h3>
                <p>خروج ساعتی</p>
                <small>در حال حاضر</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card stats-late">
            <div class="stats-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.AttendanceStats.LateEmployees</h3>
                <p>تأخیر داشته‌اند</p>
                <small>@Model.AttendanceStats.LateRate.ToString("F1")% از حاضرین</small>
            </div>
        </div>
    </div>
</div>

<!-- Search Card -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-search"></i> جستجوی هوشمند و ثبت تردد
        </h6>
    </div>
    <div class="card-body">
        <form method="post" asp-page-handler="Search">
            <div class="input-group mb-3">
                <input type="text" asp-for="SearchTerm" class="form-control" placeholder="نام خودرو، پلاک، نام کارمند یا کد پرسنلی را وارد کنید..." />
                <button class="btn btn-primary" type="submit"><i class="bi bi-search"></i> جستجو</button>
            </div>
        </form>

        <!-- Results Area -->
        @if (Model.SearchResult != null)
        {
            <!-- Car Result -->
            @if (Model.SearchResult.Car != null)
            {
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-info">نتیجه جستجوی خودرو</h6>
                    </div>
                    <div class="card-body">
                        <h4>@Model.SearchResult.Car.Model - @Model.SearchResult.Car.PlateNumber</h4>
                        @if (Model.SearchResult.Car.IsInParking)
                        {
                            <p>وضعیت: <span class="badge bg-success">در پارکینگ</span></p>
                            <hr />
                            <h5>ثبت خروج خودرو</h5>
                            <form method="post" asp-page-handler="RegisterCarExit">
                                <input type="hidden" name="carId" value="@Model.SearchResult.Car.CarId" />
                                <div class="mb-3">
                                    <label class="form-label">راننده</label>
                                    <select name="driverId" class="form-control" id="driverSelect">
                                    @foreach (var driver in Model.AvailableDrivers)
                                    {
                                    <option value="@driver.EmployeeId">@driver.FirstName @driver.LastName</option>
                                    }
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        سرنشینان (ظرفیت: <span id="capacity">@Model.SearchResult.Car.PassengerCapacity</span> نفر) - 
                                        انتخاب شده: <span id="selectedCount">0</span>
                                    </label>
                                    <select name="passengerIds" class="form-control" multiple id="passengerSelect">
                                        @foreach (var employee in Model.AvailableEmployees)
                                        {
                                            <option value="@employee.EmployeeId">@employee.FirstName @employee.LastName</option>
                                        }
                                    </select>
                                    <div id="passengerWarning" class="text-danger mt-1" style="display: none;">
                                        تعداد سرنشینان انتخاب شده بیش از ظرفیت خودرو است!
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">توضیحات</label>
                                    <textarea name="notes" class="form-control"></textarea>
                                </div>
                                <button type="submit" class="btn btn-danger">ثبت خروج</button>
                            </form>
                        }
                        else
                        {
                            <p>وضعیت: <span class="badge bg-warning">خارج از پارکینگ</span></p>
                            <hr />
                            <h5>ثبت ورود خودرو</h5>
                            <form method="post" asp-page-handler="RegisterCarEntry">
                                <input type="hidden" name="carId" value="@Model.SearchResult.Car.CarId" />
                                @if (Model.LastCarExitLog != null)
                                {
                                    <p>راننده: @Model.LastCarExitLog.DriverEmployee.FirstName @Model.LastCarExitLog.DriverEmployee.LastName</p>
                                    <h5>سرنشینان</h5>
                                    @foreach (var passenger in Model.LastCarExitLog.CarPassengers)
                                    {
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="passengerIds" value="@passenger.EmployeeId" checked>
                                            <label class="form-check-label">
                                                @passenger.Employee.FirstName @passenger.Employee.LastName
                                            </label>
                                        </div>
                                    }
                                }
                                <button type="submit" class="btn btn-success mt-3">ثبت ورود</button>
                            </form>
                        }
                    </div>
                </div>
            }

            <!-- Employee Result -->
            @if (Model.SearchResult.Employee != null)
            {
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-info">نتیجه جستجوی کارمند</h6>
                    </div>
                    <div class="card-body">
                        <h4>@Model.SearchResult.Employee.FirstName @Model.SearchResult.Employee.LastName</h4>
                        <p>کد پرسنلی: @Model.SearchResult.Employee.PersonnelCode</p>
                        @if (Model.SearchResult.EmployeeStatus?.IsPresentInBuilding ?? false)
                        {
                            <p>وضعیت: <span class="badge bg-success">حاضر در ساختمان</span></p>
                            <hr />
                            <h5>ثبت خروج فردی</h5>
                            <form method="post" asp-page-handler="RegisterIndividualExit">
                                <input type="hidden" name="employeeId" value="@Model.SearchResult.Employee.EmployeeId" />
                                <button type="submit" class="btn btn-warning">ثبت خروج ساعتی</button>
                            </form>
                            <form method="post" asp-page-handler="RegisterOfficialMission" class="d-inline ms-2">
                                <input type="hidden" name="employeeId" value="@Model.SearchResult.Employee.EmployeeId" />
                                <button type="submit" class="btn btn-primary">ثبت ماموریت اداری</button>
                            </form>
                        }
                        else
                        {
                            <p>وضعیت: <span class="badge bg-secondary">خارج از ساختمان</span></p>
                            <hr />
                            <h5>ثبت ورود فردی</h5>
                            <form method="post" asp-page-handler="RegisterIndividualEntry">
                                <input type="hidden" name="employeeId" value="@Model.SearchResult.Employee.EmployeeId" />
                                <button type="submit" class="btn btn-info">ثبت ورود</button>
                            </form>
                        }
                    </div>
                </div>
            }

            @if (Model.SearchResult.Car == null && Model.SearchResult.Employee == null)
            {
                <div class="alert alert-warning">موردی با عبارت جستجو شده یافت نشد.</div>
            }
        }
    </div>
</div>

<!-- عملیات سریع -->
@* <div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0" style="color:black">
                    <i class="bi bi-lightning"></i> عملیات سریع
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <!-- ستون 1: عملیات روزانه -->
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-operations-column">
                            <div class="column-header">
                                <i class="bi bi-calendar-day text-success"></i>
                                <h6 class="mb-0">عملیات روزانه</h6>
                            </div>
                            <div class="column-content">
                                <a href="/traffic/dailyentry" class="quick-action-btn daily-entry">
                                    <i class="bi bi-building"></i>
                                    <span>ورود اولیه</span>
                                </a>
                                <a href="/traffic/finalexit" class="quick-action-btn final-exit">
                                    <i class="bi bi-door-open"></i>
                                    <span>خروج نهایی</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- ستون 2: عملیات ساعتی -->
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-operations-column">
                            <div class="column-header">
                                <i class="bi bi-clock-history text-warning"></i>
                                <h6 class="mb-0">عملیات ساعتی</h6>
                            </div>
                            <div class="column-content">
                                <a href="/traffic/individualexit" class="quick-action-btn hourly-exit">
                                    <i class="bi bi-clock"></i>
                                    <span>خروج ساعتی/ماموریت اداری</span>
                                </a>
                               
                                <a href="/traffic/individualEntry" class="quick-action-btn return-entry">
                                    <i class="bi bi-arrow-return-left"></i>
                                    <span>ورود بازگشت</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- ستون 3: عملیات خودرو -->
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-operations-column">
                            <div class="column-header">
                                <i class="bi bi-car-front text-primary"></i>
                                <h6 class="mb-0">عملیات خودرو</h6>
                            </div>
                            <div class="column-content">
                                <a href="/traffic/carexitnew" class="quick-action-btn vehicle-exit">
                                    <i class="bi bi-car-front"></i>
                                    <span>خروج خودرو</span>
                                </a>

                                <a href="/traffic/carentrynew" class="quick-action-btn vehicle-entry">
                                    <i class="bi bi-car-front-fill"></i>
                                    <span>ورود خودرو</span>
                                </a>
                               
                            </div>
                        </div>
                    </div>
 
                    <!-- ستون 4: گزارشات و تردد -->
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-operations-column">
                            <div class="column-header">
                                <i class="bi bi-graph-up text-info"></i>
                                <h6 class="mb-0">گزارشات و تردد</h6>
                            </div>
                            <div class="column-content">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <a href="/reports/presentemployees" class="quick-action-btn present-report">
                                            <i class="bi bi-people"></i>
                                            <span>کارمندان حاضر</span>
                                        </a>
                                    </div>
                                    <div class="col-6">
                                        <a href="/reports/outofbuilding" class="quick-action-btn out-report">
                                            <i class="bi bi-person-dash"></i>
                                            <span>خارج از ساختمان</span>
                                        </a>
                                    </div>
                                    <div class="col-6">
                                        <a href="/reports/carstatus" class="quick-action-btn vehicle-status">
                                            <i class="bi bi-car-front-fill"></i>
                                            <span>وضعیت خودروها</span>
                                        </a>
                                    </div>
                                    <div class="col-6">
                                        <a href="/reports/dailyattendance" class="quick-action-btn attendance-report">
                                            <i class="bi bi-file-earmark-text"></i>
                                            <span>گزارش حضور</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> *@

<!-- کارمندان حاضر و خارج از ساختمان -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" style="color:black">
                    <i class="bi bi-people"></i> کارمندان حاضر در ساختمان
                </h5>
                <span class="badge bg-success">@Model.PresentEmployees.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.PresentEmployees.Any())
                {
                    <div class="employee-list">
                        @foreach (var employee in Model.PresentEmployees.Take(10))
                        {
                            <div class="employee-item">
                                <div class="employee-info">
                                    <strong>@employee.Employee.FirstName @employee.Employee.LastName</strong>
                                    <small class="text-muted d-block">@employee.Employee.PersonnelCode</small>
                                    @if (employee.Employee.Job != null)
                                    {
                                        <small class="text-info d-block">@employee.Employee.Job.Title</small>
                                    }
                                </div>
                                <div class="employee-status">
                                    <form method="post" asp-page-handler="RegisterQuickHourlyExit" class="d-inline">
                                        <input type="hidden" name="employeeId" value="@employee.EmployeeId" />
                                        <button type="submit" class="btn btn-sm btn-outline-warning" title="ثبت خروج ساعتی"><i class="bi bi-clock"></i></button>
                                    </form>
                                    <form method="post" asp-page-handler="RegisterQuickMission" class="d-inline ms-1">
                                        <input type="hidden" name="employeeId" value="@employee.EmployeeId" />
                                        <button type="submit" class="btn btn-sm btn-outline-primary" title="ثبت ماموریت"><i class="bi bi-briefcase"></i></button>
                                    </form>
                                </div>
                            </div>
                        }
                        @if (Model.PresentEmployees.Count > 10)
                        {
                            <div class="text-center mt-3">
                                <a href="/reports/present-employees" class="btn btn-sm btn-outline-primary">
                                    مشاهده همه (@Model.PresentEmployees.Count نفر)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-person-slash" style="font-size: 2rem;"></i>
                        <p class="mt-2">هیچ کارمندی در حال حاضر در ساختمان نیست</p>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" style="color:black">
                    <i class="bi bi-clock-history"></i> کارمندان خارج از ساختمان
                </h5>
                <span class="badge bg-warning">@Model.EmployeesOutOfBuilding.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.EmployeesOutOfBuilding.Any())
                {
                    <div class="employee-list">
                        @foreach (var employee in Model.EmployeesOutOfBuilding.Take(10))
                        {
                            <div class="employee-item">
                                <div class="employee-info">
                                    <strong>@employee.Employee.FirstName @employee.Employee.LastName</strong>
                                    <small class="text-muted d-block">@employee.Employee.PersonnelCode</small>
                                    @if (!string.IsNullOrEmpty(employee.ExitPermitNumber))
                                    {
                                        <small class="text-info d-block">برگه: @employee.ExitPermitNumber</small>
                                    }
                                    @if (employee.IsInVehicle && employee.CurrentVehicle != null)
                                    {
                                        <small class="text-warning d-block">
                                            <i class="bi bi-car-front"></i> خودرو: @employee.CurrentVehicle.Model
                                        </small>
                                    }
                                    @if (!string.IsNullOrEmpty(employee.Notes))
                                    {
                                        <small class="text-secondary d-block">@employee.Notes</small>
                                    }
                                </div>
                                <div class="employee-status">
                                    <form method="post" asp-page-handler="RegisterQuickEntry" class="d-inline">
                                        <input type="hidden" name="employeeId" value="@employee.EmployeeId" />
                                        <button type="submit" class="btn btn-sm btn-outline-success" title="ثبت ورود"><i class="bi bi-arrow-return-left"></i></button>
                                    </form>
                                </div>
                            </div>
                        }
                        @if (Model.EmployeesOutOfBuilding.Count > 10)
                        {
                            <div class="text-center mt-3">
                                <a href="/reports/out-of-building" class="btn btn-sm btn-outline-warning">
                                    مشاهده همه (@Model.EmployeesOutOfBuilding.Count نفر)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        @if (Model.HasAnyEmployeeEnteredToday)
                        {
                            <p class="mt-2">همه کارمندان در ساختمان حضور دارند</p>
                        }
                        else
                        {
                            <p class="mt-2">هنوز هیچ کارمندی ورود اولیه ثبت نکرده است</p>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- خودروهای داخل و خارج پارکینگ -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" style="color:black">
                    <i class="bi bi-car-front-fill"></i> خودروهای داخل پارکینگ
                </h5>
                <span class="badge bg-success">@Model.CarsInParking.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.CarsInParking.Any())
                {
                    <div class="car-list">
                        @foreach (var car in Model.CarsInParking.Take(8))
                        {
                            <div class="car-item">
                                <div class="car-info">
                                    <strong>@car.PlateNumber</strong>
                                    <small class="text-muted d-block">@car.Model - @car.Type</small>
                                    @if (car.IsMoneyTransport)
                                    {
                                        <small class="text-warning d-block">
                                            <i class="bi bi-shield-check"></i> پولرسان
                                        </small>
                                    }
                                </div>
                                <div class="car-status">
                                    <span class="badge bg-success">
                                        <i class="bi bi-car-front-fill"></i>
                                        @(car.IsInParking ? "در پارکینگ" : "خارج از پارکینگ")
                                    </span>
                                    <small class="text-muted d-block">ظرفیت: @car.PassengerCapacity نفر</small>
                                    <button type="button" class="btn btn-sm btn-warning mt-2"
                                            onclick="showCarExitModal(@car.CarId, '@car.PlateNumber', '@car.Model', @car.PassengerCapacity, @car.IsMoneyTransport.ToString().ToLower())"
                                            title="ثبت خروج خودرو">
                                        <i class="bi bi-box-arrow-right"></i> خروج
                                    </button>
                                </div>
                            </div>
                        }
                        @if (Model.CarsInParking.Count > 8)
                        {
                            <div class="text-center mt-3">
                                <a href="/traffic/car-exit" class="btn btn-sm btn-outline-success">
                                    مشاهده همه (@Model.CarsInParking.Count خودرو)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-car-front" style="font-size: 2rem;"></i>
                        @if (Model.CarsOutOfParking.Any())
                        {
                            <p class="mt-2">همه خودروها خارج از پارکینگ هستند</p>
                        }
                        else
                        {
                            <p class="mt-2">هیچ تردد خودرویی ثبت نشده است</p>
                        }
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" style="color:black">
                    <i class="bi bi-car-front"></i> خودروهای خارج از پارکینگ
                </h5>
                <span class="badge bg-secondary">@Model.CarsOutOfParking.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.CarsOutOfParking.Any())
                {
                    <div class="car-list">
                        @foreach (var car in Model.CarsOutOfParking.Take(8))
                        {
                            <div class="car-item">
                                <div class="car-info">
                                    <strong>@car.Car.PlateNumber</strong>
                                    <small class="text-muted d-block">
                                        <a href="#" class="text-decoration-none" onclick="showCarPassengers(@car.CarTrafficLogId)">
                                            @car.Car.Model - @car.Car.Type
                                        </a>
                                    </small>
                                    @if (car.Car.IsMoneyTransport)
                                    {
                                        <small class="text-warning d-block">
                                            <i class="bi bi-shield-check"></i> پولرسان
                                        </small>
                                    }
                                </div>
                                <div class="car-status">
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-car-front"></i>
                                        @car.CurrentStatus
                                    </span>
                                    @{
                                        var currentPassengers = car.CarPassengers.Count(cp => !cp.IsEntered);
                                        if (car.DriverEmployeeId > 0) currentPassengers++; // اضافه کردن راننده
                                    }
                                    <small class="text-muted d-block">سرنشینان: @currentPassengers نفر</small>
                                </div>
                            </div>
                        }
                        @if (Model.CarsOutOfParking.Count > 8)
                        {
                            <div class="text-center mt-3">
                                <a href="/reports/car-status" class="btn btn-sm btn-outline-secondary">
                                    مشاهده همه (@Model.CarsOutOfParking.Count خودرو)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        @if (Model.CarsInParking.Any())
                        {
                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                            <p class="mt-2">همه خودروها در پارکینگ هستند</p>
                        }
                        else
                        {
                            <i class="bi bi-car-front" style="font-size: 2rem;"></i>
                            <p class="mt-2">هیچ تردد خودرویی ثبت نشده است</p>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- مودال نمایش سرنشینان خودرو -->
<div class="modal fade" id="carPassengersModal" tabindex="-1" aria-labelledby="carPassengersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="carPassengersModalLabel">
                    <i class="bi bi-people-fill"></i> سرنشینان خودرو
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="carPassengersContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">در حال بارگذاری...</span>
                        </div>
                        <p class="mt-2">در حال بارگذاری اطلاعات...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال ثبت خروج خودرو -->
<div class="modal fade" id="carExitModal" tabindex="-1" aria-labelledby="carExitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="carExitModalLabel">
                    <i class="bi bi-box-arrow-right"></i> ثبت خروج خودرو
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="carExitForm">
                    <input type="hidden" id="modalCarId" name="carId" />

                    <!-- اطلاعات خودرو -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="bi bi-car-front"></i> اطلاعات خودرو
                            </h6>
                            <p class="mb-1"><strong>پلاک:</strong> <span id="modalCarPlate"></span></p>
                            <p class="mb-1"><strong>مدل:</strong> <span id="modalCarModel"></span></p>
                            <p class="mb-0"><strong>ظرفیت:</strong> <span id="modalCarCapacity"></span> نفر</p>
                        </div>
                    </div>

                    <!-- انتخاب راننده -->
                    <div class="mb-3">
                        <label for="modalDriverSelect" class="form-label">
                            <i class="bi bi-person-gear"></i> انتخاب راننده <span class="text-danger">*</span>
                        </label>
                        <select id="modalDriverSelect" name="driverId" class="form-select" required>
                            <option value="">انتخاب کنید...</option>
                        </select>
                        <div class="form-text">فقط کارمندان دارای گواهینامه یا شغل راننده نمایش داده می‌شوند</div>
                    </div>

                    <!-- انتخاب سرنشینان -->
                    <div class="mb-3">
                        <label for="modalPassengerSelect" class="form-label">
                            <i class="bi bi-people"></i> انتخاب سرنشینان
                            <small class="text-muted">(انتخاب شده: <span id="modalSelectedCount">0</span>)</small>
                        </label>
                        <select id="modalPassengerSelect" name="passengerIds" class="form-select" multiple size="6">
                        </select>
                        <div id="modalPassengerWarning" class="alert alert-warning mt-2" style="display: none;">
                            <i class="bi bi-exclamation-triangle"></i>
                            تعداد سرنشینان انتخاب شده بیش از ظرفیت مجاز خودرو است!
                        </div>
                        <div class="form-text">برای انتخاب چند سرنشین، کلید Ctrl را نگه دارید</div>
                    </div>

                    <!-- نوع خروج (فقط برای خودروهای پولرسان) -->
                    <div id="modalExitTypeGroup" class="mb-3" style="display: none;">
                        <label class="form-label">
                            <i class="bi bi-arrow-right-circle"></i> نوع خروج
                        </label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="exitType" id="modalExitTypeGeneral" value="general" checked>
                                <label class="form-check-label" for="modalExitTypeGeneral">عمومی</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="exitType" id="modalExitTypeTreasury" value="treasury">
                                <label class="form-check-label" for="modalExitTypeTreasury">خزانه‌داری</label>
                            </div>
                        </div>
                    </div>

                    <!-- توضیحات -->
                    <div class="mb-3">
                        <label for="modalExitNotes" class="form-label">
                            <i class="bi bi-chat-text"></i> توضیحات
                        </label>
                        <textarea id="modalExitNotes" name="notes" class="form-control" rows="3" placeholder="توضیحات اختیاری..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-warning" id="modalSubmitBtn" onclick="submitCarExit()">
                    <i class="bi bi-box-arrow-right"></i> ثبت خروج
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .dashboard-date {
        text-align: center;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stats-icon {
        font-size: 2.5rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 1rem;
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-absent {
        border-right: 4px solid #dc3545;
    }
    .stats-absent .stats-icon { color: #dc3545; }

    .stats-hourly-exit {
        border-right: 4px solid #ffc107;
    }
    .stats-hourly-exit .stats-icon { color: #ffc107; }

    .stats-late {
        border-right: 4px solid #fd7e14;
    }
    .stats-late .stats-icon { color: #fd7e14; }

    /* Quick Operations Column Styles */
    .quick-operations-column {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        height: 100%;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .quick-operations-column:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .column-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #dee2e6;
    }

    .column-header i {
        font-size: 1.2rem;
        margin-left: 0.5rem;
    }

    .column-header h6 {
        font-weight: 600;
        color: #495057;
    }

    .column-content {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 0.75rem;
        text-decoration: none;
        color: #495057;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        min-height: 50px;
        position: relative;
        z-index: 1;
        cursor: pointer;
        pointer-events: auto;
    }

    .quick-action-btn:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
        transform: translateX(5px);
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
        text-decoration: none;
    }

    .quick-action-btn i {
        font-size: 1.2rem;
        margin-left: 0.75rem;
        opacity: 0.8;
        min-width: 20px;
    }

    .quick-action-btn span {
        font-weight: 500;
        font-size: 0.9rem;
        line-height: 1.2;
    }

    .quick-action-btn:hover i {
        opacity: 1;
    }

    /* Specific button colors */
    .daily-entry:hover { background: linear-gradient(135deg, #28a745, #20c997); }
    .final-exit:hover { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    .hourly-exit:hover { background: linear-gradient(135deg, #fd7e14, #ffc107); }
    .mission-exit:hover { background: linear-gradient(135deg, #007bff, #6f42c1); }
    .return-entry:hover { background: linear-gradient(135deg, #20c997, #17a2b8); }
    .vehicle-entry:hover { background: linear-gradient(135deg, #28a745, #20c997); }
    .vehicle-exit:hover { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    .present-report:hover { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
    .out-report:hover { background: linear-gradient(135deg, #ffc107, #fd7e14); }
    .vehicle-status:hover { background: linear-gradient(135deg, #6c757d, #495057); }
    .attendance-report:hover { background: linear-gradient(135deg, #28a745, #20c997); }
    .system-settings:hover { background: linear-gradient(135deg, #6f42c1, #e83e8c); }

    /* Driving License Section Styles */
    .driving-license-section {
        transition: all 0.3s ease;
    }

    .driving-license-section:hover {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
    }

    .driving-license-section .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .driving-license-section .form-check-input:checked + .form-check-label {
        color: #28a745;
        font-weight: 600;
    }

    .employee-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .employee-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.2s ease;
    }

    .employee-item:hover {
        background-color: #f8f9fa;
    }

    .employee-item:last-child {
        border-bottom: none;
    }

    .employee-info strong {
        color: #2c3e50;
    }

    .employee-status {
        text-align: left;
    }

    .car-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .car-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.2s ease;
    }

    .car-item:hover {
        background-color: #f8f9fa;
    }

    .car-item:last-child {
        border-bottom: none;
    }

    .car-info strong {
        color: #2c3e50;
    }

    .car-status {
        text-align: left;
    }

    .card {
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-radius: 15px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 15px 15px 0 0 !important;
        padding: 1rem 1.5rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .auto-refresh-indicator {
        position: fixed;
        top: 20px;
        left: 20px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        z-index: 1000;
        display: none;
    }

    .auto-refresh-indicator.show {
        display: block;
    }
</style>

<div class="auto-refresh-indicator" id="refreshIndicator">
    <i class="bi bi-arrow-clockwise"></i> به‌روزرسانی خودکار...
</div>

<script>
    // Auto-refresh every 30 seconds
    let refreshInterval;
    let lastRefresh = Date.now();

    function startAutoRefresh() {
        refreshInterval = setInterval(() => {
            // بررسی اینکه آیا کاربر در حال navigation است
            if (window.isNavigating) {
                console.log('Navigation in progress, skipping auto-refresh');
                return;
            }

            // نمایش نشانگر به‌روزرسانی
            const indicator = document.getElementById('refreshIndicator');
            indicator.classList.add('show');

            // به‌روزرسانی صفحه
            setTimeout(() => {
                if (!window.isNavigating) {
                    window.location.reload();
                }
            }, 500);
        }, 60000); // هر 60 ثانیه (کاهش فرکانس)
    }

    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    }

    // شروع auto-refresh
    document.addEventListener('DOMContentLoaded', function() {
        // تنظیم متغیر navigation
        window.isNavigating = false;

        // موقتاً auto-refresh را غیرفعال می‌کنیم
        // startAutoRefresh();

        // توقف auto-refresh هنگام خروج از صفحه
        window.addEventListener('beforeunload', stopAutoRefresh);

        // توقف auto-refresh هنگام کلیک روی لینک‌ها
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                console.log('Link clicked:', href);

                // بررسی اینکه آیا لینک معتبر است
                if (href && href !== '#' && !href.startsWith('javascript:')) {
                    // تنظیم flag navigation
                    window.isNavigating = true;

                    // متوقف کردن auto-refresh
                    stopAutoRefresh();

                    console.log('Navigating to:', href);

                    // اجازه دادن به navigation طبیعی
                    return true;
                }

                // جلوگیری از default behavior برای لینک‌های خاص
                e.preventDefault();
            });
        });

        // نمایش زمان آخرین به‌روزرسانی
        const now = new Date();
        console.log('Dashboard loaded at:', now.toLocaleTimeString('fa-IR'));
    });

    // Manual refresh function
    function manualRefresh() {
        const indicator = document.getElementById('refreshIndicator');
        indicator.innerHTML = '<i class="bi bi-arrow-clockwise"></i> به‌روزرسانی دستی...';
        indicator.classList.add('show');

        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    // Keyboard shortcut for manual refresh (F5 or Ctrl+R)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
            e.preventDefault();
            manualRefresh();
        }
    });

    // نمایش سرنشینان خودرو
    async function showCarPassengers(carTrafficLogId) {
        try {
            // نمایش مودال
            const modal = new bootstrap.Modal(document.getElementById('carPassengersModal'));
            modal.show();

            // بارگذاری اطلاعات
            const response = await fetch(`/api/traffic/car-passengers/${carTrafficLogId}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'خطا در دریافت اطلاعات');
            }

            // ساخت محتوای مودال
            let content = `
                <div class="car-info-section mb-4">
                    <h6 class="border-bottom pb-2 mb-3">
                        <i class="bi bi-car-front-fill"></i> اطلاعات خودرو
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>شماره پلاک:</strong> ${data.car.plateNumber}
                        </div>
                        <div class="col-md-6">
                            <strong>مدل:</strong> ${data.car.model} - ${data.car.type}
                        </div>
                    </div>
                    ${data.car.isMoneyTransport ? '<div class="mt-2"><span class="badge bg-warning"><i class="bi bi-shield-check"></i> پولرسان</span></div>' : ''}
                </div>

                <div class="exit-info-section mb-4">
                    <h6 class="border-bottom pb-2 mb-3">
                        <i class="bi bi-box-arrow-right"></i> اطلاعات خروج
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>زمان خروج:</strong> ${data.exitInfo.exitTime || 'نامشخص'}
                        </div>
                        <div class="col-md-6">
                            <strong>نوع خروج:</strong> ${data.exitInfo.exitType || 'نامشخص'}
                        </div>
                    </div>
                    ${data.exitInfo.notes ? `<div class="mt-2"><strong>توضیحات:</strong> ${data.exitInfo.notes}</div>` : ''}
                </div>`;

            // اطلاعات راننده
            if (data.driver) {
                content += `
                    <div class="driver-section mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-person-badge"></i> راننده
                        </h6>
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <strong>${data.driver.name}</strong>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">کد: ${data.driver.personnelCode}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="badge bg-primary">${data.driver.jobTitle || 'نامشخص'}</span>
                                    </div>
                                    <div class="col-md-2">
                                        ${data.driver.phoneNumber ? `<small class="text-muted">${data.driver.phoneNumber}</small>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
            }

            // لیست سرنشینان
            content += `
                <div class="passengers-section">
                    <h6 class="border-bottom pb-2 mb-3">
                        <i class="bi bi-people"></i> سرنشینان (${data.passengers.length} نفر)
                    </h6>`;

            if (data.passengers.length > 0) {
                content += '<div class="list-group">';
                data.passengers.forEach(passenger => {
                    content += `
                        <div class="list-group-item">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <strong>${passenger.name}</strong>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">کد: ${passenger.personnelCode}</small>
                                </div>
                                <div class="col-md-3">
                                    <span class="badge bg-secondary">${passenger.jobTitle || 'نامشخص'}</span>
                                </div>
                                <div class="col-md-2">
                                    ${passenger.phoneNumber ? `<small class="text-muted">${passenger.phoneNumber}</small>` : ''}
                                </div>
                                <div class="col-md-1">
                                    <span class="badge ${passenger.isEntered ? 'bg-success' : 'bg-warning'}">
                                        ${passenger.isEntered ? 'برگشته' : 'خارج'}
                                    </span>
                                </div>
                            </div>
                        </div>`;
                });
                content += '</div>';
            } else {
                content += '<div class="text-center text-muted py-3"><i class="bi bi-person-x"></i> هیچ سرنشینی در خودرو نیست</div>';
            }

            content += '</div>';

            // نمایش محتوا در مودال
            document.getElementById('carPassengersContent').innerHTML = content;

        } catch (error) {
            console.error('Error loading car passengers:', error);
            document.getElementById('carPassengersContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> خطا در بارگذاری اطلاعات: ${error.message}
                </div>`;
        }
    }

    document.addEventListener("DOMContentLoaded", function() {
        const driverSelect = document.getElementById('driverSelect');
        const passengerSelect = document.getElementById('passengerSelect');

        if (driverSelect && passengerSelect) {
            const capacitySpan = document.getElementById('capacity');
            const selectedCountSpan = document.getElementById('selectedCount');
            const passengerWarning = document.getElementById('passengerWarning');
            const exitForm = passengerSelect.closest('form');
            const submitButton = exitForm ? exitForm.querySelector('button[type="submit"]') : null;
            const capacity = parseInt(capacitySpan.textContent) - 1; // Subtract 1 for the driver

            function updatePassengerList() {
                const selectedDriverId = driverSelect.value;
                for (let option of passengerSelect.options) {
                    option.style.display = (option.value === selectedDriverId) ? 'none' : 'block';
                }
            }

            function validatePassengers() {
                const selectedCount = Array.from(passengerSelect.options).filter(option => option.selected && option.style.display !== 'none').length;
                selectedCountSpan.textContent = selectedCount;

                if (selectedCount > capacity) {
                    passengerWarning.style.display = 'block';
                    if (submitButton) submitButton.disabled = true;
                } else {
                    passengerWarning.style.display = 'none';
                    if (submitButton) submitButton.disabled = false;
                }
            }

            driverSelect.addEventListener('change', () => {
                updatePassengerList();
                validatePassengers();
            });

            passengerSelect.addEventListener('change', validatePassengers);

            // Initial setup
            updatePassengerList();
            validatePassengers();
        }
    });

    // متغیرهای مودال خروج خودرو
    let currentCarId = null;
    let currentCarCapacity = 0;
    let isMoneyTransport = false;

    // نمایش مودال خروج خودرو
    async function showCarExitModal(carId, plateNumber, model, capacity, moneyTransport) {
        currentCarId = carId;
        currentCarCapacity = capacity;
        isMoneyTransport = moneyTransport;

        // تنظیم اطلاعات خودرو
        document.getElementById('modalCarId').value = carId;
        document.getElementById('modalCarPlate').textContent = plateNumber;
        document.getElementById('modalCarModel').textContent = model;
        document.getElementById('modalCarCapacity').textContent = capacity;

        // نمایش/مخفی کردن نوع خروج برای خودروهای پولرسان
        const exitTypeGroup = document.getElementById('modalExitTypeGroup');
        if (moneyTransport) {
            exitTypeGroup.style.display = 'block';
        } else {
            exitTypeGroup.style.display = 'none';
        }

        // بارگذاری لیست رانندگان
        await loadAvailableDrivers();

        // بارگذاری لیست سرنشینان
        await loadAvailablePassengers();

        // پاک کردن فرم
        document.getElementById('modalDriverSelect').value = '';
        document.getElementById('modalPassengerSelect').selectedIndex = -1;
        document.getElementById('modalExitNotes').value = '';
        document.getElementById('modalSelectedCount').textContent = '0';
        document.getElementById('modalPassengerWarning').style.display = 'none';

        // نمایش مودال
        const modal = new bootstrap.Modal(document.getElementById('carExitModal'));
        modal.show();
    }

    // بارگذاری لیست رانندگان
    async function loadAvailableDrivers() {
        try {
            const response = await fetch('/api/traffic/available-drivers');
            const drivers = await response.json();

            const driverSelect = document.getElementById('modalDriverSelect');
            driverSelect.innerHTML = '<option value="">انتخاب کنید...</option>';

            drivers.forEach(driver => {
                const option = document.createElement('option');
                option.value = driver.employeeId;
                option.textContent = `${driver.firstName} ${driver.lastName} - ${driver.personnelCode}`;
                driverSelect.appendChild(option);
            });
        } catch (error) {
            console.error('خطا در بارگذاری لیست رانندگان:', error);
            alert('خطا در بارگذاری لیست رانندگان');
        }
    }

    // بارگذاری لیست سرنشینان
    async function loadAvailablePassengers() {
        try {
            let url = '/api/traffic/available-passengers';
            if (isMoneyTransport) {
                const exitType = document.querySelector('input[name="exitType"]:checked').value;
                url += `?exitType=${exitType}`;
            }

            const response = await fetch(url);
            const passengers = await response.json();

            const passengerSelect = document.getElementById('modalPassengerSelect');
            passengerSelect.innerHTML = '';

            passengers.forEach(passenger => {
                const option = document.createElement('option');
                option.value = passenger.employeeId;
                option.textContent = `${passenger.firstName} ${passenger.lastName} - ${passenger.personnelCode}`;
                passengerSelect.appendChild(option);
            });
        } catch (error) {
            console.error('خطا در بارگذاری لیست سرنشینان:', error);
            alert('خطا در بارگذاری لیست سرنشینان');
        }
    }

    // مدیریت تغییر راننده
    document.addEventListener('DOMContentLoaded', function() {
        const modalDriverSelect = document.getElementById('modalDriverSelect');
        const modalPassengerSelect = document.getElementById('modalPassengerSelect');

        if (modalDriverSelect && modalPassengerSelect) {
            modalDriverSelect.addEventListener('change', updateModalPassengerList);
            modalPassengerSelect.addEventListener('change', validateModalPassengers);

            // مدیریت تغییر نوع خروج برای خودروهای پولرسان
            const exitTypeRadios = document.querySelectorAll('input[name="exitType"]');
            exitTypeRadios.forEach(radio => {
                radio.addEventListener('change', loadAvailablePassengers);
            });
        }
    });

    // به‌روزرسانی لیست سرنشینان (حذف راننده انتخاب شده)
    function updateModalPassengerList() {
        const selectedDriverId = document.getElementById('modalDriverSelect').value;
        const passengerOptions = document.getElementById('modalPassengerSelect').options;

        for (let option of passengerOptions) {
            option.style.display = (option.value === selectedDriverId) ? 'none' : 'block';
            if (option.value === selectedDriverId && option.selected) {
                option.selected = false;
            }
        }

        validateModalPassengers();
    }

    // اعتبارسنجی تعداد سرنشینان
    function validateModalPassengers() {
        const passengerSelect = document.getElementById('modalPassengerSelect');
        const selectedCount = Array.from(passengerSelect.options)
            .filter(option => option.selected && option.style.display !== 'none').length;

        document.getElementById('modalSelectedCount').textContent = selectedCount;

        const maxPassengers = currentCarCapacity - 1; // کم کردن 1 برای راننده
        const warning = document.getElementById('modalPassengerWarning');
        const submitBtn = document.getElementById('modalSubmitBtn');

        if (selectedCount > maxPassengers) {
            warning.style.display = 'block';
            submitBtn.disabled = true;
        } else {
            warning.style.display = 'none';
            submitBtn.disabled = false;
        }
    }

    // ارسال فرم خروج خودرو
    async function submitCarExit() {
        const driverId = document.getElementById('modalDriverSelect').value;
        const passengerSelect = document.getElementById('modalPassengerSelect');
        const passengerIds = Array.from(passengerSelect.options)
            .filter(option => option.selected && option.style.display !== 'none')
            .map(option => parseInt(option.value));
        const notes = document.getElementById('modalExitNotes').value;

        // اعتبارسنجی
        if (!driverId) {
            alert('لطفاً راننده را انتخاب کنید.');
            return;
        }

        const submitBtn = document.getElementById('modalSubmitBtn');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> در حال ثبت...';

        try {
            const formData = new FormData();
            formData.append('carId', currentCarId);
            formData.append('driverId', driverId);
            passengerIds.forEach(id => formData.append('passengerIds', id));
            formData.append('notes', notes || '');

            const response = await fetch('?handler=RegisterCarExit', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                }
            });

            if (response.ok) {
                // بستن مودال
                const modal = bootstrap.Modal.getInstance(document.getElementById('carExitModal'));
                modal.hide();

                // نمایش پیام موفقیت
                alert('خروج خودرو با موفقیت ثبت شد.');

                // بارگذاری مجدد صفحه
                window.location.reload();
            } else {
                alert('خطا در ثبت خروج خودرو. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('خطا در ثبت خروج:', error);
            alert('خطا در ارتباط با سرور.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }
</script>

