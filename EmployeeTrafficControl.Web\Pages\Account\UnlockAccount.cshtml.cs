using EmployeeTrafficControl.Data.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Web.Pages.Account
{
    public class UnlockAccountModel : PageModel
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<UnlockAccountModel> _logger;

        public UnlockAccountModel(UserManager<ApplicationUser> userManager, ILogger<UnlockAccountModel> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        [BindProperty]
        [Required(ErrorMessage = "نام کاربری الزامی است.")]
        public string Username { get; set; } = string.Empty;

        public string? Message { get; set; }
        public bool IsSuccess { get; set; }

        public void OnGet()
        {
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var user = await _userManager.FindByNameAsync(Username);
                if (user == null)
                {
                    Message = "کاربری با این نام کاربری یافت نشد.";
                    IsSuccess = false;
                    return Page();
                }

                // بررسی وضعیت قفل
                var isLockedOut = await _userManager.IsLockedOutAsync(user);
                if (!isLockedOut)
                {
                    Message = "این حساب کاربری قفل نشده است.";
                    IsSuccess = false;
                    return Page();
                }

                // بازگشایی حساب
                var result = await _userManager.SetLockoutEndDateAsync(user, null);
                if (result.Succeeded)
                {
                    // Reset failed access count
                    await _userManager.ResetAccessFailedCountAsync(user);
                    
                    Message = "حساب کاربری با موفقیت بازگشایی شد. اکنون می‌توانید وارد شوید.";
                    IsSuccess = true;
                    _logger.LogInformation("حساب کاربری {Username} بازگشایی شد.", Username);
                }
                else
                {
                    Message = "خطا در بازگشایی حساب کاربری.";
                    IsSuccess = false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در بازگشایی حساب کاربری {Username}", Username);
                Message = "خطای سیستمی رخ داده است.";
                IsSuccess = false;
            }

            return Page();
        }
    }
}
