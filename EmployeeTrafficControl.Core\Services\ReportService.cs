using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Services
{
    public class ReportService
    {
        private readonly ApplicationDbContext _context;

        public ReportService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// گزارش ترافیک کارمندان در بازه زمانی مشخص
        /// </summary>
        public async Task<EmployeeTrafficReportData> GetEmployeeTrafficReportAsync(
            DateTime fromDate, DateTime toDate, int? buildingId = null, int? employeeId = null)
        {
            var query = _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Where(es => es.Date.Date >= fromDate.Date && es.Date.Date <= toDate.Date);

            if (buildingId.HasValue)
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);

            if (employeeId.HasValue)
                query = query.Where(es => es.EmployeeId == employeeId.Value);

            var employeeStatuses = await query
                .OrderBy(es => es.Date)
                .ThenBy(es => es.Employee.FirstName)
                .ThenBy(es => es.Employee.LastName)
                .ToListAsync();

            // گروه‌بندی بر اساس کارمند و تاریخ
            var groupedData = employeeStatuses
                .GroupBy(es => new { es.EmployeeId, es.Date.Date })
                .Select(g => new EmployeeTrafficReportItem
                {
                    Employee = g.First().Employee,
                    Date = g.Key.Date,
                    EntryTime = g.FirstOrDefault()?.EntryTime,
                    FinalExitTime = g.FirstOrDefault()?.FinalExitTime,
                    CurrentStatus = g.First().CurrentStatus,
                    Notes = g.First().Notes,
                    IsInVehicle = g.First().IsInVehicle,
                    CurrentVehicleId = g.First().CurrentVehicleId
                })
                .ToList();

            return new EmployeeTrafficReportData
            {
                FromDate = fromDate,
                ToDate = toDate,
                BuildingId = buildingId,
                EmployeeId = employeeId,
                Items = groupedData,
                TotalRecords = groupedData.Count,
                TotalEmployees = groupedData.Select(x => x.Employee.EmployeeId).Distinct().Count()
            };
        }

        /// <summary>
        /// گزارش ترافیک خودروها در بازه زمانی مشخص
        /// </summary>
        public async Task<CarTrafficReportData> GetCarTrafficReportAsync(
            DateTime fromDate, DateTime toDate, int? buildingId = null, int? carId = null)
        {
            var query = _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => (ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date >= fromDate.Date && ctl.EntryTime.Value.Date <= toDate.Date) ||
                             (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date >= fromDate.Date && ctl.ExitTime.Value.Date <= toDate.Date));

            if (buildingId.HasValue)
                query = query.Where(ctl => ctl.BuildingId == buildingId.Value);

            if (carId.HasValue)
                query = query.Where(ctl => ctl.CarId == carId.Value);

            var carTrafficLogs = await query
                .OrderBy(ctl => ctl.ExitTime ?? ctl.EntryTime)
                .ToListAsync();

            var items = carTrafficLogs.Select(ctl => new CarTrafficReportItem
            {
                Car = ctl.Car,
                DriverEmployee = ctl.DriverEmployee,
                Building = ctl.Building,
                EntryTime = ctl.EntryTime,
                ExitTime = ctl.ExitTime,
                CurrentStatus = ctl.CurrentStatus,
                ExitType = ctl.ExitType,
                Notes = ctl.Notes,
                PassengerCount = ctl.CarPassengers.Count,
                Passengers = ctl.CarPassengers.Select(cp => cp.Employee).ToList(),
                Duration = ctl.EntryTime.HasValue && ctl.ExitTime.HasValue
                    ? ctl.EntryTime.Value - ctl.ExitTime.Value
                    : null
            }).ToList();

            return new CarTrafficReportData
            {
                FromDate = fromDate,
                ToDate = toDate,
                BuildingId = buildingId,
                CarId = carId,
                Items = items,
                TotalRecords = items.Count,
                TotalCars = items.Select(x => x.Car.CarId).Distinct().Count()
            };
        }

        /// <summary>
        /// گزارش حضور و غیاب در بازه زمانی مشخص
        /// </summary>
        public async Task<AttendanceReportData> GetAttendanceReportAsync(
            DateTime fromDate, DateTime toDate, int? buildingId = null, int? employeeId = null)
        {
            var query = _context.DailyAttendances
                .Include(da => da.Employee)
                    .ThenInclude(e => e.Job)
                .Include(da => da.Employee)
                    .ThenInclude(e => e.Building)
                .Where(da => da.Date.Date >= fromDate.Date && da.Date.Date <= toDate.Date);

            if (buildingId.HasValue)
                query = query.Where(da => da.Employee.BuildingId == buildingId.Value);

            if (employeeId.HasValue)
                query = query.Where(da => da.EmployeeId == employeeId.Value);

            var attendances = await query
                .OrderBy(da => da.Date)
                .ThenBy(da => da.Employee.FirstName)
                .ThenBy(da => da.Employee.LastName)
                .ToListAsync();

            var items = attendances.Select(da => new AttendanceReportItem
            {
                Employee = da.Employee,
                Date = da.Date,
                CheckInTime = da.CheckInTime,
                CheckOutTime = da.CheckOutTime,
                IsPresent = da.IsPresent,
                TotalWorkHours = da.TotalWorkHours,
                LateMinutes = da.LateMinutes,
                EarlyLeaveMinutes = da.EarlyLeaveMinutes,
                HourlyExitCount = da.HourlyExitCount,
                TotalHourlyExitTime = da.TotalHourlyExitTime,
                Notes = da.Notes,
                IsApproved = da.IsApproved
            }).ToList();

            // محاسبه آمار
            var totalDays = (toDate.Date - fromDate.Date).Days + 1;
            var totalPossibleAttendances = items.Select(x => x.Employee.EmployeeId).Distinct().Count() * totalDays;
            var totalPresent = items.Count(x => x.IsPresent);
            var totalAbsent = totalPossibleAttendances - totalPresent;

            return new AttendanceReportData
            {
                FromDate = fromDate,
                ToDate = toDate,
                BuildingId = buildingId,
                EmployeeId = employeeId,
                Items = items,
                TotalRecords = items.Count,
                TotalEmployees = items.Select(x => x.Employee.EmployeeId).Distinct().Count(),
                TotalDays = totalDays,
                TotalPresent = totalPresent,
                TotalAbsent = totalAbsent,
                AttendancePercentage = totalPossibleAttendances > 0 ? (double)totalPresent / totalPossibleAttendances * 100 : 0
            };
        }

        /// <summary>
        /// گزارش تفصیلی ترددها در یک روز مشخص
        /// </summary>
        public async Task<DetailedDailyReportData> GetDetailedDailyReportAsync(
            DateTime date, int? buildingId = null)
        {
            // ترددهای کارمندان
            var employeeStatuses = await _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Where(es => es.Date.Date == date.Date)
                .Where(es => buildingId == null || es.Employee.BuildingId == buildingId.Value)
                .OrderBy(es => es.EntryTime ?? es.LastUpdated)
                .ToListAsync();

            // ترددهای خودروها
            var carTrafficLogs = await _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => (ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date == date.Date) ||
                             (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date == date.Date))
                .Where(ctl => buildingId == null || ctl.BuildingId == buildingId.Value)
                .OrderBy(ctl => ctl.ExitTime ?? ctl.EntryTime)
                .ToListAsync();

            return new DetailedDailyReportData
            {
                Date = date,
                BuildingId = buildingId,
                EmployeeStatuses = employeeStatuses,
                CarTrafficLogs = carTrafficLogs,
                TotalEmployeeMovements = employeeStatuses.Count,
                TotalCarMovements = carTrafficLogs.Count
            };
        }

        /// <summary>
        /// دریافت لیست ساختمان‌ها برای فیلتر
        /// </summary>
        public async Task<List<Building>> GetBuildingsAsync()
        {
            return await _context.Buildings
                .OrderBy(b => b.Name)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست کارمندان برای فیلتر
        /// </summary>
        public async Task<List<Employee>> GetEmployeesAsync(int? buildingId = null)
        {
            var query = _context.Employees
                .Include(e => e.Job)
                .Include(e => e.Building)
                .Where(e => e.IsActive);

            if (buildingId.HasValue)
                query = query.Where(e => e.BuildingId == buildingId.Value);

            return await query
                .OrderBy(e => e.FirstName)
                .ThenBy(e => e.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست خودروها برای فیلتر
        /// </summary>
        public async Task<List<Car>> GetCarsAsync(int? buildingId = null)
        {
            var query = _context.Cars.AsQueryable();

            if (buildingId.HasValue)
                query = query.Where(c => c.BuildingId == buildingId.Value);

            return await query
                .Include(c => c.Building)
                .OrderBy(c => c.PlateNumber)
                .ToListAsync();
        }
    }
}
