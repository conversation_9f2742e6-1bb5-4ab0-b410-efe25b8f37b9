using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Services
{
    public class ReportService
    {
        private readonly ApplicationDbContext _context;

        public ReportService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// گزارش ترافیک کارمندان در بازه زمانی مشخص
        /// </summary>
        public async Task<EmployeeTrafficReportData> GetEmployeeTrafficReportAsync(
            DateTime fromDate, DateTime toDate, int? buildingId = null, int? employeeId = null)
        {
            var query = _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Where(es => es.Date.Date >= fromDate.Date && es.Date.Date <= toDate.Date);

            if (buildingId.HasValue)
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);

            if (employeeId.HasValue)
                query = query.Where(es => es.EmployeeId == employeeId.Value);

            var employeeStatuses = await query
                .OrderBy(es => es.Date)
                .ThenBy(es => es.Employee.FirstName)
                .ThenBy(es => es.Employee.LastName)
                .ToListAsync();

            // گروه‌بندی بر اساس کارمند و تاریخ
            var groupedData = employeeStatuses
                .GroupBy(es => new { es.EmployeeId, es.Date.Date })
                .Select(g => new EmployeeTrafficReportItem
                {
                    Employee = g.First().Employee,
                    Date = g.Key.Date,
                    EntryTime = g.FirstOrDefault()?.EntryTime,
                    FinalExitTime = g.FirstOrDefault()?.FinalExitTime,
                    CurrentStatus = g.First().CurrentStatus,
                    Notes = g.First().Notes,
                    IsInVehicle = g.First().IsInVehicle,
                    CurrentVehicleId = g.First().CurrentVehicleId
                })
                .ToList();

            return new EmployeeTrafficReportData
            {
                FromDate = fromDate,
                ToDate = toDate,
                BuildingId = buildingId,
                EmployeeId = employeeId,
                Items = groupedData,
                TotalRecords = groupedData.Count,
                TotalEmployees = groupedData.Select(x => x.Employee.EmployeeId).Distinct().Count()
            };
        }

        /// <summary>
        /// گزارش ترافیک خودروها در بازه زمانی مشخص
        /// </summary>
        public async Task<CarTrafficReportData> GetCarTrafficReportAsync(
            DateTime fromDate, DateTime toDate, int? buildingId = null, int? carId = null)
        {
            var query = _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => (ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date >= fromDate.Date && ctl.EntryTime.Value.Date <= toDate.Date) ||
                             (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date >= fromDate.Date && ctl.ExitTime.Value.Date <= toDate.Date));

            if (buildingId.HasValue)
                query = query.Where(ctl => ctl.BuildingId == buildingId.Value);

            if (carId.HasValue)
                query = query.Where(ctl => ctl.CarId == carId.Value);

            var carTrafficLogs = await query
                .OrderBy(ctl => ctl.ExitTime ?? ctl.EntryTime)
                .ToListAsync();

            var items = carTrafficLogs.Select(ctl => new CarTrafficReportItem
            {
                Car = ctl.Car,
                DriverEmployee = ctl.DriverEmployee,
                Building = ctl.Building,
                EntryTime = ctl.EntryTime,
                ExitTime = ctl.ExitTime,
                CurrentStatus = ctl.CurrentStatus,
                ExitType = ctl.ExitType,
                Notes = ctl.Notes,
                PassengerCount = ctl.CarPassengers.Count,
                Passengers = ctl.CarPassengers.Select(cp => cp.Employee).ToList(),
                Duration = ctl.EntryTime.HasValue && ctl.ExitTime.HasValue
                    ? ctl.EntryTime.Value - ctl.ExitTime.Value
                    : null
            }).ToList();

            return new CarTrafficReportData
            {
                FromDate = fromDate,
                ToDate = toDate,
                BuildingId = buildingId,
                CarId = carId,
                Items = items,
                TotalRecords = items.Count,
                TotalCars = items.Select(x => x.Car.CarId).Distinct().Count()
            };
        }

        /// <summary>
        /// گزارش حضور و غیاب در بازه زمانی مشخص
        /// </summary>
        public async Task<AttendanceReportData> GetAttendanceReportAsync(
            DateTime fromDate, DateTime toDate, int? buildingId = null, int? employeeId = null)
        {
            var query = _context.DailyAttendances
                .Include(da => da.Employee)
                    .ThenInclude(e => e.Job)
                .Include(da => da.Employee)
                    .ThenInclude(e => e.Building)
                .Where(da => da.Date.Date >= fromDate.Date && da.Date.Date <= toDate.Date);

            if (buildingId.HasValue)
                query = query.Where(da => da.Employee.BuildingId == buildingId.Value);

            if (employeeId.HasValue)
                query = query.Where(da => da.EmployeeId == employeeId.Value);

            var attendances = await query
                .OrderBy(da => da.Date)
                .ThenBy(da => da.Employee.FirstName)
                .ThenBy(da => da.Employee.LastName)
                .ToListAsync();

            var items = attendances.Select(da => new AttendanceReportItem
            {
                Employee = da.Employee,
                Date = da.Date,
                CheckInTime = da.CheckInTime,
                CheckOutTime = da.CheckOutTime,
                IsPresent = da.IsPresent,
                TotalWorkHours = da.TotalWorkHours,
                LateMinutes = da.LateMinutes,
                EarlyLeaveMinutes = da.EarlyLeaveMinutes,
                HourlyExitCount = da.HourlyExitCount,
                TotalHourlyExitTime = da.TotalHourlyExitTime,
                Notes = da.Notes,
                IsApproved = da.IsApproved
            }).ToList();

            // محاسبه آمار
            var totalDays = (toDate.Date - fromDate.Date).Days + 1;
            var totalPossibleAttendances = items.Select(x => x.Employee.EmployeeId).Distinct().Count() * totalDays;
            var totalPresent = items.Count(x => x.IsPresent);
            var totalAbsent = totalPossibleAttendances - totalPresent;

            return new AttendanceReportData
            {
                FromDate = fromDate,
                ToDate = toDate,
                BuildingId = buildingId,
                EmployeeId = employeeId,
                Items = items,
                TotalRecords = items.Count,
                TotalEmployees = items.Select(x => x.Employee.EmployeeId).Distinct().Count(),
                TotalDays = totalDays,
                TotalPresent = totalPresent,
                TotalAbsent = totalAbsent,
                AttendancePercentage = totalPossibleAttendances > 0 ? (double)totalPresent / totalPossibleAttendances * 100 : 0
            };
        }

        /// <summary>
        /// گزارش تفصیلی ترددها در یک روز مشخص
        /// </summary>
        public async Task<DetailedDailyReportData> GetDetailedDailyReportAsync(
            DateTime date, int? buildingId = null)
        {
            // ترددهای کارمندان
            var employeeStatuses = await _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Where(es => es.Date.Date == date.Date)
                .Where(es => buildingId == null || es.Employee.BuildingId == buildingId.Value)
                .OrderBy(es => es.EntryTime ?? es.LastUpdated)
                .ToListAsync();

            // ترددهای خودروها
            var carTrafficLogs = await _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => (ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date == date.Date) ||
                             (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date == date.Date))
                .Where(ctl => buildingId == null || ctl.BuildingId == buildingId.Value)
                .OrderBy(ctl => ctl.ExitTime ?? ctl.EntryTime)
                .ToListAsync();

            return new DetailedDailyReportData
            {
                Date = date,
                BuildingId = buildingId,
                EmployeeStatuses = employeeStatuses,
                CarTrafficLogs = carTrafficLogs,
                TotalEmployeeMovements = employeeStatuses.Count,
                TotalCarMovements = carTrafficLogs.Count
            };
        }

        /// <summary>
        /// دریافت لیست ساختمان‌ها برای فیلتر
        /// </summary>
        public async Task<List<Building>> GetBuildingsAsync()
        {
            return await _context.Buildings
                .OrderBy(b => b.Name)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست کارمندان برای فیلتر
        /// </summary>
        public async Task<List<Employee>> GetEmployeesAsync(int? buildingId = null)
        {
            var query = _context.Employees
                .Include(e => e.Job)
                .Include(e => e.Building)
                .Where(e => e.IsActive);

            if (buildingId.HasValue)
                query = query.Where(e => e.BuildingId == buildingId.Value);

            return await query
                .OrderBy(e => e.FirstName)
                .ThenBy(e => e.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست خودروها برای فیلتر
        /// </summary>
        public async Task<List<Car>> GetCarsAsync(int? buildingId = null)
        {
            var query = _context.Cars.AsQueryable();

            if (buildingId.HasValue)
                query = query.Where(c => c.BuildingId == buildingId.Value);

            return await query
                .Include(c => c.Building)
                .OrderBy(c => c.PlateNumber)
                .ToListAsync();
        }

        /// <summary>
        /// گزارش تفصیلی تردد یک کارمند خاص
        /// </summary>
        public async Task<EmployeeDetailedTrafficReportData> GetEmployeeDetailedTrafficReportAsync(
            int employeeId, DateTime fromDate, DateTime toDate)
        {
            // دریافت تمام وضعیت‌های کارمند در بازه زمانی
            var employeeStatuses = await _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Include(es => es.CurrentVehicle)
                .Include(es => es.UpdatedByUser)
                .Where(es => es.EmployeeId == employeeId &&
                           es.Date.Date >= fromDate.Date &&
                           es.Date.Date <= toDate.Date)
                .OrderBy(es => es.Date)
                .ThenBy(es => es.LastUpdated)
                .ToListAsync();

            // دریافت تمام لاگ‌های تردد کارمند
            var trafficLogs = await _context.TrafficLogs
                .Include(tl => tl.Employee)
                .Include(tl => tl.Building)
                .Include(tl => tl.User)
                .Where(tl => tl.EmployeeId == employeeId &&
                           ((tl.EntryTime.HasValue && tl.EntryTime.Value.Date >= fromDate.Date && tl.EntryTime.Value.Date <= toDate.Date) ||
                            (tl.ExitTime.HasValue && tl.ExitTime.Value.Date >= fromDate.Date && tl.ExitTime.Value.Date <= toDate.Date)))
                .OrderBy(tl => tl.EntryTime ?? tl.ExitTime)
                .ToListAsync();

            // دریافت تردد خودروها که کارمند در آنها بوده
            var carTrafficLogs = await _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => (ctl.DriverEmployeeId == employeeId ||
                              ctl.CarPassengers.Any(cp => cp.EmployeeId == employeeId)) &&
                            ((ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date >= fromDate.Date && ctl.EntryTime.Value.Date <= toDate.Date) ||
                             (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date >= fromDate.Date && ctl.ExitTime.Value.Date <= toDate.Date)))
                .OrderBy(ctl => ctl.ExitTime ?? ctl.EntryTime)
                .ToListAsync();

            // تجمیع داده‌ها بر اساس تاریخ
            var dailyReports = new List<EmployeeDailyTrafficItem>();
            var dates = employeeStatuses.Select(es => es.Date.Date).Distinct().OrderBy(d => d);

            foreach (var date in dates)
            {
                var dayStatuses = employeeStatuses.Where(es => es.Date.Date == date).ToList();

                // فیلتر ترددهای روزانه - شامل تمام ترددهای آن روز
                var dayTrafficLogs = trafficLogs.Where(tl =>
                    (tl.EntryTime.HasValue && tl.EntryTime.Value.Date == date) ||
                    (tl.ExitTime.HasValue && tl.ExitTime.Value.Date == date)).ToList();

                // فیلتر ترددهای خودرو - شامل تمام سفرهای آن روز
                var dayCarLogs = carTrafficLogs.Where(ctl =>
                    (ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date == date) ||
                    (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date == date)).ToList();

                var dailyItem = new EmployeeDailyTrafficItem
                {
                    Date = date,
                    Employee = dayStatuses.FirstOrDefault()?.Employee ?? employeeStatuses.FirstOrDefault()?.Employee,
                    DailyStatuses = dayStatuses,
                    TrafficLogs = dayTrafficLogs,
                    CarTrafficLogs = dayCarLogs,
                    // محاسبه آمار روزانه
                    TotalWorkHours = CalculateDailyWorkHours(dayStatuses),
                    EntryTime = dayStatuses.FirstOrDefault(s => s.EntryTime.HasValue)?.EntryTime,
                    FinalExitTime = dayStatuses.FirstOrDefault(s => s.FinalExitTime.HasValue)?.FinalExitTime,
                    // محاسبه بر اساس TrafficLog که نوع تردد دقیق دارد
                    HourlyExitCount = dayTrafficLogs.Count(tl => IsHourlyExit(tl.TrafficType)),
                    MissionCount = dayTrafficLogs.Count(tl => IsMission(tl.TrafficType)),
                    CarTripCount = dayCarLogs.Count()
                };

                dailyReports.Add(dailyItem);
            }

            return new EmployeeDetailedTrafficReportData
            {
                EmployeeId = employeeId,
                Employee = employeeStatuses.FirstOrDefault()?.Employee,
                FromDate = fromDate,
                ToDate = toDate,
                DailyReports = dailyReports,
                TotalDays = dailyReports.Count,
                TotalWorkHours = dailyReports.Sum(dr => dr.TotalWorkHours?.TotalHours ?? 0),
                TotalHourlyExits = dailyReports.Sum(dr => dr.HourlyExitCount),
                TotalMissions = dailyReports.Sum(dr => dr.MissionCount),
                TotalCarTrips = dailyReports.Sum(dr => dr.CarTripCount)
            };
        }

        /// <summary>
        /// گزارش تفصیلی تردد یک خودرو خاص
        /// </summary>
        public async Task<CarDetailedTrafficReportData> GetCarDetailedTrafficReportAsync(
            int carId, DateTime fromDate, DateTime toDate)
        {
            var carTrafficLogs = await _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => ctl.CarId == carId &&
                            ((ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date >= fromDate.Date && ctl.EntryTime.Value.Date <= toDate.Date) ||
                             (ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date >= fromDate.Date && ctl.ExitTime.Value.Date <= toDate.Date)))
                .OrderBy(ctl => ctl.ExitTime ?? ctl.EntryTime)
                .ToListAsync();

            // تجمیع بر اساس نوع خروج
            var exitTypeGroups = carTrafficLogs
                .Where(ctl => !string.IsNullOrEmpty(ctl.ExitType))
                .GroupBy(ctl => ctl.ExitType)
                .Select(g => new CarExitTypeStatistic
                {
                    ExitType = g.Key,
                    Count = g.Count(),
                    TotalDuration = TimeSpan.FromTicks(g.Where(ctl => ctl.GetParkingDuration().HasValue)
                                                       .Sum(ctl => ctl.GetParkingDuration()!.Value.Ticks))
                })
                .ToList();

            // تجمیع بر اساس راننده
            var driverGroups = carTrafficLogs
                .GroupBy(ctl => ctl.DriverEmployeeId)
                .Select(g => new CarDriverStatistic
                {
                    DriverEmployee = g.First().DriverEmployee,
                    TripCount = g.Count(),
                    TotalDuration = TimeSpan.FromTicks(g.Where(ctl => ctl.GetParkingDuration().HasValue)
                                                       .Sum(ctl => ctl.GetParkingDuration()!.Value.Ticks))
                })
                .ToList();

            return new CarDetailedTrafficReportData
            {
                CarId = carId,
                Car = carTrafficLogs.FirstOrDefault()?.Car,
                FromDate = fromDate,
                ToDate = toDate,
                TrafficLogs = carTrafficLogs,
                ExitTypeStatistics = exitTypeGroups,
                DriverStatistics = driverGroups,
                TotalTrips = carTrafficLogs.Count,
                TotalExitTime = TimeSpan.FromTicks(carTrafficLogs.Where(ctl => ctl.GetParkingDuration().HasValue)
                                                                 .Sum(ctl => ctl.GetParkingDuration()!.Value.Ticks)),
                AveragePassengerCount = carTrafficLogs.Any() ? carTrafficLogs.Average(ctl => ctl.CarPassengers.Count) : 0
            };
        }

        private TimeSpan? CalculateDailyWorkHours(List<EmployeeStatus> dayStatuses)
        {
            var entryTime = dayStatuses.FirstOrDefault(s => s.EntryTime.HasValue)?.EntryTime;
            var exitTime = dayStatuses.FirstOrDefault(s => s.FinalExitTime.HasValue)?.FinalExitTime;

            if (entryTime.HasValue && exitTime.HasValue)
            {
                return exitTime.Value - entryTime.Value;
            }
            else if (entryTime.HasValue && !exitTime.HasValue)
            {
                // اگر هنوز خروج نهایی نداشته، تا الان محاسبه کن
                return DateTime.Now - entryTime.Value;
            }

            return null;
        }

        /// <summary>
        /// محاسبه دقیق آمار روزانه بر اساس تغییرات وضعیت
        /// </summary>
        private (int hourlyExits, int missions) CalculateDailyStatusChanges(List<EmployeeStatus> dayStatuses)
        {
            int hourlyExits = 0;
            int missions = 0;

            // مرتب کردن بر اساس زمان آخرین به‌روزرسانی
            var sortedStatuses = dayStatuses.OrderBy(s => s.LastUpdated).ToList();

            EmployeeCurrentStatus? previousStatus = null;

            foreach (var status in sortedStatuses)
            {
                // فقط تغییرات وضعیت را محاسبه کن
                if (previousStatus != status.CurrentStatus)
                {
                    switch (status.CurrentStatus)
                    {
                        case EmployeeCurrentStatus.HourlyExit:
                            hourlyExits++;
                            break;
                        case EmployeeCurrentStatus.OfficialMission:
                            missions++;
                            break;
                    }
                }
                previousStatus = status.CurrentStatus;
            }

            return (hourlyExits, missions);
        }

        /// <summary>
        /// تشخیص نوع تردد بر اساس TrafficType
        /// </summary>
        private string GetTrafficTypeDisplayName(string? trafficType)
        {
            if (string.IsNullOrEmpty(trafficType))
                return "عادی";

            var lowerType = trafficType.ToLower();

            if (lowerType.Contains("خروج ساعتی") || lowerType.Contains("hourly") || lowerType.Contains("مرخصی"))
                return "خروج ساعتی";

            if (lowerType.Contains("ماموریت") || lowerType.Contains("mission"))
                return "ماموریت";

            if (lowerType.Contains("ورود") || lowerType.Contains("entry"))
                return "ورود";

            if (lowerType.Contains("خروج") || lowerType.Contains("exit"))
                return "خروج";

            return trafficType;
        }

        /// <summary>
        /// بررسی اینکه آیا تردد از نوع خروج ساعتی است
        /// </summary>
        private bool IsHourlyExit(string? trafficType)
        {
            if (string.IsNullOrEmpty(trafficType))
                return false;

            var lowerType = trafficType.ToLower();
            return lowerType.Contains("خروج ساعتی") ||
                   lowerType.Contains("خروج ساعت") ||  // برای مشکل encoding
                   lowerType.Contains("hourly") ||
                   lowerType.Contains("مرخصی") ||
                   lowerType.Contains("leave");
        }

        /// <summary>
        /// بررسی اینکه آیا تردد از نوع ماموریت است
        /// </summary>
        private bool IsMission(string? trafficType)
        {
            if (string.IsNullOrEmpty(trafficType))
                return false;

            var lowerType = trafficType.ToLower();
            return lowerType.Contains("ماموریت") ||
                   lowerType.Contains("مامور") ||  // برای مشکل encoding
                   lowerType.Contains("mission");
        }
    }
}
