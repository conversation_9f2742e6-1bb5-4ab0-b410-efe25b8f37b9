﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class CarTrafficLog
    {
        [Key]
        [Display(Name = "شناسه تردد خودرو")]
        public int CarTrafficLogId { get; set; }

        [Display(Name = "خودرو")]
        public int CarId { get; set; }

        [Display(Name = "راننده")]
        public int DriverEmployeeId { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان ورود")]
        public DateTime? EntryTime { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان خروج")]
        public DateTime? ExitTime { get; set; }

        [Display(Name = "در پارکینگ است؟")]
        public bool IsInParking { get; set; } = true;

        [StringLength(4000, ErrorMessage = "توضیحات حداکثر 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Notes { get; set; }

        [Display(Name = "کاربر ثبت کننده")]
        public int UserId { get; set; } = default!; // Changed to int
 
        [StringLength(50, ErrorMessage = "نوع خروج حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نوع خروج")]
        public string? ExitType { get; set; } // "اداری", "ماموریت", "پولرسانی"
 
        // Navigation Properties
        [Display(Name = "خودرو")]
        public Car Car { get; set; } = default!;
 
        [Display(Name = "راننده")]
        public Employee DriverEmployee { get; set; } = default!;
 
        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;
 
        public ICollection<CarPassenger> CarPassengers { get; set; } = new List<CarPassenger>();
 
        // Helper properties for compatibility
        [NotMapped]
        public string PlateNumber => Car?.PlateNumber ?? "";
 
        [NotMapped]
        public string? GuestName => DriverEmployeeId == 0 ? "مهمان" : null;
 
        [NotMapped]
        public string CurrentStatus => IsInParking ? "در پارکینگ" : "خارج از پارکینگ";
 
        [NotMapped]
        public CarStatus Status => IsInParking ? CarStatus.InsideParking : CarStatus.OutsideParking;
 
        // Helper methods
        public bool IsOutOfParking => !IsInParking;
 
        public TimeSpan? GetParkingDuration()
        {
            if (ExitTime.HasValue && EntryTime.HasValue)
            {
                return EntryTime.Value - ExitTime.Value;
            }
            else if (!IsInParking && ExitTime.HasValue)
            {
                return DateTime.Now - ExitTime.Value;
            }
            return null;
        }
 
        public string GetCarType()
        {
            return DriverEmployeeId > 0 ? "کارمند" : "مهمان";
        }
 
        public string GetDriverOrGuestName()
        {
            if (DriverEmployee != null)
            {
                return $"{DriverEmployee.FirstName} {DriverEmployee.LastName}";
            }
            return DriverEmployeeId > 0 ? "نامشخص" : "مهمان";
        }
 
        public string GetStatusBadgeClass()
        {
            return Status.GetStatusBadgeClass();
        }
 
        public string GetStatusDisplayName()
        {
            return Status.GetDisplayName();
        }
 
        public string GetStatusIcon()
        {
            return Status.GetStatusIcon();
        }
 
        public ApplicationUser User { get; set; } = default!; // Removed ForeignKey attribute
    }
}