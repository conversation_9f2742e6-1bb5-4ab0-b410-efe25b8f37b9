using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.Extensions.Logging;

namespace EmployeeTrafficControl.Core.Services
{
    public class SystemSettingsService
    {
        private readonly ApplicationDbContext _context;

        public SystemSettingsService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// دریافت تنظیمات سیستم (همیشه یک رکورد وجود دارد)
        /// </summary>
        /// <returns>تنظیمات سیستم</returns>
        public async Task<SystemSettings> GetSystemSettingsAsync()
        {
            var settings = await _context.SystemSettings
                                         .Include(s => s.LastUpdatedByUser)
                                         .FirstOrDefaultAsync();

            if (settings == null)
            {
                // اگر تنظیمات وجود ندارد، تنظیمات پیش‌فرض ایجاد می‌کنیم
                settings = new SystemSettings();
                _context.SystemSettings.Add(settings);
                await _context.SaveChangesAsync();
            }

            return settings;
        }

        /// <summary>
        /// به‌روزرسانی تنظیمات سیستم
        /// </summary>
        /// <param name="settings">تنظیمات جدید</param>
        /// <param name="userId">شناسه کاربر به‌روزرسانی‌کننده</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> UpdateSystemSettingsAsync(SystemSettings settings, int userId)
        {
            try
            {
                var existingSettings = await _context.SystemSettings.FirstOrDefaultAsync();

                if (existingSettings == null)
                {
                    // اگر تنظیمات وجود ندارد، جدید ایجاد می‌کنیم
                    settings.LastUpdatedByUserId = userId;
                    settings.LastUpdated = DateTime.Now;
                    _context.SystemSettings.Add(settings);
                }
                else
                {
                    // به‌روزرسانی تنظیمات موجود
                    existingSettings.WorkStartTime = settings.WorkStartTime;
                    existingSettings.WorkEndTime = settings.WorkEndTime;
                    existingSettings.ThursdayWorkStartTime = settings.ThursdayWorkStartTime;
                    existingSettings.ThursdayWorkEndTime = settings.ThursdayWorkEndTime;
                    existingSettings.MaxLateMinutes = settings.MaxLateMinutes;
                    existingSettings.MaxEarlyLeaveMinutes = settings.MaxEarlyLeaveMinutes;
                    existingSettings.WorkingDays = settings.WorkingDays;
                    existingSettings.OrganizationName = settings.OrganizationName;
                    existingSettings.OrganizationAddress = settings.OrganizationAddress;
                    existingSettings.OrganizationPhone = settings.OrganizationPhone;
                    existingSettings.OrganizationEmail = settings.OrganizationEmail;
                    existingSettings.AutoRegisterEntry = settings.AutoRegisterEntry;
                    existingSettings.AutoRegisterExit = settings.AutoRegisterExit;
                    existingSettings.RequireApprovalForHourlyExit = settings.RequireApprovalForHourlyExit;
                    existingSettings.MaxHourlyExitHours = settings.MaxHourlyExitHours;
                    existingSettings.LastUpdatedByUserId = userId;
                    existingSettings.LastUpdated = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                // به‌روزرسانی ساعات کاری در DailyWorkSchedule ها
                await UpdateDailyWorkSchedulesAsync(settings, userId);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating system settings: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// بررسی اینکه آیا امروز روز کاری است یا نه
        /// </summary>
        /// <returns>true اگر امروز روز کاری باشد</returns>
        public async Task<bool> IsTodayWorkingDayAsync()
        {
            var settings = await GetSystemSettingsAsync();
            return settings.IsWorkingDay(DateTime.Today.DayOfWeek);
        }

        /// <summary>
        /// بررسی اینکه آیا زمان مشخص شده در ساعات کاری است یا نه
        /// </summary>
        /// <param name="time">زمان مورد بررسی</param>
        /// <returns>true اگر در ساعات کاری باشد</returns>
        public async Task<bool> IsWithinWorkingHoursAsync(TimeSpan time)
        {
            var settings = await GetSystemSettingsAsync();
            return settings.IsWithinWorkingHours(time);
        }

        /// <summary>
        /// بررسی تأخیر
        /// </summary>
        /// <param name="arrivalTime">زمان ورود</param>
        /// <returns>true اگر تأخیر داشته باشد</returns>
        public async Task<bool> IsLateAsync(TimeSpan arrivalTime)
        {
            var settings = await GetSystemSettingsAsync();
            return settings.IsLate(arrivalTime);
        }

        /// <summary>
        /// بررسی زودتر رفتن
        /// </summary>
        /// <param name="leaveTime">زمان خروج</param>
        /// <returns>true اگر زودتر رفته باشد</returns>
        public async Task<bool> IsEarlyLeaveAsync(TimeSpan leaveTime)
        {
            var settings = await GetSystemSettingsAsync();
            return settings.IsEarlyLeave(leaveTime);
        }

        /// <summary>
        /// محاسبه دقایق تأخیر
        /// </summary>
        /// <param name="arrivalTime">زمان ورود</param>
        /// <returns>تعداد دقایق تأخیر</returns>
        public async Task<int> CalculateLateMinutesAsync(TimeSpan arrivalTime)
        {
            var settings = await GetSystemSettingsAsync();
            if (arrivalTime <= settings.WorkStartTime)
                return 0;

            return (int)(arrivalTime - settings.WorkStartTime).TotalMinutes;
        }

        /// <summary>
        /// محاسبه دقایق زودتر رفتن
        /// </summary>
        /// <param name="leaveTime">زمان خروج</param>
        /// <returns>تعداد دقایق زودتر رفتن</returns>
        public async Task<int> CalculateEarlyLeaveMinutesAsync(TimeSpan leaveTime)
        {
            var settings = await GetSystemSettingsAsync();
            if (leaveTime >= settings.WorkEndTime)
                return 0;

            return (int)(settings.WorkEndTime - leaveTime).TotalMinutes;
        }

        /// <summary>
        /// دریافت ساعت شروع کار
        /// </summary>
        /// <returns>ساعت شروع کار</returns>
        public async Task<TimeSpan> GetWorkStartTimeAsync()
        {
            var settings = await GetSystemSettingsAsync();
            return settings.WorkStartTime;
        }

        /// <summary>
        /// دریافت ساعت پایان کار
        /// </summary>
        /// <returns>ساعت پایان کار</returns>
        public async Task<TimeSpan> GetWorkEndTimeAsync()
        {
            var settings = await GetSystemSettingsAsync();
            return settings.WorkEndTime;
        }

        /// <summary>
        /// دریافت نام سازمان
        /// </summary>
        /// <returns>نام سازمان</returns>
        public async Task<string> GetOrganizationNameAsync()
        {
            var settings = await GetSystemSettingsAsync();
            return settings.OrganizationName;
        }

        /// <summary>
        /// به‌روزرسانی ساعات کاری در DailyWorkSchedule ها بر اساس تنظیمات سیستم
        /// </summary>
        private async Task UpdateDailyWorkSchedulesAsync(SystemSettings settings, int userId)
        {
            try
            {
                var schedules = await _context.DailyWorkSchedules.ToListAsync();

                foreach (var schedule in schedules)
                {
                    if (schedule.DayOfWeek == DayOfWeek.Thursday)
                    {
                        // پنج‌شنبه
                        schedule.WorkStartTime = settings.ThursdayWorkStartTime;
                        schedule.WorkEndTime = settings.ThursdayWorkEndTime;
                    }
                    else if (schedule.DayOfWeek != DayOfWeek.Friday)
                    {
                        // شنبه تا چهارشنبه
                        schedule.WorkStartTime = settings.WorkStartTime;
                        schedule.WorkEndTime = settings.WorkEndTime;
                    }

                    schedule.MaxLateMinutes = settings.MaxLateMinutes;
                    schedule.MaxEarlyLeaveMinutes = settings.MaxEarlyLeaveMinutes;
                    schedule.LastUpdated = DateTime.Now;
                    schedule.LastUpdatedByUserId = userId;
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating daily work schedules: {ex.Message}");
            }
        }
    }
}
