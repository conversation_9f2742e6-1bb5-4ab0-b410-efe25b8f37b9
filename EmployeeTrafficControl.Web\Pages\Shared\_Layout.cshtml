@using Microsoft.AspNetCore.Identity
@using EmployeeTrafficControl.Data.Models
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
<!DOCTYPE html>
<html lang="fa" dir="rtl" class="h-100">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - سیستم کنترل تردد کارمندان</title>
    <!-- Bootstrap RTL CSS -->
    <link href="~/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Vazirmatn Font -->
    <link href="~/fonts/vazirmatn-font-face.css" rel="stylesheet" />
    <!-- Persian DatePicker CSS -->
    <link rel="stylesheet" href="~/css/persian-datepicker.css" asp-append-version="true" />

    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/dashboard.css" asp-append-version="true" />
</head>
<body class="d-flex flex-column h-100">
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-page="/Dashboard/index">سیستم کنترل تردد</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="تغییر ناوبری">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-page="/Dashboard/index">
                                <i class="bi bi-house-door"></i> داشبورد
                            </a>
                        </li>
                        @if (SignInManager.IsSignedIn(User))
                        {
                            var currentUser = await UserManager.GetUserAsync(User);
                            var userRoles = currentUser != null ? await UserManager.GetRolesAsync(currentUser) : new List<string>();
                            bool isAdmin = userRoles.Contains("Admin");
                            bool isManager = userRoles.Contains("Manager") || isAdmin;
                            bool isGuard = userRoles.Contains("Guard") || isManager;

                            <!-- منوهای اصلی برای تمام کاربران احراز هویت شده -->
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-page="/Employees/Index">
                                    <i class="bi bi-people"></i> کارمندان
                                </a>
                            </li>

                            <!-- منوی کشویی عملیات سریع -->
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="quickActionsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-lightning"></i> عملیات سریع
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="quickActionsDropdown">
                                    <li>
                                        <a class="dropdown-item" href="/traffic/dailyentry">
                                            <i class="bi bi-building"></i> ورود اولیه
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/traffic/finalexit">
                                            <i class="bi bi-door-open"></i> خروج نهایی
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/reports/presentemployees">
                                            <i class="bi bi-people"></i> کارمندان حاضر
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/reports/carstatus">
                                            <i class="bi bi-car-front-fill"></i> وضعیت خودروها
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="/reports/dailyattendance">
                                            <i class="bi bi-file-earmark-text"></i> گزارش حضور
                                        </a>
                                    </li>
                                </ul>
                            </li>

                            @if (isManager)
                            {
                                <!-- منوهای مخصوص ادمین و مدیر -->
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-page="/Buildings/Index">
                                        <i class="bi bi-building"></i> ساختمان‌ها
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-page="/Jobs/Index">
                                        <i class="bi bi-briefcase"></i> مشاغل
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-page="/Cars/Index">
                                        <i class="bi bi-car-front"></i> خودروها
                                    </a>
                                </li>
                            }

                            @if (isAdmin)
                            {
                                <!-- منوهای مخصوص ادمین -->
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-page="/Users/<USER>">
                                        <i class="bi bi-person-gear"></i> کاربران
                                    </a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-gear"></i> تنظیمات
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                                        <li>
                                            <a class="dropdown-item" asp-area="" asp-page="/Admin/SystemSettings">
                                                <i class="bi bi-gear"></i> تنظیمات سیستم
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-area="" asp-page="/Admin/DatabaseStatus">
                                                <i class="bi bi-database"></i> وضعیت دیتابیس
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-area="" asp-page="/Admin/CreateSampleData">
                                                <i class="bi bi-database-add"></i> ایجاد داده‌های نمونه
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            }
                        }
                        else
                        {
                            <!-- پیام برای کاربران غیر احراز هویت شده -->
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-page="/Account/Login">
                                    <i class="bi bi-box-arrow-in-right"></i> ورود
                                </a>
                            </li>
                        }
                    </ul>

                    <!-- User Info and Logout -->
                    @if (SignInManager.IsSignedIn(User))
                    {
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle"></i> @User.Identity?.Name
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <li>
                                        <form method="post" asp-page="/Account/Logout" class="d-inline">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi bi-box-arrow-right"></i> خروج از حساب
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>
    <div class="container-fluid flex-grow-1">
        <main role="main" class="pb-3">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="بستن"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="بستن"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle"></i>
                    @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="بستن"></button>
                </div>
            }
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted mt-auto">
        <div class="container">
            &copy; 2025 - سیستم کنترل تردد کارمندان - <a asp-area="" asp-page="/Privacy">حریم خصوصی</a>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/persian-datepicker.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
