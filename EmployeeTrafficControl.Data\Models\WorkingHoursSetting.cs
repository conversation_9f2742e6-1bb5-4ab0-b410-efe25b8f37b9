﻿using EmployeeTrafficControl.Data.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Data.Models
{
    public class WorkingHoursSetting
    {
        [Key]
        [Display(Name = "شناسه تنظیمات")]
        public int SettingId { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Required(ErrorMessage = "ساعت شروع کار اجباری است.")]
        [Column(TypeName = "time")]
        [Display(Name = "ساعت شروع کار پیش‌فرض")]
        public TimeSpan DefaultStartTime { get; set; }

        [Required(ErrorMessage = "ساعت پایان کار اجباری است.")]
        [Column(TypeName = "time")]
        [Display(Name = "ساعت پایان کار پیش‌فرض")]
        public TimeSpan DefaultEndTime { get; set; }

        [Display(Name = "تلورانس تاخیر ورود (دقیقه)")]
        public int LateEntryToleranceMinutes { get; set; } = 0;

        [Display(Name = "تلورانس تعجیل خروج (دقیقه)")]
        public int EarlyExitToleranceMinutes { get; set; } = 0;

        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;
    }
}