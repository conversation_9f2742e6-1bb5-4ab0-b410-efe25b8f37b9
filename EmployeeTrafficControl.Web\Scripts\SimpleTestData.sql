-- اسکریپت ساده برای اضافه کردن داده‌های تست
USE [Traffic]
GO

DECLARE @EmployeeId INT
DECLARE @BuildingId INT
DECLARE @UserId NVARCHAR(450)

-- دریا<PERSON>ت اولین کارمند
SELECT TOP 1 @EmployeeId = EmployeeId, @BuildingId = BuildingId 
FROM EmployeeTrafficControl.Employees 
ORDER BY EmployeeId

-- دریافت اولین کاربر
SELECT TOP 1 @UserId = Id
FROM EmployeeTrafficControl.AspNetUsers
ORDER BY Id

PRINT 'Employee ID: ' + CAST(@EmployeeId AS VARCHAR)
PRINT 'Building ID: ' + CAST(@BuildingId AS VARCHAR)
PRINT 'User ID: ' + @UserId

-- اضافه کردن خروج ساعتی
INSERT INTO EmployeeTrafficControl.TrafficLogs
(EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId, IsAutomaticExit, IsAbsence, IsLate, IsEarlyExit)
VALUES
(@EmployeeId, @BuildingId, GETDATE(), 'خروج ساعتی', 'تست خروج ساعتی', @UserId, 0, 0, 0, 0)

PRINT 'خروج ساعتی اضافه شد'

-- اضافه کردن ماموریت
INSERT INTO EmployeeTrafficControl.TrafficLogs
(EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId, IsAutomaticExit, IsAbsence, IsLate, IsEarlyExit)
VALUES
(@EmployeeId, @BuildingId, DATEADD(HOUR, 1, GETDATE()), 'ماموریت', 'تست ماموریت', @UserId, 0, 0, 0, 0)

PRINT 'ماموریت اضافه شد'

-- بررسی نتیجه
SELECT TrafficType, COUNT(*) as Count 
FROM EmployeeTrafficControl.TrafficLogs 
GROUP BY TrafficType
ORDER BY Count DESC

PRINT 'داده‌های تست اضافه شدند!'
