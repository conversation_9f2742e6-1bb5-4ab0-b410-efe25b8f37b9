@page 
@model EmployeeTrafficControl.Web.Pages.Reports.OutOfBuildingModel
@{
    ViewData["Title"] = "کارمندان خارج از ساختمان";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="bi bi-people"></i> کارمندان خارج از ساختمان
            </h2>
            <p class="text-muted mb-0">گزارش کارمندان خارج از ساختمان در تاریخ @DateTime.Today.ToString("yyyy/MM/dd")</p>
        </div>
        <div>
            <a href="/dashboard" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>

    <!-- آ<PERSON><PERSON>ر کلی -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.TotalOutOfBuilding</h3>
                    <p class="mb-0">کل خارج از ساختمان</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.HourlyExitCount</h3>
                    <p class="mb-0">خروج ساعتی</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.OfficialMissionCount</h3>
                    <p class="mb-0">ماموریت اداری</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">@Model.InVehicleCount</h3>
                    <p class="mb-0">در خودرو</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فیلتر ساختمان -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">فیلتر بر اساس ساختمان</label>
                    <select name="buildingId" class="form-select" onchange="this.form.submit()">
                        <option value="">همه ساختمان‌ها</option>
                        @foreach (var building in Model.Buildings)
                        {
                            <option value="@building.BuildingId" selected="@(building.BuildingId == Model.BuildingId)">
                                @building.Name
                            </option>
                        }
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول کارمندان -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-list"></i> لیست کارمندان خارج از ساختمان
                <span class="badge bg-warning ms-2">@Model.EmployeesOutOfBuilding.Count نفر</span>
            </h5>
        </div>
        <div class="card-body">
            @if (Model.EmployeesOutOfBuilding.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>نام و نام خانوادگی</th>
                                <th>کد پرسنلی</th>
                                <th>ساختمان</th>
                                <th>شغل</th>
                                <th>وضعیت</th>
                                <th>خودرو</th>
                                <th>زمان خروج</th>
                                <th>توضیحات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var status in Model.EmployeesOutOfBuilding.OrderBy(e => e.Employee.LastName))
                            {
                                <tr>
                                    <td>
                                        <div>
                                            <strong>@status.Employee.FirstName @status.Employee.LastName</strong>
                                            @if (!string.IsNullOrEmpty(status.Employee.PhoneNumber))
                                            {
                                                <br><small class="text-muted">@status.Employee.PhoneNumber</small>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <code>@status.Employee.PersonnelCode</code>
                                    </td>
                                    <td>
                                        @if (status.Employee.Building != null)
                                        {
                                            <span class="badge bg-info">@status.Employee.Building.Name</span>
                                        }
                                    </td>
                                    <td>
                                        @if (status.Employee.Job != null)
                                        {
                                            <span class="badge bg-secondary">@status.Employee.Job.Title</span>
                                            @if (status.Employee.HasDrivingLicense)
                                            {
                                                <span class="badge bg-warning ms-1">دارای گواهینامه</span>
                                            }
                                        }
                                    </td>
                                    <td>
                                        <span class="badge @status.GetStatusBadgeClass()">
                                            @status.GetStatusDisplayName()
                                        </span>
                                    </td>
                                    <td>
                                        @if (status.IsInVehicle && status.CurrentVehicle != null)
                                        {
                                            <span class="badge bg-dark">
                                                <i class="bi bi-car-front"></i> @status.CurrentVehicle.PlateNumber
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        <span class="text-muted">@status.LastUpdated.ToString("HH:mm")</span>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(status.Notes))
                                        {
                                            <small class="text-muted">@status.Notes</small>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center text-muted py-5">
                    <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                    @if (Model.HasAnyEmployeeEnteredToday)
                    {
                        <h4 class="mt-3">همه کارمندان در ساختمان حضور دارند</h4>
                        <p>در حال حاضر هیچ کارمندی خارج از ساختمان نیست.</p>
                    }
                    else
                    {
                        <h4 class="mt-3">هنوز هیچ کارمندی ورود اولیه ثبت نکرده است</h4>
                        <p>برای نمایش کارمندان خارج از ساختمان، ابتدا باید ورود اولیه کارمندان ثبت شود.</p>
                    }
                </div>
            }
        </div>
    </div>
</div>

<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.75rem;
    }

    .employee-item {
        padding: 0.75rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .employee-item:last-child {
        border-bottom: none;
    }

    .employee-info strong {
        color: #495057;
    }
</style>
