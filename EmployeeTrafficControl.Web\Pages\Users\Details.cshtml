@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Users.DetailsModel
@{
    ViewData["Title"] = "جزئیات کاربر";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>جزئیات کاربر</h1>
        <div>
            <a asp-page="Edit" asp-route-id="@Model.User.Id" class="btn btn-primary">
                <i class="bi bi-pencil"></i> ویرایش
            </a>
            <a asp-page="Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به لیست
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">@Model.User.UserName</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">شناسه:</dt>
                    <dd class="col-sm-9">@Model.User.Id</dd>
                    
                    <dt class="col-sm-3">نام کاربری:</dt>
                    <dd class="col-sm-9">
                        <strong>@Model.User.UserName</strong>
                    </dd>
                    
                    <dt class="col-sm-3">نقش:</dt>
                    <dd class="col-sm-9">
                        @if (Model.User.Role?.Code == "ADMIN")
                        {
                            <span class="badge bg-danger">مدیر سیستم</span>
                            <p class="mb-0 mt-2 text-muted">دسترسی کامل به تمام بخش‌های سیستم</p>
                        }
                        else if (Model.User.Role?.Code == "MANAGER")
                        {
                            <span class="badge bg-warning">مدیر ساختمان</span>
                            <p class="mb-0 mt-2 text-muted">مدیریت ساختمان و کارمندان مربوطه</p>
                        }
                        else if (Model.User.Role?.Code == "GUARD")
                        {
                            <span class="badge bg-info">نگهبان</span>
                            <p class="mb-0 mt-2 text-muted">کنترل تردد و ثبت ورود و خروج</p>
                        }
                        else
                        {
                            <span class="badge bg-secondary">@(Model.User.Role?.Name ?? "نامشخص")</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">کارمند مرتبط:</dt>
                    <dd class="col-sm-9">
                        @if (Model.User.Employee != null)
                        {
                            <div class="d-flex align-items-center">
                                <span class="me-2">@Model.User.Employee.FirstName @Model.User.Employee.LastName</span>
                                <span class="badge bg-light text-dark">@Model.User.Employee.PersonnelCode</span>
                                <a asp-page="/Employees/Details" asp-route-id="@Model.User.Employee.EmployeeId" 
                                   class="btn btn-sm btn-outline-info ms-2">
                                    <i class="bi bi-eye"></i> مشاهده
                                </a>
                            </div>
                        }
                        else
                        {
                            <span class="text-muted">بدون کارمند مرتبط</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">ساختمان مرتبط:</dt>
                    <dd class="col-sm-9">
                        @if (Model.User.Building != null)
                        {
                            <div class="d-flex align-items-center">
                                <span class="badge bg-info me-2">@Model.User.Building.Name</span>
                                <a asp-page="/Buildings/Details" asp-route-id="@Model.User.Building.BuildingId" 
                                   class="btn btn-sm btn-outline-info">
                                    <i class="bi bi-eye"></i> مشاهده
                                </a>
                            </div>
                            <p class="mb-0 mt-2 text-muted">دسترسی محدود به این ساختمان</p>
                        }
                        else
                        {
                            <span class="text-muted">دسترسی به همه ساختمان‌ها</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">عملیات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-page="Edit" asp-route-id="@Model.User.Id" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> ویرایش کاربر
                    </a>
                    
                    @if (Model.User.Employee == null)
                    {
                        <a asp-page="/Employees/Create" class="btn btn-success">
                            <i class="bi bi-person-plus"></i> ایجاد کارمند برای این کاربر
                        </a>
                    }
                    
                    <hr>
                    <a asp-page="Delete" asp-route-id="@Model.User.Id" class="btn btn-danger w-100">
                        <i class="bi bi-trash"></i> حذف کاربر
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">خلاصه دسترسی‌ها</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        @if (Model.User.Role?.Code == "ADMIN")
                        {
                            <h4 class="text-danger">مدیر سیستم</h4>
                            <p class="mb-0">دسترسی کامل</p>
                        }
                        else if (Model.User.Role?.Code == "MANAGER")
                        {
                            <h4 class="text-warning">مدیر ساختمان</h4>
                            <p class="mb-0">دسترسی محدود</p>
                        }
                        else
                        {
                            <h4 class="text-info">@(Model.User.Role?.Name ?? "کاربر")</h4>
                            <p class="mb-0">دسترسی عادی</p>
                        }
                    </div>
                </div>
                
                <hr>
                <ul class="list-unstyled">
                    @if (Model.User.Role?.Code == "ADMIN")
                    {
                        <li><i class="bi bi-check-circle text-success"></i> مدیریت کاربران</li>
                        <li><i class="bi bi-check-circle text-success"></i> مدیریت ساختمان‌ها</li>
                        <li><i class="bi bi-check-circle text-success"></i> مدیریت کارمندان</li>
                        <li><i class="bi bi-check-circle text-success"></i> گزارش‌گیری</li>
                    }
                    else if (Model.User.Role?.Code == "MANAGER")
                    {
                        <li><i class="bi bi-check-circle text-warning"></i> مدیریت کارمندان</li>
                        <li><i class="bi bi-check-circle text-warning"></i> کنترل تردد</li>
                        <li><i class="bi bi-x-circle text-muted"></i> مدیریت کاربران</li>
                    }
                    else
                    {
                        <li><i class="bi bi-check-circle text-info"></i> مشاهده اطلاعات</li>
                        <li><i class="bi bi-x-circle text-muted"></i> ویرایش اطلاعات</li>
                    }
                </ul>
            </div>
        </div>
    </div>
</div>
