using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    /// <summary>
    /// مدل ارتباط نقش‌ها با مجوزها
    /// </summary>
    public class RolePermission
    {
        [Key]
        [Display(Name = "شناسه")]
        public int RolePermissionId { get; set; }

        [Required]
        [Display(Name = "شناسه نقش")]
        public int RoleId { get; set; }

        [Required]
        [Display(Name = "شناسه مجوز")]
        public int PermissionId { get; set; }

        [Display(Name = "تاریخ اعطا")]
        public DateTime GrantedAt { get; set; } = DateTime.Now;

        [Display(Name = "اعطا شده توسط")]
        public int? GrantedByUserId { get; set; }

        // Navigation properties
        [ForeignKey("RoleId")]
        [Display(Name = "نقش")]
        public Role Role { get; set; }

        [ForeignKey("PermissionId")]
        [Display(Name = "مجوز")]
        public Permission Permission { get; set; }

        [ForeignKey("GrantedByUserId")]
        [Display(Name = "اعطا کننده")]
        public ApplicationUser? GrantedByUser { get; set; }
    }
}
