using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    /// <summary>
    /// مدل برنامه کاری روزانه
    /// </summary>
    public class DailyWorkSchedule
    {
        [Key]
        public int ScheduleId { get; set; }

        /// <summary>
        /// روز هفته (0 = یکشنبه، 1 = دوشنبه، ... ، 6 = شنبه)
        /// </summary>
        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        /// <summary>
        /// نام روز به فارسی
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string DayNamePersian { get; set; } = string.Empty;

        /// <summary>
        /// آیا این روز کاری است؟
        /// </summary>
        public bool IsWorkingDay { get; set; }

        /// <summary>
        /// ساعت شروع کار
        /// </summary>
        public TimeSpan? WorkStartTime { get; set; }

        /// <summary>
        /// ساعت پایان کار
        /// </summary>
        public TimeSpan? WorkEndTime { get; set; }



        /// <summary>
        /// حداکثر دقیقه تاخیر مجاز
        /// </summary>
        public int MaxLateMinutes { get; set; } = 15;

        /// <summary>
        /// حداکثر دقیقه خروج زودهنگام مجاز
        /// </summary>
        public int MaxEarlyLeaveMinutes { get; set; } = 15;

        /// <summary>
        /// آخرین به‌روزرسانی
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// شناسه کاربری که آخرین تغییر را انجام داده
        /// </summary>
        public int? LastUpdatedByUserId { get; set; }

        /// <summary>
        /// کاربری که آخرین تغییر را انجام داده
        /// </summary>
        public ApplicationUser? LastUpdatedByUser { get; set; }

        /// <summary>
        /// دریافت نام روز به فارسی
        /// </summary>
        public static string GetPersianDayName(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Saturday => "شنبه",
                DayOfWeek.Sunday => "یکشنبه",
                DayOfWeek.Monday => "دوشنبه",
                DayOfWeek.Tuesday => "سه‌شنبه",
                DayOfWeek.Wednesday => "چهارشنبه",
                DayOfWeek.Thursday => "پنج‌شنبه",
                DayOfWeek.Friday => "جمعه",
                _ => "نامشخص"
            };
        }

        /// <summary>
        /// بررسی اینکه آیا ساعت داده شده در بازه کاری است
        /// </summary>
        public bool IsWithinWorkingHours(TimeSpan time)
        {
            if (!IsWorkingDay || !WorkStartTime.HasValue || !WorkEndTime.HasValue)
                return false;

            return time >= WorkStartTime.Value && time <= WorkEndTime.Value;
        }

        /// <summary>
        /// محاسبه دقایق تاخیر
        /// </summary>
        public int CalculateLateMinutes(TimeSpan arrivalTime)
        {
            if (!IsWorkingDay || !WorkStartTime.HasValue)
                return 0;

            if (arrivalTime <= WorkStartTime.Value)
                return 0;

            return (int)(arrivalTime - WorkStartTime.Value).TotalMinutes;
        }

        /// <summary>
        /// محاسبه دقایق خروج زودهنگام
        /// </summary>
        public int CalculateEarlyLeaveMinutes(TimeSpan departureTime)
        {
            if (!IsWorkingDay || !WorkEndTime.HasValue)
                return 0;

            if (departureTime >= WorkEndTime.Value)
                return 0;

            return (int)(WorkEndTime.Value - departureTime).TotalMinutes;
        }
    }
}
