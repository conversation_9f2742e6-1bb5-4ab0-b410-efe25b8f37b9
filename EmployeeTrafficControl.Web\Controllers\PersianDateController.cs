using Microsoft.AspNetCore.Mvc;
using EmployeeTrafficControl.Web.Helpers;

namespace EmployeeTrafficControl.Web.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PersianDateController : ControllerBase
    {
        /// <summary>
        /// تبدیل تاریخ میلادی به شمسی
        /// </summary>
        [HttpPost("ToPersian")]
        public IActionResult ToPersian([FromBody] DateRequest request)
        {
            try
            {
                if (request?.Date == null)
                    return BadRequest("تاریخ نامعتبر است");

                var persianDate = PersianDateHelper.ToPersianDate(request.Date);
                return Ok(persianDate);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطا در تبدیل تاریخ: {ex.Message}");
            }
        }

        /// <summary>
        /// تبدیل تاریخ شمسی به میلادی
        /// </summary>
        [HttpPost("ToGregorian")]
        public IActionResult ToGregorian([FromBody] PersianDateRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request?.PersianDate))
                    return BadRequest("تاریخ شمسی نامعتبر است");

                var gregorianDate = PersianDateHelper.ToGregorianDate(request.PersianDate);
                if (!gregorianDate.HasValue)
                    return BadRequest("تاریخ شمسی نامعتبر است");

                return Ok(gregorianDate.Value.ToString("yyyy-MM-dd"));
            }
            catch (Exception ex)
            {
                return BadRequest($"خطا در تبدیل تاریخ: {ex.Message}");
            }
        }

        /// <summary>
        /// اعتبارسنجی تاریخ شمسی
        /// </summary>
        [HttpPost("Validate")]
        public IActionResult ValidatePersianDate([FromBody] PersianDateRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request?.PersianDate))
                    return Ok(new { isValid = false, message = "تاریخ خالی است" });

                var isValid = PersianDateHelper.IsValidPersianDate(request.PersianDate);
                return Ok(new { isValid, message = isValid ? "تاریخ معتبر است" : "تاریخ نامعتبر است" });
            }
            catch (Exception ex)
            {
                return Ok(new { isValid = false, message = $"خطا: {ex.Message}" });
            }
        }

        /// <summary>
        /// دریافت تاریخ امروز شمسی
        /// </summary>
        [HttpGet("Today")]
        public IActionResult GetTodayPersian()
        {
            try
            {
                var today = PersianDateHelper.TodayPersian;
                return Ok(today);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطا: {ex.Message}");
            }
        }

        /// <summary>
        /// دریافت تاریخ کامل شمسی
        /// </summary>
        [HttpPost("FullDate")]
        public IActionResult GetFullPersianDate([FromBody] DateRequest request)
        {
            try
            {
                if (request?.Date == null)
                    return BadRequest("تاریخ نامعتبر است");

                var fullDate = PersianDateHelper.ToFullPersianDate(request.Date);
                return Ok(fullDate);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطا: {ex.Message}");
            }
        }
    }

    // مدل‌های درخواست
    public class DateRequest
    {
        public DateTime? Date { get; set; }
    }

    public class PersianDateRequest
    {
        public string? PersianDate { get; set; }
    }
}
