@page "/Reports/CarDetailedTraffic"
@model EmployeeTrafficControl.Web.Pages.Reports.CarDetailedTrafficModel
@{
    ViewData["Title"] = "گزارش تفصیلی تردد خودرو";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-car-front-fill"></i> گزارش تفصیلی تردد خودرو
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/Dashboard">داشبورد</a></li>
                        <li class="breadcrumb-item"><a href="/Reports">گزارشات</a></li>
                        <li class="breadcrumb-item active">تردد تفصیلی خودرو</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- فیلترها -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel"></i> فیلترهای جستجو
            </h5>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" onchange="this.form.submit()">
                            <option value="">همه ساختمان‌ها</option>
                            @foreach (var building in Model.AvailableBuildings)
                            {
                                <option value="@building.BuildingId" selected="@(Model.BuildingId == building.BuildingId)">
                                    @building.Name
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">جستجوی خودرو</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="پلاک، مدل یا برند">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">از تاریخ</label>
                        <input type="date" name="FromDate" value="@Model.FromDate?.ToString("yyyy-MM-dd")" class="form-control">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">تا تاریخ</label>
                        <input type="date" name="ToDate" value="@Model.ToDate?.ToString("yyyy-MM-dd")" class="form-control">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="bi bi-search"></i> جستجو
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- انتخاب خودرو -->
    @if (Model.AvailableCars.Any())
    {
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-car-front"></i> انتخاب خودرو
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach (var car in Model.AvailableCars.Take(20))
                    {
                        <div class="col-md-6 col-lg-4 mb-2">
                            <a href="?CarId=@car.CarId&FromDate=@Model.FromDate?.ToString("yyyy-MM-dd")&ToDate=@Model.ToDate?.ToString("yyyy-MM-dd")&BuildingId=@Model.BuildingId&SearchTerm=@Model.SearchTerm" 
                               class="btn btn-outline-primary w-100 text-start @(Model.CarId == car.CarId ? "active" : "")">
                                <div>
                                    <strong>@car.PlateNumber</strong>
                                    <small class="d-block text-muted">@car.Type @car.Model</small>
                                    <small class="d-block">
                                        <span class="badge @(car.IsInParking ? "bg-success" : "bg-warning")">
                                            @(car.IsInParking ? "در پارکینگ" : "خارج از پارکینگ")
                                        </span>
                                        @if (car.IsMoneyTransport)
                                        {
                                            <span class="badge bg-info">پولرسان</span>
                                        }
                                    </small>
                                </div>
                            </a>
                        </div>
                    }
                </div>
                @if (Model.AvailableCars.Count > 20)
                {
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle"></i>
                        @(Model.AvailableCars.Count - 20) خودرو دیگر یافت شد. لطفاً جستجو را محدودتر کنید.
                    </div>
                }
            </div>
        </div>
    }

    <!-- نتایج گزارش -->
    @if (Model.ReportData != null)
    {
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> گزارش تفصیلی - @Model.ReportData.Car?.PlateNumber
                </h5>
                <form method="post" asp-page-handler="Export" class="d-inline">
                    <input type="hidden" name="CarId" value="@Model.CarId" />
                    <input type="hidden" name="FromDate" value="@Model.FromDate?.ToString("yyyy-MM-dd")" />
                    <input type="hidden" name="ToDate" value="@Model.ToDate?.ToString("yyyy-MM-dd")" />
                    <button type="submit" class="btn btn-success btn-sm">
                        <i class="bi bi-download"></i> خروجی Excel
                    </button>
                </form>
            </div>
            <div class="card-body">
                <!-- آمار کلی -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.TotalTrips</h4>
                                <small>تعداد سفر</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.TotalExitTime.ToString(@"dd\:hh\:mm")</h4>
                                <small>مدت خروج</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.AveragePassengerCount.ToString("F1")</h4>
                                <small>میانگین سرنشین</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4>@Model.ReportData.DriverStatistics.Count</h4>
                                <small>تعداد راننده</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آمار بر اساس نوع خروج -->
                @if (Model.ReportData.ExitTypeStatistics.Any())
                {
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6><i class="bi bi-pie-chart"></i> آمار بر اساس نوع خروج</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>نوع خروج</th>
                                            <th>تعداد</th>
                                            <th>مدت کل</th>
                                            <th>درصد</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var stat in Model.ReportData.ExitTypeStatistics.OrderByDescending(s => s.Count))
                                        {
                                            var percentage = Model.ReportData.TotalTrips > 0 ? (stat.Count * 100.0 / Model.ReportData.TotalTrips) : 0;
                                            <tr>
                                                <td>@stat.DisplayName</td>
                                                <td><span class="badge bg-primary">@stat.Count</span></td>
                                                <td>@stat.TotalDuration.ToString(@"dd\:hh\:mm")</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar" style="width: @percentage%">
                                                            @percentage.ToString("F1")%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }

                <!-- آمار رانندگان -->
                @if (Model.ReportData.DriverStatistics.Any())
                {
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6><i class="bi bi-person-gear"></i> آمار رانندگان</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>راننده</th>
                                            <th>تعداد سفر</th>
                                            <th>مدت کل</th>
                                            <th>درصد</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var stat in Model.ReportData.DriverStatistics.OrderByDescending(s => s.TripCount))
                                        {
                                            var percentage = Model.ReportData.TotalTrips > 0 ? (stat.TripCount * 100.0 / Model.ReportData.TotalTrips) : 0;
                                            <tr>
                                                <td>@stat.DriverName</td>
                                                <td><span class="badge bg-success">@stat.TripCount</span></td>
                                                <td>@stat.TotalDuration.ToString(@"dd\:hh\:mm")</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-success" style="width: @percentage%">
                                                            @percentage.ToString("F1")%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }

                <!-- جدول تفصیلی سفرها -->
                <h6><i class="bi bi-list-ul"></i> تفصیل سفرها</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>تاریخ</th>
                                <th>راننده</th>
                                <th>خروج</th>
                                <th>ورود</th>
                                <th>مدت</th>
                                <th>نوع</th>
                                <th>سرنشینان</th>
                                <th>توضیحات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var log in Model.ReportData.TrafficLogs.OrderByDescending(l => l.ExitTime ?? l.EntryTime))
                            {
                                <tr>
                                    <td>
                                        @if (log.ExitTime.HasValue)
                                        {
                                            <span>@log.ExitTime.Value.ToString("yyyy/MM/dd")</span>
                                        }
                                        else if (log.EntryTime.HasValue)
                                        {
                                            <span>@log.EntryTime.Value.ToString("yyyy/MM/dd")</span>
                                        }
                                    </td>
                                    <td>
                                        @if (log.DriverEmployee != null)
                                        {
                                            <span>@log.DriverEmployee.FirstName @log.DriverEmployee.LastName</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">نامشخص</span>
                                        }
                                    </td>
                                    <td>
                                        @if (log.ExitTime.HasValue)
                                        {
                                            <span class="badge bg-danger">@log.ExitTime.Value.ToString("HH:mm")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (log.EntryTime.HasValue)
                                        {
                                            <span class="badge bg-success">@log.EntryTime.Value.ToString("HH:mm")</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">در حال سفر</span>
                                        }
                                    </td>
                                    <td>
                                        @if (log.GetParkingDuration().HasValue)
                                        {
                                            <span>@log.GetParkingDuration().Value.ToString(@"hh\:mm")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(log.ExitType))
                                        {
                                            <span class="badge bg-info">@log.ExitType</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">@log.CarPassengers.Count</span>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(log.Notes))
                                        {
                                            <span title="@log.Notes">@(log.Notes.Length > 30 ? log.Notes.Substring(0, 30) + "..." : log.Notes)</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else if (Model.CarId.HasValue)
    {
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            هیچ رکورد ترددی برای خودرو انتخاب شده در بازه زمانی مشخص شده یافت نشد.
        </div>
    }
    else if (!Model.AvailableCars.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            لطفاً ابتدا فیلترهای جستجو را تنظیم کنید تا لیست خودروها نمایش داده شود.
        </div>
    }
</div>
