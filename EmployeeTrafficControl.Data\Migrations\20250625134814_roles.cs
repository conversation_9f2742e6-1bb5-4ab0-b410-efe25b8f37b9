﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class roles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CarKilometers_Users_CreatedByUserId",
                table: "CarKilometers");

            migrationBuilder.DropForeignKey(
                name: "FK_DailyAttendances_Users_ApprovedByUserId",
                table: "DailyAttendances");

            migrationBuilder.DropForeignKey(
                name: "FK_DailyAttendances_Users_RegisteredByUserId",
                table: "DailyAttendances");

            migrationBuilder.DropForeignKey(
                name: "FK_DailyWorkSchedules_Users_LastUpdatedByUserId",
                table: "DailyWorkSchedules");

            migrationBuilder.DropForeignKey(
                name: "FK_EmployeeStatuses_Users_UpdatedByUserId",
                table: "EmployeeStatuses");

            migrationBuilder.DropForeignKey(
                name: "FK_RolePermissions_Users_GrantedByUserId",
                table: "RolePermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_SystemSettings_Users_LastUpdatedByUserId",
                table: "SystemSettings");

            migrationBuilder.DropForeignKey(
                name: "FK_UserPermissions_Users_GrantedByUserId",
                table: "UserPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserPermissions_Users_UserId",
                table: "UserPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserSessions_Users_UserId",
                table: "UserSessions");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_EmployeeId",
                table: "AspNetUsers");

            migrationBuilder.EnsureSchema(
                name: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "WorkingHoursSetting",
                newName: "WorkingHoursSetting",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "UserSessions",
                newName: "UserSessions",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "UserPermissions",
                newName: "UserPermissions",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "TrafficLogs",
                newName: "TrafficLogs",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "SystemSettings",
                newName: "SystemSettings",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "Roles",
                newName: "Roles",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "RolePermissions",
                newName: "RolePermissions",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "Permissions",
                newName: "Permissions",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "Jobs",
                newName: "Jobs",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "GuestCarTrafficLogs",
                newName: "GuestCarTrafficLogs",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "EmployeeWorkingHours",
                newName: "EmployeeWorkingHours",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "EmployeeStatuses",
                newName: "EmployeeStatuses",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "Employees",
                newName: "Employees",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "DailyWorkSchedules",
                newName: "DailyWorkSchedules",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "DailyAttendances",
                newName: "DailyAttendances",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "CarTrafficLogs",
                newName: "CarTrafficLogs",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "Cars",
                newName: "Cars",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "CarPassengers",
                newName: "CarPassengers",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "CarKilometers",
                newName: "CarKilometers",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "Buildings",
                newName: "Buildings",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetUserTokens",
                newName: "AspNetUserTokens",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetUsers",
                newName: "AspNetUsers",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetUserRoles",
                newName: "AspNetUserRoles",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetUserLogins",
                newName: "AspNetUserLogins",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetUserClaims",
                newName: "AspNetUserClaims",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetRoles",
                newName: "AspNetRoles",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.RenameTable(
                name: "AspNetRoleClaims",
                newName: "AspNetRoleClaims",
                newSchema: "EmployeeTrafficControl");

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "TrafficLogs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "EmployeeTrafficControl",
                table: "Jobs",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "GuestCarTrafficLogs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeWorkingHours",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "CarTrafficLogs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "RoleId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TrafficLogs_UserId",
                schema: "EmployeeTrafficControl",
                table: "TrafficLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GuestCarTrafficLogs_UserId",
                schema: "EmployeeTrafficControl",
                table: "GuestCarTrafficLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeWorkingHours_UserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeWorkingHours",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CarTrafficLogs_UserId",
                schema: "EmployeeTrafficControl",
                table: "CarTrafficLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_EmployeeId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers",
                column: "EmployeeId",
                unique: true,
                filter: "[EmployeeId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_RoleId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers",
                column: "UserName",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUsers_Roles_RoleId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers",
                column: "RoleId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "Roles",
                principalColumn: "RoleId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CarKilometers_AspNetUsers_CreatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "CarKilometers",
                column: "CreatedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CarTrafficLogs_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "CarTrafficLogs",
                column: "UserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DailyAttendances_AspNetUsers_ApprovedByUserId",
                schema: "EmployeeTrafficControl",
                table: "DailyAttendances",
                column: "ApprovedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_DailyAttendances_AspNetUsers_RegisteredByUserId",
                schema: "EmployeeTrafficControl",
                table: "DailyAttendances",
                column: "RegisteredByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DailyWorkSchedules_AspNetUsers_LastUpdatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "DailyWorkSchedules",
                column: "LastUpdatedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_EmployeeStatuses_AspNetUsers_UpdatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeStatuses",
                column: "UpdatedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_EmployeeWorkingHours_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeWorkingHours",
                column: "UserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_GuestCarTrafficLogs_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "GuestCarTrafficLogs",
                column: "UserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_RolePermissions_AspNetUsers_GrantedByUserId",
                schema: "EmployeeTrafficControl",
                table: "RolePermissions",
                column: "GrantedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SystemSettings_AspNetUsers_LastUpdatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "SystemSettings",
                column: "LastUpdatedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TrafficLogs_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "TrafficLogs",
                column: "UserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserPermissions_AspNetUsers_GrantedByUserId",
                schema: "EmployeeTrafficControl",
                table: "UserPermissions",
                column: "GrantedByUserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_UserPermissions_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "UserPermissions",
                column: "UserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserSessions_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "UserSessions",
                column: "UserId",
                principalSchema: "EmployeeTrafficControl",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUsers_Roles_RoleId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_CarKilometers_AspNetUsers_CreatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "CarKilometers");

            migrationBuilder.DropForeignKey(
                name: "FK_CarTrafficLogs_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "CarTrafficLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_DailyAttendances_AspNetUsers_ApprovedByUserId",
                schema: "EmployeeTrafficControl",
                table: "DailyAttendances");

            migrationBuilder.DropForeignKey(
                name: "FK_DailyAttendances_AspNetUsers_RegisteredByUserId",
                schema: "EmployeeTrafficControl",
                table: "DailyAttendances");

            migrationBuilder.DropForeignKey(
                name: "FK_DailyWorkSchedules_AspNetUsers_LastUpdatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "DailyWorkSchedules");

            migrationBuilder.DropForeignKey(
                name: "FK_EmployeeStatuses_AspNetUsers_UpdatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeStatuses");

            migrationBuilder.DropForeignKey(
                name: "FK_EmployeeWorkingHours_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeWorkingHours");

            migrationBuilder.DropForeignKey(
                name: "FK_GuestCarTrafficLogs_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "GuestCarTrafficLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_RolePermissions_AspNetUsers_GrantedByUserId",
                schema: "EmployeeTrafficControl",
                table: "RolePermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_SystemSettings_AspNetUsers_LastUpdatedByUserId",
                schema: "EmployeeTrafficControl",
                table: "SystemSettings");

            migrationBuilder.DropForeignKey(
                name: "FK_TrafficLogs_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "TrafficLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_UserPermissions_AspNetUsers_GrantedByUserId",
                schema: "EmployeeTrafficControl",
                table: "UserPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserPermissions_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "UserPermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserSessions_AspNetUsers_UserId",
                schema: "EmployeeTrafficControl",
                table: "UserSessions");

            migrationBuilder.DropIndex(
                name: "IX_TrafficLogs_UserId",
                schema: "EmployeeTrafficControl",
                table: "TrafficLogs");

            migrationBuilder.DropIndex(
                name: "IX_GuestCarTrafficLogs_UserId",
                schema: "EmployeeTrafficControl",
                table: "GuestCarTrafficLogs");

            migrationBuilder.DropIndex(
                name: "IX_EmployeeWorkingHours_UserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeWorkingHours");

            migrationBuilder.DropIndex(
                name: "IX_CarTrafficLogs_UserId",
                schema: "EmployeeTrafficControl",
                table: "CarTrafficLogs");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_EmployeeId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_RoleId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "TrafficLogs");

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "EmployeeTrafficControl",
                table: "Jobs");

            migrationBuilder.DropColumn(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "GuestCarTrafficLogs");

            migrationBuilder.DropColumn(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "EmployeeWorkingHours");

            migrationBuilder.DropColumn(
                name: "UserId",
                schema: "EmployeeTrafficControl",
                table: "CarTrafficLogs");

            migrationBuilder.DropColumn(
                name: "RoleId",
                schema: "EmployeeTrafficControl",
                table: "AspNetUsers");

            migrationBuilder.RenameTable(
                name: "WorkingHoursSetting",
                schema: "EmployeeTrafficControl",
                newName: "WorkingHoursSetting");

            migrationBuilder.RenameTable(
                name: "UserSessions",
                schema: "EmployeeTrafficControl",
                newName: "UserSessions");

            migrationBuilder.RenameTable(
                name: "UserPermissions",
                schema: "EmployeeTrafficControl",
                newName: "UserPermissions");

            migrationBuilder.RenameTable(
                name: "TrafficLogs",
                schema: "EmployeeTrafficControl",
                newName: "TrafficLogs");

            migrationBuilder.RenameTable(
                name: "SystemSettings",
                schema: "EmployeeTrafficControl",
                newName: "SystemSettings");

            migrationBuilder.RenameTable(
                name: "Roles",
                schema: "EmployeeTrafficControl",
                newName: "Roles");

            migrationBuilder.RenameTable(
                name: "RolePermissions",
                schema: "EmployeeTrafficControl",
                newName: "RolePermissions");

            migrationBuilder.RenameTable(
                name: "Permissions",
                schema: "EmployeeTrafficControl",
                newName: "Permissions");

            migrationBuilder.RenameTable(
                name: "Jobs",
                schema: "EmployeeTrafficControl",
                newName: "Jobs");

            migrationBuilder.RenameTable(
                name: "GuestCarTrafficLogs",
                schema: "EmployeeTrafficControl",
                newName: "GuestCarTrafficLogs");

            migrationBuilder.RenameTable(
                name: "EmployeeWorkingHours",
                schema: "EmployeeTrafficControl",
                newName: "EmployeeWorkingHours");

            migrationBuilder.RenameTable(
                name: "EmployeeStatuses",
                schema: "EmployeeTrafficControl",
                newName: "EmployeeStatuses");

            migrationBuilder.RenameTable(
                name: "Employees",
                schema: "EmployeeTrafficControl",
                newName: "Employees");

            migrationBuilder.RenameTable(
                name: "DailyWorkSchedules",
                schema: "EmployeeTrafficControl",
                newName: "DailyWorkSchedules");

            migrationBuilder.RenameTable(
                name: "DailyAttendances",
                schema: "EmployeeTrafficControl",
                newName: "DailyAttendances");

            migrationBuilder.RenameTable(
                name: "CarTrafficLogs",
                schema: "EmployeeTrafficControl",
                newName: "CarTrafficLogs");

            migrationBuilder.RenameTable(
                name: "Cars",
                schema: "EmployeeTrafficControl",
                newName: "Cars");

            migrationBuilder.RenameTable(
                name: "CarPassengers",
                schema: "EmployeeTrafficControl",
                newName: "CarPassengers");

            migrationBuilder.RenameTable(
                name: "CarKilometers",
                schema: "EmployeeTrafficControl",
                newName: "CarKilometers");

            migrationBuilder.RenameTable(
                name: "Buildings",
                schema: "EmployeeTrafficControl",
                newName: "Buildings");

            migrationBuilder.RenameTable(
                name: "AspNetUserTokens",
                schema: "EmployeeTrafficControl",
                newName: "AspNetUserTokens");

            migrationBuilder.RenameTable(
                name: "AspNetUsers",
                schema: "EmployeeTrafficControl",
                newName: "AspNetUsers");

            migrationBuilder.RenameTable(
                name: "AspNetUserRoles",
                schema: "EmployeeTrafficControl",
                newName: "AspNetUserRoles");

            migrationBuilder.RenameTable(
                name: "AspNetUserLogins",
                schema: "EmployeeTrafficControl",
                newName: "AspNetUserLogins");

            migrationBuilder.RenameTable(
                name: "AspNetUserClaims",
                schema: "EmployeeTrafficControl",
                newName: "AspNetUserClaims");

            migrationBuilder.RenameTable(
                name: "AspNetRoles",
                schema: "EmployeeTrafficControl",
                newName: "AspNetRoles");

            migrationBuilder.RenameTable(
                name: "AspNetRoleClaims",
                schema: "EmployeeTrafficControl",
                newName: "AspNetRoleClaims");

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "AspNetUsers",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BuildingId = table.Column<int>(type: "int", nullable: true),
                    EmployeeId = table.Column<int>(type: "int", nullable: true),
                    RoleId = table.Column<int>(type: "int", nullable: false),
                    BuildingId1 = table.Column<int>(type: "int", nullable: true),
                    EmployeeId1 = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    PasswordHash = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    RoleId1 = table.Column<int>(type: "int", nullable: true),
                    Username = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.UserId);
                    table.ForeignKey(
                        name: "FK_Users_Buildings_BuildingId",
                        column: x => x.BuildingId,
                        principalTable: "Buildings",
                        principalColumn: "BuildingId");
                    table.ForeignKey(
                        name: "FK_Users_Buildings_BuildingId1",
                        column: x => x.BuildingId1,
                        principalTable: "Buildings",
                        principalColumn: "BuildingId");
                    table.ForeignKey(
                        name: "FK_Users_Employees_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "Employees",
                        principalColumn: "EmployeeId");
                    table.ForeignKey(
                        name: "FK_Users_Employees_EmployeeId1",
                        column: x => x.EmployeeId1,
                        principalTable: "Employees",
                        principalColumn: "EmployeeId");
                    table.ForeignKey(
                        name: "FK_Users_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Users_Roles_RoleId1",
                        column: x => x.RoleId1,
                        principalTable: "Roles",
                        principalColumn: "RoleId");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_EmployeeId",
                table: "AspNetUsers",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_BuildingId",
                table: "Users",
                column: "BuildingId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_BuildingId1",
                table: "Users",
                column: "BuildingId1");

            migrationBuilder.CreateIndex(
                name: "IX_Users_EmployeeId",
                table: "Users",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_EmployeeId1",
                table: "Users",
                column: "EmployeeId1",
                unique: true,
                filter: "[EmployeeId1] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId",
                table: "Users",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId1",
                table: "Users",
                column: "RoleId1");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "Users",
                column: "Username",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CarKilometers_Users_CreatedByUserId",
                table: "CarKilometers",
                column: "CreatedByUserId",
                principalTable: "Users",
                principalColumn: "UserId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DailyAttendances_Users_ApprovedByUserId",
                table: "DailyAttendances",
                column: "ApprovedByUserId",
                principalTable: "Users",
                principalColumn: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_DailyAttendances_Users_RegisteredByUserId",
                table: "DailyAttendances",
                column: "RegisteredByUserId",
                principalTable: "Users",
                principalColumn: "UserId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_DailyWorkSchedules_Users_LastUpdatedByUserId",
                table: "DailyWorkSchedules",
                column: "LastUpdatedByUserId",
                principalTable: "Users",
                principalColumn: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_EmployeeStatuses_Users_UpdatedByUserId",
                table: "EmployeeStatuses",
                column: "UpdatedByUserId",
                principalTable: "Users",
                principalColumn: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_RolePermissions_Users_GrantedByUserId",
                table: "RolePermissions",
                column: "GrantedByUserId",
                principalTable: "Users",
                principalColumn: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_SystemSettings_Users_LastUpdatedByUserId",
                table: "SystemSettings",
                column: "LastUpdatedByUserId",
                principalTable: "Users",
                principalColumn: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserPermissions_Users_GrantedByUserId",
                table: "UserPermissions",
                column: "GrantedByUserId",
                principalTable: "Users",
                principalColumn: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserPermissions_Users_UserId",
                table: "UserPermissions",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "UserId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserSessions_Users_UserId",
                table: "UserSessions",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "UserId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
