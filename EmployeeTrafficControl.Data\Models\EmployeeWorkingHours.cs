﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class EmployeeWorkingHours
    {
        [Key]
        [Display(Name = "شناسه ساعات کاری شناور")]
        public int EmployeeWorkingHoursId { get; set; }

        [Display(Name = "کارمند")]
        public int EmployeeId { get; set; }

        [Required(ErrorMessage = "تاریخ شروع اجباری است.")]
        [Column(TypeName = "date")]
        [Display(Name = "تاریخ شروع")]
        public DateTime StartDate { get; set; }

        [Column(TypeName = "date")]
        [Display(Name = "تاریخ پایان")]
        public DateTime? EndDate { get; set; }

        [Required(ErrorMessage = "ساعت شروع سفارشی اجباری است.")]
        [Column(TypeName = "time")]
        [Display(Name = "ساعت شروع سفارشی")]
        public TimeSpan CustomStartTime { get; set; }

        [Required(ErrorMessage = "ساعت پایان سفارشی اجباری است.")]
        [Column(TypeName = "time")]
        [Display(Name = "ساعت پایان سفارشی")]
        public TimeSpan CustomEndTime { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "کارمند")]
        public Employee Employee { get; set; } = default!;

        [Display(Name = "کاربر")]
        public ApplicationUser User { get; set; }
    }
}