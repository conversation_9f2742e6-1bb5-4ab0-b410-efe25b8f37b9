 using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Roles
{
    public class PermissionsModel : PageModel
    {
        private readonly RoleService _roleService;
        private readonly PermissionService _permissionService;
        private readonly UserManager<ApplicationUser> _userManager;

        public PermissionsModel(
            RoleService roleService,
            PermissionService permissionService,
            UserManager<ApplicationUser> userManager)
        {
            _roleService = roleService;
            _permissionService = permissionService;
            _userManager = userManager;
        }

        public Role Role { get; set; } = null!;
        public List<Permission> AllPermissions { get; set; } = new();
        public List<Permission> RolePermissions { get; set; } = new();
        [BindProperty]
        public List<string> SelectedPermissions { get; set; } = new();
        public Dictionary<string, List<Permission>> PermissionsByCategory { get; set; } = new();
        public List<int> RolePermissionIds { get; set; } = new();
        public int RoleId { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            if (!User.Identity?.IsAuthenticated == true)
                return RedirectToPage("/Account/Login");

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return RedirectToPage("/Account/Login");

            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanManageRoles(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(id);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return RedirectToPage("/Roles/Index");
            }
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            if (!User.Identity?.IsAuthenticated == true)
                return RedirectToPage("/Account/Login");

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return RedirectToPage("/Account/Login");

            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanManageRoles(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await UpdateRolePermissionsAsync(id);
                TempData["SuccessMessage"] = "دسترسی‌های نقش با موفقیت به‌روزرسانی شد.";
                return RedirectToPage("/Roles/Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در به‌روزرسانی دسترسی‌ها: " + ex.Message;
                await LoadDataAsync(id);
                return Page();
            }
        }

        private async Task LoadDataAsync(int roleId)
        {
            RoleId = roleId;
            Role = await _roleService.GetRoleByIdAsync(roleId);
            if (Role == null)
                throw new Exception("نقش یافت نشد.");

            AllPermissions = await _permissionService.GetAllPermissionsAsync();
            PermissionsByCategory = AllPermissions
                .GroupBy(p => p.Category ?? "سایر")
                .ToDictionary(g => g.Key, g => g.ToList());

            RolePermissions = await _roleService.GetRolePermissionsAsync(roleId);
            RolePermissionIds = RolePermissions.Select(p => p.PermissionId).ToList();
        }

        private async Task UpdateRolePermissionsAsync(int roleId)
        {
            var currentPermissions = await _roleService.GetRolePermissionsAsync(roleId);
            foreach (var permission in currentPermissions)
                await _roleService.RevokePermissionFromRoleAsync(roleId, permission.PermissionId);

            foreach (var permissionId in SelectedPermissions)
                if (int.TryParse(permissionId, out int id))
                    await _roleService.GrantPermissionToRoleAsync(roleId, id, 1); // 1 = Admin User ID
        }

        private bool CanManageRoles(IList<string> userRoles)
        {
            return userRoles.Contains("Admin");
        }
    }
}