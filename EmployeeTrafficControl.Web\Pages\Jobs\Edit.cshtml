@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Jobs.EditModel
@{
    ViewData["Title"] = "ویرایش شغل";
}

<div class="page-header">
    <h1>ویرایش شغل</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <input type="hidden" asp-for="Job.JobId" />

                    <div class="form-section">
                        <div class="mb-3">
                            <label asp-for="Job.BuildingId" class="form-label">ساختمان <span class="text-danger">*</span></label>
                            <select asp-for="Job.BuildingId" asp-items="Model.Buildings" class="form-select">
                                <option value="">انتخاب کنید...</option>
                            </select>
                            <span asp-validation-for="Job.BuildingId" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Job.Title" class="form-label">عنوان شغل <span class="text-danger">*</span></label>
                            <input asp-for="Job.Title" class="form-control" placeholder="مثال: مهندس نرم‌افزار" />
                            <span asp-validation-for="Job.Title" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <p class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                مجوز رانندگی برای هر کارمند به صورت جداگانه در بخش کارمندان تعریف می‌شود.
                            </p>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره تغییرات
                        </button>
                        <a asp-page="Details" asp-route-id="@Model.Job.JobId" class="btn btn-info">
                            <i class="bi bi-eye"></i> مشاهده جزئیات
                        </a>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">اطلاعات شغل</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">شناسه:</dt>
                    <dd class="col-sm-7">@Model.Job.JobId</dd>


                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        عنوان شغل باید منحصر به فرد باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
