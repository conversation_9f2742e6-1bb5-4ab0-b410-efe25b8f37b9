@page
@using Microsoft.AspNetCore.Identity
@using EmployeeTrafficControl.Data.Models
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@model EmployeeTrafficControl.Web.Pages.Users.IndexModel
@{

    ViewData["Title"] = "لیست کاربران";
    var currentUser = await UserManager.GetUserAsync(User);
    var userRoles = currentUser != null ? await UserManager.GetRolesAsync(currentUser) : new List<string>();
    bool canCreate = userRoles.Contains("Admin") || userRoles.Contains("Manager");

}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>لیست کاربران</h1>
        @if (canCreate)

        {

            <a asp-page="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> افزودن کاربر جدید
            </a>
        }
    </div>
</div>

@if (Model.Users == null || !Model.Users.Any())
{

    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle"></i>
        <p class="mb-0">هیچ کاربری یافت نشد.</p>
        @if (canCreate)

        {

            <a asp-page="Create" class="btn btn-primary mt-2">افزودن اولین کاربر</a>

        }
    </div>
}
else
{
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>نام کاربری</th>
                            <th>نام کامل</th>
                            <th>ایمیل</th>
                            <th>وضعیت</th>
                            <th class="text-center">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr>
                                <td>
                                    <strong>@user.UserName</strong>
                                </td>
                                <td>
                                    @(user.FullName ?? "نامشخص")
                                </td>
                                <td>
                                    @(user.Email ?? "نامشخص")
                                </td>
                                <td>
                                    @if (user.IsActive)
                                    {
                                        <span class="badge bg-success">فعال</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غیرفعال</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        @if (canCreate)

                                        {
                                            <a asp-page="Edit" asp-route-id="@user.Id" class="btn btn-sm btn-outline-primary" title="ویرایش">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        }
                                        <a asp-page="Details" asp-route-id="@user.Id" class="btn btn-sm btn-outline-info" title="جزئیات">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        @if (canCreate)
                                        {
                                            <form method="post" style="display: inline;" onsubmit="return confirm('آیا از حذف این کاربر اطمینان دارید؟')">
                                                <input type="hidden" name="id" value="@user.Id" />
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        }
                                       
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <p class="text-muted">
                    نمایش @Model.Users.Count مورد
                </p>
            </div>
            <div class="col-md-6 text-end">
                @if (canCreate)
                {
                    <a asp-page="Create" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> افزودن کاربر جدید
                </a> 
                }
               
            </div>
        </div>
    </div>
}
