@page 
@model EmployeeTrafficControl.Web.Pages.Reports.PresentEmployeesModel
@{
    ViewData["Title"] = "کارمندان حاضر";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-people-fill"></i> کارمندان حاضر در ساختمان</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">لیست کارمندان حاضر در ساختمان امروز</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">جستجو در کارمندان</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="نام، نام خانوادگی یا کد پرسنلی" />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">شغل</label>
                        <select name="JobId" class="form-select" asp-items="Model.Jobs">
                            <option value="">همه مشاغل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-present">
            <div class="stats-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeesPresent.Count</h3>
                <p>حاضر در ساختمان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-on-time">
            <div class="stats-icon">
                <i class="bi bi-clock-fill"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.OnTimeCount</h3>
                <p>به موقع آمده‌اند</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-late">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.LateCount</h3>
                <p>تأخیر داشته‌اند</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-overtime">
            <div class="stats-icon">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.OvertimeCount</h3>
                <p>اضافه کار</p>
            </div>
        </div>
    </div>
</div>

<!-- لیست کارمندان حاضر -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> کارمندان حاضر
                </h5>
                <span class="badge bg-success">@Model.EmployeesPresent.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.EmployeesPresent.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>کارمند</th>
                                    <th>کد پرسنلی</th>
                                    <th>ساختمان</th>
                                    <th>شغل</th>
                                    <th>زمان ورود</th>
                                    <th>وضعیت ورود</th>
                                    <th>ساعات کار</th>
                                    <th>وضعیت فعلی</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var status in Model.EmployeesPresent)
                                {
                                    var attendance = Model.TodayAttendances.FirstOrDefault(a => a.EmployeeId == status.EmployeeId);
                                    
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>@status.Employee.FirstName @status.Employee.LastName</strong>
                                                @if (!string.IsNullOrEmpty(status.Employee.PhoneNumber))
                                                {
                                                    <br><small class="text-muted">@status.Employee.PhoneNumber</small>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            <code>@status.Employee.PersonnelCode</code>
                                        </td>
                                        <td>
                                            @if (status.Employee.Building != null)
                                            {
                                                <span class="badge bg-info">@status.Employee.Building.Name</span>
                                            }
                                        </td>
                                        <td>
                                            @if (status.Employee.Job != null)
                                            {
                                                <span class="badge bg-secondary">@status.Employee.Job.Title</span>
                                                @if (status.Employee.HasDrivingLicense)
                                                {
                                                    <span class="badge bg-warning ms-1">دارای گواهینامه</span>
                                                }
                                            }
                                        </td>
                                        <td>
                                            @if (attendance?.CheckInTime.HasValue == true)
                                            {
                                                <span class="text-success">@attendance.CheckInTime.Value.ToString("HH:mm")</span>
                                            }
                                            else if (status.EntryTime.HasValue)
                                            {
                                                <span class="text-success">@status.EntryTime.Value.ToString("HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (attendance != null)
                                            {
                                                if (attendance.LateMinutes > 0)
                                                {
                                                    <span class="badge bg-warning">@attendance.LateMinutes دقیقه تأخیر</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">به موقع</span>
                                                }
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">نامشخص</span>
                                            }
                                        </td>
                                        <td>
                                            @if (attendance?.TotalWorkHours.HasValue == true)
                                            {
                                                var hours = (int)attendance.TotalWorkHours.Value.TotalHours;
                                                var minutes = attendance.TotalWorkHours.Value.Minutes;
                                                <span class="text-info">@hours:@minutes.ToString("D2")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">محاسبه نشده</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @status.GetStatusBadgeClass()">
                                                @status.GetStatusDisplayName()
                                            </span>
                                            @if (!string.IsNullOrEmpty(status.Notes))
                                            {
                                                <br><small class="text-muted">@status.Notes</small>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-person-slash" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">کارمندی در ساختمان حضور ندارد</h5>
                        <p>همه کارمندان خارج از ساختمان هستند یا هنوز ورود ثبت نشده است.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-on-time {
        border-right: 4px solid #007bff;
    }
    .stats-on-time .stats-icon { color: #007bff; }

    .stats-late {
        border-right: 4px solid #ffc107;
    }
    .stats-late .stats-icon { color: #ffc107; }

    .stats-overtime {
        border-right: 4px solid #17a2b8;
    }
    .stats-overtime .stats-icon { color: #17a2b8; }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }
</style>
