using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    [AuthorizePermission("CREATE_USER")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class CreateModel : PageModel
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole<int>> _roleManager;
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;
        private readonly RoleService _roleService;

        public CreateModel(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<int>> roleManager,
            EmployeeService employeeService,
            BuildingService buildingService,
            RoleService roleService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _employeeService = employeeService;
            _buildingService = buildingService;
            _roleService = roleService;
        }

        [BindProperty]
        public new ApplicationUser User { get; set; } = default!;

        [BindProperty]
        public string Password { get; set; } = default!;

        [BindProperty]
        public string RoleName { get; set; } = default!;

        public SelectList Employees { get; set; } = default!;
        public SelectList Buildings { get; set; } = default!;
        public SelectList Roles { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? employeeId)
        {
            await LoadSelectLists();
            User = new ApplicationUser();

            // Pre-select employee if provided
            if (employeeId.HasValue)
            {
                User.EmployeeId = employeeId.Value;
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // Remove validation errors for navigation properties
            ModelState.Remove("User.Employee");
            ModelState.Remove("User.Building");

            if (string.IsNullOrEmpty(Password) || Password.Length < 6)
            {
                ModelState.AddModelError("Password", "رمز عبور باید حداقل 6 کاراکتر باشد.");
            }

            if (string.IsNullOrEmpty(RoleName))
            {
                ModelState.AddModelError("RoleName", "انتخاب نقش اجباری است.");
            }

            if (!ModelState.IsValid)
            {
                await LoadSelectLists();
                return Page();
            }

            // Check if username already exists in Identity system
            var existingUser = await _userManager.FindByNameAsync(User.UserName);
            if (existingUser != null)
            {
                ModelState.AddModelError("User.UserName", "نام کاربری وارد شده قبلاً ثبت شده است.");
                await LoadSelectLists();
                return Page();
            }

            try
            {
                // Create ApplicationUser for Identity system
                var applicationUser = new ApplicationUser
                {
                    UserName = User.UserName,
                    Email = User.Email,
                    FullName = User.FullName,
                    EmployeeId = User.EmployeeId,
                    BuildingId = User.BuildingId,
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedAt = DateTime.Now
                };

                // Create user with Identity system
                var result = await _userManager.CreateAsync(applicationUser, Password);

                if (result.Succeeded)
                {
                    // Add user to role
                    await _userManager.AddToRoleAsync(applicationUser, RoleName);

                    TempData["SuccessMessage"] = "کاربر جدید با موفقیت اضافه شد.";
                    return RedirectToPage("./Index");
                }
                else
                {
                    foreach (var error in result.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                    await LoadSelectLists();
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var employees = await _employeeService.GetAllEmployeesAsync();
            var buildings = await _buildingService.GetAllBuildingsAsync();
            var identityRoles = _roleManager.Roles.ToList();

            Employees = new SelectList(employees.Select(e => new {
                Value = e.EmployeeId,
                Text = $"{e.FirstName} {e.LastName} ({e.PersonnelCode})"
            }), "Value", "Text");

            Buildings = new SelectList(buildings, "BuildingId", "Name");
            Roles = new SelectList(identityRoles, "Name", "Name");
        }
    }
}
