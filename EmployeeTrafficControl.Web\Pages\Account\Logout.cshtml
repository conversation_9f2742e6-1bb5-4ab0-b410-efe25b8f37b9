@page "/Account/Logout"
@model EmployeeTrafficControl.Web.Pages.Account.LogoutModel
@{
    ViewData["Title"] = "خروج از سیستم";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>خروج از سیستم</h2>
            <p class="text-muted">در حال خروج از سیستم...</p>
        </div>

        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">در حال خروج...</span>
            </div>
            <p class="mt-3">لطفاً صبر کنید...</p>
        </div>
    </div>
</div>

<script>
    // Redirect to login after logout
    setTimeout(function() {
        window.location.href = '/login';
    }, 2000);
</script>
