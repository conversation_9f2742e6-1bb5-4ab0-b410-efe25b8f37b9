using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Web.Pages.Admin
{
    public class DatabaseStatusModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public DatabaseStatusModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public bool IsDatabaseConnected { get; set; }
        public string ConnectionStatus { get; set; } = "";
       
        public DatabaseStatsViewModel Stats { get; set; } = new DatabaseStatsViewModel();

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanAccessAdmin(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDatabaseStatusAsync();
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDatabaseStatusAsync()
        {
            try
            {
                await _context.Database.CanConnectAsync();
                IsDatabaseConnected = true;
                ConnectionStatus = "متصل";

                Stats.BuildingsCount = await _context.Buildings.CountAsync();
                Stats.EmployeesCount = await _context.Employees.CountAsync();
                Stats.CarsCount = await _context.Cars.CountAsync();
                Stats.JobsCount = await _context.Jobs.CountAsync();
                Stats.CarsInParkingCount = await _context.Cars.CountAsync(c => c.IsInParking); // Adjust property name if needed
                Stats.CarsOutOfParkingCount = await _context.Cars.CountAsync(c => !c.IsInParking); // Adjust property name if needed
                Stats.UsersCount = await _userManager.Users.CountAsync();
                Stats.RolesCount = await _context.Roles.CountAsync();
                Stats.PermissionsCount = await _context.Permissions.CountAsync();

                var today = DateTime.Today;
                Stats.TodayAttendanceCount = await _context.DailyAttendances.CountAsync(a => a.Date == today);
                Stats.TodayEmployeeTrafficCount = await _context.EmployeeWorkingHours.CountAsync(e => e.StartDate == today);
                Stats.TodayCarTrafficCount = await _context.CarTrafficLogs.CountAsync(c => c.EntryTime.HasValue && c.EntryTime.Value.Date == today);
            }
            catch (Exception ex)
            {
                IsDatabaseConnected = false;
                ConnectionStatus = $"خطا: {ex.Message}";
            }
        }

        private bool CanAccessAdmin(IList<string> userRoles)
        {
            return userRoles.Contains("Admin");
        }
    }
}
