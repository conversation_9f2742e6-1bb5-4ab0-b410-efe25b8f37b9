using EmployeeTrafficControl.Data.Models; // Corrected namespace
using EmployeeTrafficControl.Data.Models;
using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Data.Models // Corrected namespace
{
    /// <summary>
    /// مدل نقش‌های سیستم
    /// </summary>
    public class Role
    {
        [Key]
        [Display(Name = "شناسه نقش")]
        public int RoleId { get; set; }

        [Required(ErrorMessage = "نام نقش اجباری است.")]
        [StringLength(50, ErrorMessage = "نام نقش حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نام نقش")]
        public string Name { get; set; } = string.Empty; // Initialized to avoid null warning

        [Required(ErrorMessage = "کد نقش اجباری است.")]
        [StringLength(50, ErrorMessage = "کد نقش حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "کد نقش")]
        public string Code { get; set; } = string.Empty; // Initialized to avoid null warning

        [StringLength(500, ErrorMessage = "توضیحات حداکثر 500 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Description { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "نقش سیستمی")]
        public bool IsSystemRole { get; set; } = false;

        [Display(Name = "تاریخ ایجاد")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public ICollection<ApplicationUser> Users { get; set; } = new List<ApplicationUser>(); // Changed to collection for many-to-one with ApplicationUser
        public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }
}