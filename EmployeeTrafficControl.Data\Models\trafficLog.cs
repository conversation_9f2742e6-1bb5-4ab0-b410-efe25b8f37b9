﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class TrafficLog
    {
        [Key]
        [Display(Name = "شناسه تردد")]
        public int TrafficLogId { get; set; }

        [Display(Name = "کارمند")]
        public int EmployeeId { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان ورود")]
        public DateTime? EntryTime { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان خروج")]
        public DateTime? ExitTime { get; set; }

        [Required(ErrorMessage = "نوع تردد اجباری است.")]
        [StringLength(50, ErrorMessage = "نوع تردد حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نوع تردد")]
        public string TrafficType { get; set; }

        [StringLength(4000, ErrorMessage = "توضیحات حداکثر 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Description { get; set; }

        [Display(Name = "خروج خودکار")]
        public bool IsAutomaticExit { get; set; } = false;

        [Display(Name = "غیبت")]
        public bool IsAbsence { get; set; } = false;

        [Display(Name = "تاخیر")]
        public bool IsLate { get; set; } = false;

        [Display(Name = "تعجیل در خروج")]
        public bool IsEarlyExit { get; set; } = false;

        [Display(Name = "کارمند")]
        public Employee Employee { get; set; } = default!;

        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;

        [Display(Name = "کاربر")]
        public ApplicationUser User { get; set; }
    }
}