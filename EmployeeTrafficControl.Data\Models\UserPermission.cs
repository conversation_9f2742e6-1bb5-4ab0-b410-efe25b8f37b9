using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    /// <summary>
    /// مدل مجوزهای خاص کاربران (برای override کردن مجوزهای نقش)
    /// </summary>
    public class UserPermission
    {
        [Key]
        [Display(Name = "شناسه")]
        public int UserPermissionId { get; set; }

        [Required]
        [Display(Name = "شناسه کاربر")]
        public int UserId { get; set; }

        [Required]
        [Display(Name = "شناسه مجوز")]
        public int PermissionId { get; set; }

        [Display(Name = "نوع دسترسی")]
        public PermissionType PermissionType { get; set; }

        [Display(Name = "تاریخ اعطا")]
        public DateTime GrantedAt { get; set; } = DateTime.Now;

        [Display(Name = "اعطا شده توسط")]
        public int? GrantedByUserId { get; set; }

        [StringLength(500, ErrorMessage = "توضیحات حداکثر 500 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Notes { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        [Display(Name = "کاربر")]
        public ApplicationUser User { get; set; }

        [ForeignKey("PermissionId")]
        [Display(Name = "مجوز")]
        public Permission Permission { get; set; }

        [ForeignKey("GrantedByUserId")]
        [Display(Name = "اعطا کننده")]
        public ApplicationUser? GrantedByUser { get; set; }
    }

    /// <summary>
    /// نوع دسترسی کاربر به مجوز
    /// </summary>
    public enum PermissionType
    {
        [Display(Name = "اعطا")]
        Grant = 1,
        
        [Display(Name = "منع")]
        Deny = 2
    }
}
