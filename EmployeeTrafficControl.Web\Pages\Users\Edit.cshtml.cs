using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Core.Services;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    [AuthorizePermission("EDIT_USER")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class EditModel : PageModel
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole<int>> _roleManager; // اضافه شده
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;
        private readonly RoleService _roleService;

        public EditModel(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole<int>> roleManager, // اضافه شده
            EmployeeService employeeService,
            BuildingService buildingService,
            RoleService roleService)
        {
            _userManager = userManager;
            _roleManager = roleManager; // اضافه شده
            _employeeService = employeeService;
            _buildingService = buildingService;
            _roleService = roleService;
        }

        [BindProperty]
        public EditUserViewModel UserEdit { get; set; } = default!;

        [BindProperty]
        public string? Password { get; set; }

        public SelectList Employees { get; set; } = default!;
        public SelectList Buildings { get; set; } = default!;
        public SelectList Roles { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Get current roles for the user
            var userRoles = await _userManager.GetRolesAsync(user);
            int? currentRoleId = null;
            if (userRoles.Any())
            {
                // Assuming you want to select the first role found for editing dropdown
                var firstRoleName = userRoles.FirstOrDefault();
                if (!string.IsNullOrEmpty(firstRoleName))
                {
                    var role = await _roleManager.FindByNameAsync(firstRoleName); // Use _roleManager here
                    if (role != null)
                    {
                        currentRoleId = role.Id;
                    }
                }
            }

            UserEdit = new EditUserViewModel
            {
                UserId = user.Id,
                UserName = user.UserName,
                RoleId = currentRoleId ?? 0, // Set RoleId based on retrieved role, provide a default if no role
                EmployeeId = user.EmployeeId,
                BuildingId = user.BuildingId,
                IsActive = user.IsActive,
                // Remove direct assignment of navigation properties like Employee, Building, Role from ApplicationUser
                // as they are not loaded by FindByIdAsync by default and are handled by the ViewModel for display.
                // Employee = user.Employee, // Remove or load explicitly if needed for display
                // Building = user.Building, // Remove or load explicitly if needed for display
                // Role = user.Role // Remove as Role is not directly on ApplicationUser in Identity
            };

            await LoadSelectLists();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                var existingUser = await _userManager.FindByIdAsync(UserEdit.UserId.ToString());
                if (existingUser == null)
                {
                    TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                    return RedirectToPage("./Index");
                }

                // بررسی یکتا بودن نام کاربری
                var usernameExists = await _userManager.Users.AnyAsync(u => u.UserName == UserEdit.UserName && u.Id != UserEdit.UserId);
                if (usernameExists)
                {
                    ModelState.AddModelError("UserEdit.UserName", "نام کاربری تکراری است.");
                    await LoadSelectLists();
                    return Page();
                }

                existingUser.UserName = UserEdit.UserName;
                // existingUser.RoleId = UserEdit.RoleId; // حذف این خط، مدیریت نقش‌ها به صورت جداگانه انجام می‌شود
                existingUser.EmployeeId = UserEdit.EmployeeId;
                existingUser.BuildingId = UserEdit.BuildingId;
                existingUser.IsActive = UserEdit.IsActive;

                // مدیریت تغییر رمز عبور
                if (!string.IsNullOrEmpty(Password))
                {
                    var token = await _userManager.GeneratePasswordResetTokenAsync(existingUser);
                    var result = await _userManager.ResetPasswordAsync(existingUser, token, Password);
                    if (!result.Succeeded)
                    {
                        ModelState.AddModelError(string.Empty, "خطا در تغییر رمز عبور: " + string.Join(", ", result.Errors.Select(e => e.Description)));
                        await LoadSelectLists();
                        return Page();
                    }
                }

                var updateResult = await _userManager.UpdateAsync(existingUser);

                if (updateResult.Succeeded)
                {
                    // === شروع تغییرات برای مدیریت نقش‌ها ===
                    var currentRoles = await _userManager.GetRolesAsync(existingUser);
                    var selectedRoleEntity = await _roleManager.FindByIdAsync(UserEdit.RoleId.ToString()); // پیدا کردن موجودیت نقش بر اساس RoleId انتخاب شده
                    string newRoleName = selectedRoleEntity?.Name; // گرفتن نام نقش جدید

                    // حذف کاربر از تمام نقش‌های فعلی‌اش
                    if (currentRoles.Any())
                    {
                        var removeResult = await _userManager.RemoveFromRolesAsync(existingUser, currentRoles);
                        if (!removeResult.Succeeded)
                        {
                            ModelState.AddModelError(string.Empty, "خطا در حذف نقش‌های قبلی کاربر: " + string.Join(", ", removeResult.Errors.Select(e => e.Description)));
                            await LoadSelectLists();
                            return Page();
                        }
                    }

                    // اضافه کردن کاربر به نقش جدید انتخاب شده
                    if (!string.IsNullOrEmpty(newRoleName))
                    {
                        var addResult = await _userManager.AddToRoleAsync(existingUser, newRoleName);
                        if (!addResult.Succeeded)
                        {
                            ModelState.AddModelError(string.Empty, "خطا در افزودن کاربر به نقش جدید: " + string.Join(", ", addResult.Errors.Select(e => e.Description)));
                            await LoadSelectLists();
                            return Page();
                        }
                    }
                    // === پایان تغییرات برای مدیریت نقش‌ها ===

                    TempData["SuccessMessage"] = "اطلاعات کاربر با موفقیت به‌روزرسانی شد.";
                    return RedirectToPage("./Index");
                }
                else
                {
                    ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + string.Join(", ", updateResult.Errors.Select(e => e.Description)));
                    await LoadSelectLists();
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var employees = await _employeeService.GetAllEmployeesAsync();
            var buildings = await _buildingService.GetAllBuildingsAsync();
            var roles = await _roleService.GetAllRolesAsync(); // مطمئن شوید این متد RoleId و Name را برمی‌گرداند

            Employees = new SelectList(employees.Select(e => new {
                Value = e.EmployeeId,
                Text = $"{e.FirstName} {e.LastName} ({e.PersonnelCode})"
            }), "Value", "Text");

            Buildings = new SelectList(buildings, "BuildingId", "Name");
            Roles = new SelectList(roles, "RoleId", "Name");
        }
    }

    /// <summary>
    /// ViewModel برای ویرایش کاربر که PasswordHash را اجباری نمی‌کند
    /// </summary>
    public class EditUserViewModel
    {
        public int UserId { get; set; }

        [Required(ErrorMessage = "نام کاربری اجباری است.")]
        [StringLength(50, ErrorMessage = "نام کاربری حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نام کاربری")]
        public string UserName { get; set; } = default!;

        [Required(ErrorMessage = "نقش کاربر اجباری است.")]
        [Display(Name = "نقش")]
        public int RoleId { get; set; } // این فیلد برای انتخاب نقش در UI استفاده می‌شود

        [Display(Name = "شناسه کارمند مرتبط")]
        public int? EmployeeId { get; set; }

        [Display(Name = "ساختمان")]
        public int? BuildingId { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties (برای نمایش) - اینها در اینجا فقط برای نگهداری داده‌ها در ViewModel هستند
        // و مستقیماً برای ذخیره در Identity استفاده نمی‌شوند.
        public Employee? Employee { get; set; }
        public Building? Building { get; set; }
        public Role? Role { get; set; }
    }
}