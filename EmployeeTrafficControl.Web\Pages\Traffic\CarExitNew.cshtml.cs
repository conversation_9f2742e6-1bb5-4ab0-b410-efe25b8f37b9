using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services; // Keep this for other services if needed

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    //[AuthorizePermission("REGISTER_CAR_EXIT")]
    public class CarExitNewModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService; // New service
        private readonly EmployeeStatusService _employeeStatusService; // New service

        public CarExitNewModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            TrafficLogService trafficLogService,
            EmployeeStatusService employeeStatusService) // Inject new services
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
            _employeeStatusService = employeeStatusService;
        }

        public List<CarTrafficLog> CarsInside { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();
        public CarTrafficLog? SelectedCar { get; set; }
        public List<SelectListItem> AvailableDrivers { get; set; } = new();
        public bool IsFirstExitToday { get; set; }
        public string? Message { get; set; }
        public List<SelectListItem> AvailableCars { get; set; } = new();
        public int? SelectedCarId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? CarType { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? DurationFilter { get; set; }

        public int TotalCarsInside { get; set; }
        public int EmployeeCarsInside { get; set; }
        public int GuestCarsInside { get; set; }
        public int TodayExits { get; set; }

        public async Task<IActionResult> OnGetAsync(int? selectedCarId = null)
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            // اگر خودرو انتخاب شده باشد
            if (selectedCarId.HasValue)
            {
                var car = await _context.Cars
                    .Include(c => c.Building)
                    .FirstOrDefaultAsync(c => c.CarId == selectedCarId.Value);

                if (car != null)
                {
                    SelectedCar = new CarTrafficLog
                    {
                        CarTrafficLogId = 0,
                        CarId = car.CarId,
                        BuildingId = car.BuildingId,
                        EntryTime = DateTime.Now.AddHours(-1),
                        IsInParking = true,
                        DriverEmployeeId = 0,
                        Notes = "خودرو در پارکینگ",
                        Car = car,
                        Building = car.Building
                    };

                    // بررسی اولین خروج امروز
                    var today = DateTime.Today;
                    IsFirstExitToday = !await _context.CarTrafficLogs
                        .AnyAsync(ctl => ctl.CarId == car.CarId &&
                                        ctl.ExitTime.HasValue &&
                                        ctl.ExitTime.Value.Date == today);

                    // بارگذاری رانندگان موجود
                    await LoadAvailableDriversAsync();
                }
            }
            // If no car is selected, ensure SelectedCar is null
            else
            {
                SelectedCar = null;
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        public async Task<IActionResult> OnPostSelectCarAsync()
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                // دریافت CarId از فرم
                var carId = Request.Form["CarId"].ToString();
                if (string.IsNullOrEmpty(carId) || !int.TryParse(carId, out int selectedCarId))
                {
                    ViewData["ErrorMessage"] = "خودرو انتخاب نشده است.";
                    await LoadDataAsync(currentUser);
                    return Page();
                }

                SelectedCarId = selectedCarId;
                
                // بارگذاری اطلاعات خودرو انتخاب شده
                var car = await _context.Cars
                    .Include(c => c.Building)
                    .FirstOrDefaultAsync(c => c.CarId == selectedCarId);

                if (car != null)
                {
                    // پیدا کردن لاگ فعال خودرو در پارکینگ
                    var carTrafficLog = await _context.CarTrafficLogs
                        .Where(ctl => ctl.CarId == car.CarId && ctl.IsInParking)
                        .OrderByDescending(ctl => ctl.EntryTime)
                        .FirstOrDefaultAsync();

                    if (carTrafficLog != null)
                    {
                        SelectedCarId = carTrafficLog.CarTrafficLogId;
                        SelectedCar = carTrafficLog;
                    }
                    else
                    {
                        SelectedCarId = 0;
                        SelectedCar = new CarTrafficLog
                        {
                            CarTrafficLogId = 0,
                            CarId = car.CarId,
                            BuildingId = car.BuildingId,
                            EntryTime = DateTime.Now.AddHours(-1),
                            IsInParking = true,
                            DriverEmployeeId = 0,
                            Notes = "خودرو در پارکینگ",
                            Car = car,
                            Building = car.Building
                        };
                    }

                    // بررسی اولین خروج امروز
                    var today = DateTime.Today;
                    IsFirstExitToday = !await _context.CarTrafficLogs
                        .AnyAsync(ctl => ctl.CarId == car.CarId &&
                                        ctl.ExitTime.HasValue &&
                                        ctl.ExitTime.Value.Date == today);

                    // بارگذاری رانندگان موجود
                    await LoadAvailableDriversAsync();
                }

                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در انتخاب خودرو: " + ex.Message;
                await LoadDataAsync(currentUser);
                return Page();
            }
        }

        public async Task<IActionResult> OnPostRegisterExitAsync()
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard/index");
            }

            try
            {
                // دریافت اطلاعات از فرم
                var carIdStr = Request.Form["CarId"].ToString();
                var notes = Request.Form["Notes"].ToString();
                var driverIdStr = Request.Form["DriverId"].ToString();
                int driverId;
                if (string.IsNullOrEmpty(carIdStr) || !int.TryParse(carIdStr, out int carId) || carId <= 0 ||
                    string.IsNullOrEmpty(driverIdStr) || !int.TryParse(driverIdStr, out driverId) || driverId <= 0)
                {
                    TempData["ErrorMessage"] = "خودرو انتخاب نشده است یا راننده انتخاب نشده است.";
                    await LoadDataAsync(currentUser);
                    return Page();
                }

                // پیدا کردن خودرو در پارکینگ
                var car = await _context.Cars.FirstOrDefaultAsync(c => c.CarId == carId && c.IsInParking);
                if (car == null)
                {
                    TempData["ErrorMessage"] = "خودرو در پارکینگ یافت نشد یا قبلاً خارج شده است.";
                    await LoadDataAsync(currentUser);
                    return Page();
                }

                TempData["InfoMessage"] = "لطفا از داشبورد یکپارچه تردد برای ثبت ورود و خروج استفاده کنید.";
                return RedirectToPage("/Traffic/TrafficDashboard");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت خروج: " + ex.Message;
                await LoadDataAsync(currentUser);
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری خودروهای داخل پارکینگ - از جدول Cars
            var carsInParkingQuery = _context.Cars
                                            .Include(c => c.Building)
                                            .Include(c => c.CarTrafficLogs.Where(ctl => ctl.IsInParking).OrderByDescending(ctl => ctl.EntryTime).Take(1)) // Load the latest in-parking log
                                            .Where(c => c.IsInParking);

            if (accessibleBuildingIds != null)
            {
                carsInParkingQuery = carsInParkingQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            // اعمال فیلتر ساختمان
            if (BuildingId.HasValue)
            {
                carsInParkingQuery = carsInParkingQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                carsInParkingQuery = carsInParkingQuery.Where(c =>
                    c.PlateNumber.ToLower().Contains(searchLower) ||
                    c.Model.ToLower().Contains(searchLower) ||
                    c.Type.ToLower().Contains(searchLower));
            }

            // اعمال فیلتر نوع خودرو (کارمندی/مهمان)
            if (!string.IsNullOrWhiteSpace(CarType))
            {
                carsInParkingQuery = CarType.ToLower() switch
                {
                    "employee" => carsInParkingQuery.Where(c => c.IsEmployeeCar),
                    "guest" => carsInParkingQuery.Where(c => !c.IsEmployeeCar),
                    _ => carsInParkingQuery
                };
            }

            var carsInParkingList = await carsInParkingQuery.ToListAsync();

            // Project into CarTrafficLog-like objects for display, using the latest log or creating a dummy one
            CarsInside = carsInParkingList.Select(car =>
            {
                var latestLog = car.CarTrafficLogs.FirstOrDefault();
                return latestLog ?? new CarTrafficLog
                {
                    CarTrafficLogId = 0, // Indicate this is a dummy log
                    CarId = car.CarId,
                    Car = car,
                    BuildingId = car.BuildingId,
                    Building = car.Building,
                    EntryTime = null, // No entry time for dummy log
                    IsInParking = true,
                    DriverEmployeeId = 0, // Default for dummy
                    Notes = "خودرو در پارکینگ (بدون لاگ ورود)",
                    UserId = 0 // Default for dummy
                };
            }).ToList();

            // بارگذاری لیست خودروهای در دسترس برای انتخاب (بر اساس لاگ‌های واقعی یا dummy)
            AvailableCars = CarsInside.Select(ctl => new SelectListItem
            {
                Value = ctl.Car.CarId.ToString(),
                Text = $"{ctl.Car.PlateNumber} - {ctl.Car.Model} - {ctl.Car.Type} (ورود: {(ctl.EntryTime.HasValue ? ctl.EntryTime.Value.ToString("HH:mm") : "نامشخص")})",
                Selected = ctl.Car.CarId == SelectedCarId
            }).ToList();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task LoadAvailableDriversAsync()
        {
            var availableDrivers = await _context.EmployeeStatuses
                                                 .Include(es => es.Employee)
                                                 .ThenInclude(e => e.Job)
                                                 .Where(es => es.Date.Date == DateTime.Today &&
                                                            es.HasInitialEntry &&
                                                            es.IsPresentInBuilding &&
                                                            !es.FinalExitTime.HasValue &&
                                                            (es.Employee.HasDrivingLicense || (es.Employee.Job != null && es.Employee.Job.Title.Contains("راننده")))) // Added null check for Job
                                                 .ToListAsync();

            AvailableDrivers = availableDrivers.Select(es => new SelectListItem
            {
                Value = es.EmployeeId.ToString(),
                Text = $"{es.Employee.FirstName} {es.Employee.LastName} ({es.Employee.PersonnelCode})"
            }).ToList();
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;

            // آمار خودروهای داخل پارکینگ - از جدول Cars
            var carsInsideQuery = _context.Cars
                                         .Where(c => c.IsInParking);

            if (accessibleBuildingIds != null)
            {
                carsInsideQuery = carsInsideQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carsInsideQuery = carsInsideQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            var carsInsideList = await carsInsideQuery.ToListAsync();

            TotalCarsInside = carsInsideList.Count();
            
            // برای محاسبه خودروهای کارمندی و مهمان، مستقیماً از جدول Cars استفاده می‌کنیم
            EmployeeCarsInside = carsInsideList.Count(c => c.IsEmployeeCar);
            GuestCarsInside = carsInsideList.Count(c => !c.IsEmployeeCar);

            // آمار خروجی امروز از جدول CarTrafficLogs
            var todayExitsQuery = _context.CarTrafficLogs
                                         .Where(c => c.ExitTime.HasValue && c.ExitTime.Value.Date == today);

            if (accessibleBuildingIds != null)
            {
                todayExitsQuery = todayExitsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                todayExitsQuery = todayExitsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            TodayExits = await todayExitsQuery.CountAsync();
        }

        // متد کمکی برای دیباگ - بررسی وضعیت خودروها
        public async Task<IActionResult> OnPostDebugCarsAsync()
        {
            try
            {
                var allCars = await _context.Cars.ToListAsync();
                var allTrafficLogs = await _context.CarTrafficLogs.ToListAsync();
                
                var debugInfo = new
                {
                    TotalCars = allCars.Count,
                    ActiveCars = allCars.Count(c => c.IsActive),
                    CarsInParking = allCars.Count(c => c.IsInParking),
                    ActiveCarsInParking = allCars.Count(c => c.IsActive && c.IsInParking),
                    TotalTrafficLogs = allTrafficLogs.Count,
                    TrafficLogsInParking = allTrafficLogs.Count(t => t.IsInParking),
                    Cars = allCars.Select(c => new { c.CarId, c.PlateNumber, c.IsActive, c.IsInParking, c.BuildingId }).ToList()
                };

                Message = $"اطلاعات دیباگ: {debugInfo.TotalCars} کل خودرو، {debugInfo.ActiveCars} فعال، {debugInfo.CarsInParking} در پارکینگ، {debugInfo.ActiveCarsInParking} فعال در پارکینگ";
                
                await LoadDataAsync(await _userManager.GetUserAsync(User));
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در دیباگ: " + ex.Message;
                return Page();
            }
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") ||
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }

        // Removed RegisterCarOccupantsExitAsync as its logic is now handled by TrafficLogService.RegisterCarExitLogAsync
    }
}