using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Web.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<LoginModel> _logger;

        public LoginModel(
            SignInManager<ApplicationUser> signInManager,
            UserManager<ApplicationUser> userManager,
            ILogger<LoginModel> logger)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public string? ReturnUrl { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public class InputModel
        {
            [Required(ErrorMessage = "نام کاربری الزامی است.")]
            [Display(Name = "نام کاربری")]
            public string Username { get; set; } = string.Empty;

            [Required(ErrorMessage = "رمز عبور الزامی است.")]
            [DataType(DataType.Password)]
            [Display(Name = "رمز عبور")]
            public string Password { get; set; } = string.Empty;

            [Display(Name = "مرا به خاطر بسپار")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(string? returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            // Clear existing external cookie
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ReturnUrl = returnUrl;

            if (User.Identity?.IsAuthenticated == true)
            {
                Response.Redirect("/Dashboard");
            }
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            returnUrl ??= Url.Content("~/Dashboard");

            if (ModelState.IsValid)
            {
                try
                {
                    // Attempt to sign in
                    var result = await _signInManager.PasswordSignInAsync(
                        Input.Username,
                        Input.Password,
                        Input.RememberMe,
                        lockoutOnFailure: true);

                    if (result.Succeeded)
                    {
                        _logger.LogInformation("User {Username} logged in successfully.", Input.Username);
                        return LocalRedirect(returnUrl);
                    }

                    if (result.RequiresTwoFactor)
                    {
                        return RedirectToPage("./LoginWith2fa", new { ReturnUrl = returnUrl, Input.RememberMe });
                    }

                    if (result.IsLockedOut)
                    {
                        _logger.LogWarning("User {Username} account locked out.", Input.Username);
                        return RedirectToPage("./Lockout");
                    }

                    ModelState.AddModelError(string.Empty, "نام کاربری یا رمز عبور اشتباه است.");
                    return Page();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during login for user {Username}", Input.Username);
                    ModelState.AddModelError(string.Empty, "خطا در ورود به سیستم. لطفاً دوباره تلاش کنید.");
                    return Page();
                }
            }

            return Page();
        }
    }
}
