using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class DailyAttendance
    {
        [Key]
        public int AttendanceId { get; set; }

        [Display(Name = "کارمند")]
        [Required]
        public int EmployeeId { get; set; }

        [Display(Name = "تاریخ")]
        [Required]
        public DateTime Date { get; set; } = DateTime.Today;

        [Display(Name = "زمان ورود")]
        public DateTime? CheckInTime { get; set; }

        [Display(Name = "زمان خروج")]
        public DateTime? CheckOutTime { get; set; }

        [Display(Name = "تأخیر (دقیقه)")]
        public int LateMinutes { get; set; } = 0;

        [Display(Name = "زودتر رفتن (دقیقه)")]
        public int EarlyLeaveMinutes { get; set; } = 0;

        [Display(Name = "کل ساعات کار")]
        public TimeSpan? TotalWorkHours { get; set; }

        [Display(Name = "کل ساعات خروج ساعتی")]
        public TimeSpan TotalHourlyExitTime { get; set; } = TimeSpan.Zero;

        [Display(Name = "تعداد خروج ساعتی")]
        public int HourlyExitCount { get; set; } = 0;

        [Display(Name = "حاضر")]
        public bool IsPresent { get; set; } = false;

        [Display(Name = "تأیید شده")]
        public bool IsApproved { get; set; } = false;

        [Display(Name = "تأیید کننده")]
        public int? ApprovedByUserId { get; set; }

        [Display(Name = "زمان تأیید")]
        public DateTime? ApprovalTime { get; set; }

        [Display(Name = "توضیحات")]
        [MaxLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "ثبت کننده")]
        [Required]
        public int RegisteredByUserId { get; set; }

        [Display(Name = "زمان ثبت")]
        public DateTime RegisteredTime { get; set; } = DateTime.Now;

        [Display(Name = "آخرین به‌روزرسانی")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // Navigation properties
        [Display(Name = "کارمند")]
        public Employee Employee { get; set; } = default!;

        [Display(Name = "تأیید کننده")]
        public ApplicationUser? ApprovedByUser { get; set; }

        [Display(Name = "ثبت کننده")]
        public ApplicationUser RegisteredByUser { get; set; } = default!;

        // Helper methods
        public void CalculateWorkHours()
        {
            if (CheckInTime.HasValue && CheckOutTime.HasValue)
            {
                var totalTime = CheckOutTime.Value - CheckInTime.Value;
                TotalWorkHours = totalTime.Subtract(TotalHourlyExitTime);
            }
        }

        public void CalculateLateMinutes(TimeSpan workStartTime)
        {
            if (CheckInTime.HasValue)
            {
                var checkInTimeOnly = CheckInTime.Value.TimeOfDay;
                if (checkInTimeOnly > workStartTime)
                {
                    LateMinutes = (int)(checkInTimeOnly - workStartTime).TotalMinutes;
                }
                else
                {
                    LateMinutes = 0;
                }
            }
        }

        public void CalculateEarlyLeaveMinutes(TimeSpan workEndTime)
        {
            if (CheckOutTime.HasValue)
            {
                var checkOutTimeOnly = CheckOutTime.Value.TimeOfDay;
                if (checkOutTimeOnly < workEndTime)
                {
                    EarlyLeaveMinutes = (int)(workEndTime - checkOutTimeOnly).TotalMinutes;
                }
                else
                {
                    EarlyLeaveMinutes = 0;
                }
            }
        }

        public string GetAttendanceStatus()
        {
            if (!IsPresent)
                return "غایب";

            if (!CheckInTime.HasValue)
                return "ورود ثبت نشده";

            if (!CheckOutTime.HasValue)
                return "در حال کار";

            if (LateMinutes > 0 && EarlyLeaveMinutes > 0)
                return $"تأخیر {LateMinutes} دقیقه، زودتر رفتن {EarlyLeaveMinutes} دقیقه";

            if (LateMinutes > 0)
                return $"تأخیر {LateMinutes} دقیقه";

            if (EarlyLeaveMinutes > 0)
                return $"زودتر رفتن {EarlyLeaveMinutes} دقیقه";

            return "حضور کامل";
        }

        public string GetWorkHoursDisplay()
        {
            if (TotalWorkHours.HasValue)
            {
                var hours = (int)TotalWorkHours.Value.TotalHours;
                var minutes = TotalWorkHours.Value.Minutes;
                return $"{hours}:{minutes:D2}";
            }
            return "محاسبه نشده";
        }
    }
}
