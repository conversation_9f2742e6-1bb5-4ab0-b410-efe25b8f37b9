using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services; // Keep this line

namespace EmployeeTrafficControl.Services
{
    public class DailyAttendanceService
    {
        private readonly ApplicationDbContext _context;
        private readonly SystemSettingsService _systemSettingsService;
        private readonly DailyWorkScheduleService _dailyWorkScheduleService; // Add this

        public DailyAttendanceService(ApplicationDbContext context, SystemSettingsService systemSettingsService, DailyWorkScheduleService dailyWorkScheduleService) // Add dailyWorkScheduleService
        {
            _context = context;
            _systemSettingsService = systemSettingsService;
            _dailyWorkScheduleService = dailyWorkScheduleService; // Initialize
        }

        /// <summary>
        /// دریافت حضور روزانه کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>حضور روزانه کارمند</returns>
        public async Task<DailyAttendance?> GetDailyAttendanceAsync(int employeeId, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            return await _context.DailyAttendances
                                 .Include(da => da.Employee)
                                 .Include(da => da.RegisteredByUser)
                                 .Include(da => da.ApprovedByUser)
                                 .FirstOrDefaultAsync(da => da.EmployeeId == employeeId && da.Date.Date == targetDate.Date);
        }

        /// <summary>
        /// ثبت ورود کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="checkInTime">زمان ورود (پیش‌فرض الان)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterCheckInAsync(int employeeId, int userId, DateTime? checkInTime = null)
        {
            try
            {
                var actualCheckInTime = checkInTime ?? DateTime.Now;
                var today = actualCheckInTime.Date;
                
                var attendance = await GetDailyAttendanceAsync(employeeId, today);
                
                if (attendance == null)
                {
                    // ایجاد رکورد جدید
                    attendance = new DailyAttendance
                    {
                        EmployeeId = employeeId,
                        Date = today,
                        CheckInTime = actualCheckInTime,
                        IsPresent = true,
                        RegisteredByUserId = userId,
                        RegisteredTime = DateTime.Now
                    };

                    // محاسبه تأخیر بر اساس برنامه کاری روزانه
                    var todaySchedule = await _dailyWorkScheduleService.GetScheduleByDayAsync(today.DayOfWeek);
                    if (todaySchedule != null && todaySchedule.IsWorkingDay && todaySchedule.WorkStartTime.HasValue)
                    {
                        attendance.CalculateLateMinutes(todaySchedule.WorkStartTime.Value);
                    }

                    _context.DailyAttendances.Add(attendance);
                }
                else
                {
                    // به‌روزرسانی رکورد موجود
                    if (!attendance.CheckInTime.HasValue)
                    {
                        attendance.CheckInTime = actualCheckInTime;
                        attendance.IsPresent = true;

                        // محاسبه تأخیر بر اساس برنامه کاری روزانه
                        var todaySchedule = await _dailyWorkScheduleService.GetScheduleByDayAsync(today.DayOfWeek);
                        if (todaySchedule != null && todaySchedule.IsWorkingDay && todaySchedule.WorkStartTime.HasValue)
                        {
                            attendance.CalculateLateMinutes(todaySchedule.WorkStartTime.Value);
                        }

                        attendance.LastUpdated = DateTime.Now;
                    }
                    else
                    {
                        // اگر کارمند خروج نهایی داشته، امکان ورود مجدد وجود دارد
                        // بررسی وضعیت کارمند
                        var employeeStatus = await _context.EmployeeStatuses
                            .FirstOrDefaultAsync(es => es.EmployeeId == employeeId && es.Date.Date == today);

                        if (employeeStatus?.IsPresentInBuilding == false)
                        {
                            // ورود مجدد - به‌روزرسانی زمان ورود
                            attendance.CheckInTime = actualCheckInTime;
                            attendance.IsPresent = true;
                            attendance.CheckOutTime = null; // پاک کردن زمان خروج قبلی

                            // محاسبه مجدد تأخیر بر اساس برنامه کاری روزانه
                            var todaySchedule = await _dailyWorkScheduleService.GetScheduleByDayAsync(today.DayOfWeek);
                            if (todaySchedule != null && todaySchedule.IsWorkingDay && todaySchedule.WorkStartTime.HasValue)
                            {
                                attendance.CalculateLateMinutes(todaySchedule.WorkStartTime.Value);
                            }

                            attendance.LastUpdated = DateTime.Now;
                            attendance.Notes = "ورود مجدد پس از خروج نهایی";
                        }
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering check-in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ثبت خروج کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="checkOutTime">زمان خروج (پیش‌فرض الان)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterCheckOutAsync(int employeeId, int userId, DateTime? checkOutTime = null)
        {
            try
            {
                var actualCheckOutTime = checkOutTime ?? DateTime.Now;
                var today = actualCheckOutTime.Date;
                
                var attendance = await GetDailyAttendanceAsync(employeeId, today);
                
                if (attendance == null)
                {
                    // اگر ورود ثبت نشده، ابتدا یک رکورد حضور جدید ایجاد می‌کنیم
                    attendance = new DailyAttendance
                    {
                        EmployeeId = employeeId,
                        Date = today,
                        CheckInTime = null, // ورود ثبت نشده است
                        CheckOutTime = actualCheckOutTime,
                        IsPresent = false, // تا زمانی که ورود ثبت نشود، حاضر نیست
                        RegisteredByUserId = userId,
                        RegisteredTime = DateTime.Now,
                        Notes = "خروج ثبت شده بدون ورود قبلی"
                    };

                    // محاسبه ساعات کار و زودتر رفتن بر اساس برنامه کاری روزانه
                    var todaySchedule = await _dailyWorkScheduleService.GetScheduleByDayAsync(today.DayOfWeek);
                    if (todaySchedule != null && todaySchedule.IsWorkingDay && todaySchedule.WorkEndTime.HasValue)
                    {
                        attendance.CalculateWorkHours();
                        attendance.CalculateEarlyLeaveMinutes(todaySchedule.WorkEndTime.Value);
                    }

                    _context.DailyAttendances.Add(attendance);
                }
                else
                {
                    // به‌روزرسانی رکورد موجود
                    attendance.CheckOutTime = actualCheckOutTime;
                    
                    // محاسبه ساعات کار و زودتر رفتن بر اساس برنامه کاری روزانه
                    var todaySchedule = await _dailyWorkScheduleService.GetScheduleByDayAsync(today.DayOfWeek);
                    if (todaySchedule != null && todaySchedule.IsWorkingDay && todaySchedule.WorkEndTime.HasValue)
                    {
                        attendance.CalculateWorkHours();
                        attendance.CalculateEarlyLeaveMinutes(todaySchedule.WorkEndTime.Value);
                    }
                    
                    attendance.LastUpdated = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering check-out: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اضافه کردن زمان خروج ساعتی
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="exitDuration">مدت زمان خروج</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> AddHourlyExitTimeAsync(int employeeId, TimeSpan exitDuration)
        {
            try
            {
                var today = DateTime.Today;
                var attendance = await GetDailyAttendanceAsync(employeeId, today);
                
                if (attendance != null)
                {
                    attendance.TotalHourlyExitTime = attendance.TotalHourlyExitTime.Add(exitDuration);
                    attendance.HourlyExitCount++;
                    attendance.CalculateWorkHours(); // محاسبه مجدد ساعات کار
                    attendance.LastUpdated = DateTime.Now;
                    
                    await _context.SaveChangesAsync();
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding hourly exit time: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// دریافت گزارش حضور روزانه
        /// </summary>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <returns>لیست حضور روزانه</returns>
        public async Task<List<DailyAttendance>> GetDailyAttendanceReportAsync(DateTime? date = null, int? buildingId = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.DailyAttendances
                               .Include(da => da.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(da => da.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(da => da.RegisteredByUser)
                               .Where(da => da.Date.Date == targetDate.Date);

            if (buildingId.HasValue)
            {
                query = query.Where(da => da.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(da => da.Employee.Building != null ? da.Employee.Building.Name : "")
                             .ThenBy(da => da.Employee.FirstName)
                             .ThenBy(da => da.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// دریافت آمار حضور
        /// </summary>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <returns>آمار حضور</returns>
        public async Task<AttendanceStats> GetAttendanceStatsAsync(DateTime? date = null, int? buildingId = null)
        {
            var targetDate = date ?? DateTime.Today;

            var query = _context.DailyAttendances
                               .Include(da => da.Employee)
                               .Where(da => da.Date.Date == targetDate.Date);

            if (buildingId.HasValue)
            {
                query = query.Where(da => da.Employee.BuildingId == buildingId.Value);
            }

            var attendances = await query.ToListAsync();

            // دریافت وضعیت فعلی کارمندان
            var employeeIds = attendances.Select(a => a.EmployeeId).ToList();
            var currentStatuses = await _context.EmployeeStatuses
                .Where(es => es.Date.Date == targetDate.Date && employeeIds.Contains(es.EmployeeId))
                .ToDictionaryAsync(es => es.EmployeeId, es => es.CurrentStatus);

            // محاسبه آمار با در نظر گیری وضعیت فعلی
            var presentEmployees = attendances.Count(a =>
                a.IsPresent &&
                currentStatuses.ContainsKey(a.EmployeeId) &&
                currentStatuses[a.EmployeeId] != EmployeeCurrentStatus.OutOfOffice);

            return new AttendanceStats
            {
                TotalEmployees = attendances.Count,
                PresentEmployees = presentEmployees,
                AbsentEmployees = attendances.Count - presentEmployees,
                LateEmployees = attendances.Count(a => a.LateMinutes > 0 &&
                    currentStatuses.ContainsKey(a.EmployeeId) &&
                    currentStatuses[a.EmployeeId] != EmployeeCurrentStatus.OutOfOffice),
                EarlyLeaveEmployees = attendances.Count(a => a.EarlyLeaveMinutes > 0),
                AverageLateMinutes = attendances.Where(a => a.LateMinutes > 0).Average(a => (double?)a.LateMinutes) ?? 0,
                AverageEarlyLeaveMinutes = attendances.Where(a => a.EarlyLeaveMinutes > 0).Average(a => (double?)a.EarlyLeaveMinutes) ?? 0
            };
        }

        /// <summary>
        /// تأیید حضور توسط مدیر
        /// </summary>
        /// <param name="attendanceId">شناسه حضور</param>
        /// <param name="approvedByUserId">شناسه کاربر تأیید‌کننده</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> ApproveAttendanceAsync(int attendanceId, int approvedByUserId)
        {
            try
            {
                var attendance = await _context.DailyAttendances.FindAsync(attendanceId);
                if (attendance != null)
                {
                    attendance.IsApproved = true;
                    attendance.ApprovedByUserId = approvedByUserId;
                    attendance.ApprovalTime = DateTime.Now;
                    attendance.LastUpdated = DateTime.Now;
                    
                    await _context.SaveChangesAsync();
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error approving attendance: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ثبت حضور دستی
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ</param>
        /// <param name="checkInTime">زمان ورود</param>
        /// <param name="checkOutTime">زمان خروج</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="notes">توضیحات</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterManualAttendanceAsync(int employeeId, DateTime date, 
            DateTime? checkInTime, DateTime? checkOutTime, int userId, string? notes = null)
        {
            try
            {
                var existingAttendance = await GetDailyAttendanceAsync(employeeId, date);
                
                if (existingAttendance != null)
                {
                    // به‌روزرسانی رکورد موجود
                    existingAttendance.CheckInTime = checkInTime;
                    existingAttendance.CheckOutTime = checkOutTime;
                    existingAttendance.IsPresent = checkInTime.HasValue;
                    existingAttendance.Notes = notes;
                    existingAttendance.LastUpdated = DateTime.Now;
                }
                else
                {
                    // ایجاد رکورد جدید
                    existingAttendance = new DailyAttendance
                    {
                        EmployeeId = employeeId,
                        Date = date,
                        CheckInTime = checkInTime,
                        CheckOutTime = checkOutTime,
                        IsPresent = checkInTime.HasValue,
                        Notes = notes,
                        RegisteredByUserId = userId,
                        RegisteredTime = DateTime.Now
                    };
                    
                    _context.DailyAttendances.Add(existingAttendance);
                }

                // محاسبه تأخیر و زودتر رفتن بر اساس برنامه کاری روزانه
                var todaySchedule = await _dailyWorkScheduleService.GetScheduleByDayAsync(date.DayOfWeek);
                if (todaySchedule != null && todaySchedule.IsWorkingDay)
                {
                    if (checkInTime.HasValue && todaySchedule.WorkStartTime.HasValue)
                    {
                        existingAttendance.CalculateLateMinutes(todaySchedule.WorkStartTime.Value);
                    }

                    if (checkOutTime.HasValue && todaySchedule.WorkEndTime.HasValue)
                    {
                        existingAttendance.CalculateEarlyLeaveMinutes(todaySchedule.WorkEndTime.Value);
                    }
                }
                existingAttendance.CalculateWorkHours(); // این متد باید همیشه فراخوانی شود تا TotalWorkHours محاسبه شود
                
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering manual attendance: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ایجاد رکوردهای حضور روزانه برای تمام کارمندان فعال در ابتدای روز
        /// </summary>
        /// <param name="userId">شناسه کاربر انجام‌دهنده (مثلاً کاربر سیستمی)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> InitializeDailyAttendanceForEmployeesAsync(int userId)
        {
            try
            {
                var today = DateTime.Today;
                var activeEmployees = await _context.Employees
                                                    .Where(e => e.IsActive)
                                                    .ToListAsync();

                foreach (var employee in activeEmployees)
                {
                    var existingAttendance = await GetDailyAttendanceAsync(employee.EmployeeId, today);
                    if (existingAttendance == null)
                    {
                        var newAttendance = new DailyAttendance
                        {
                            EmployeeId = employee.EmployeeId,
                            Date = today,
                            IsPresent = false, // در ابتدا غایب فرض می‌شود تا ورود ثبت شود
                            RegisteredByUserId = userId,
                            RegisteredTime = DateTime.Now,
                            Notes = "رکورد حضور روزانه ایجاد شده به صورت خودکار"
                        };
                        _context.DailyAttendances.Add(newAttendance);
                    }
                }
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing daily attendance: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// کلاس آمار حضور
    /// </summary>
    public class AttendanceStats
    {
        public int TotalEmployees { get; set; }
        public int PresentEmployees { get; set; }
        public int AbsentEmployees { get; set; }
        public int LateEmployees { get; set; }
        public int EarlyLeaveEmployees { get; set; }
        public double AverageLateMinutes { get; set; }
        public double AverageEarlyLeaveMinutes { get; set; }
        
        public double AttendanceRate => TotalEmployees > 0 ? (double)PresentEmployees / TotalEmployees * 100 : 0;
        public double LateRate => PresentEmployees > 0 ? (double)LateEmployees / PresentEmployees * 100 : 0;
        public double EarlyLeaveRate => PresentEmployees > 0 ? (double)EarlyLeaveEmployees / PresentEmployees * 100 : 0;
    }
}
