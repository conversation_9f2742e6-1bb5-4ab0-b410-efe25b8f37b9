
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class DailyEntryModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService;

        public DailyEntryModel(ApplicationDbContext context, UserManager<ApplicationUser> userManager, TrafficLogService trafficLogService)
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
        }

        public List<Employee> EmployeesNotEntered { get; set; } = new List<Employee>();

        public async Task<IActionResult> OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToPage("/Account/Login");

            var today = DateTime.Today;
            var enteredEmployeeIds = await _context.EmployeeStatuses
                .Where(s => s.Date == today && s.HasInitialEntry && s.IsPresentInBuilding)
                .Select(s => s.EmployeeId)
                .ToListAsync();

            EmployeesNotEntered = await _context.Employees
                .Where(e => e.IsActive && !enteredEmployeeIds.Contains(e.EmployeeId))
                .OrderBy(e => e.FirstName)
                .ToListAsync();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(List<int> selectedEmployeeIds)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToPage("/Account/Login");

            var buildingId = user.BuildingId ?? 1; // Default building if not set

            foreach (var employeeId in selectedEmployeeIds)
            {
                await _trafficLogService.RegisterEntryAsync(employeeId, buildingId, user.Id, DateTime.Now, "ورود اولیه گروهی");
            }

            TempData["SuccessMessage"] = $"ورود اولیه برای {selectedEmployeeIds.Count} کارمند با موفقیت ثبت شد.";
            return RedirectToPage("./DailyEntry");
        }
    }
}
