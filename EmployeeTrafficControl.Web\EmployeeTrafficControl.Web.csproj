<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FarsiLibrary.Core" Version="2.8.1" />
    <PackageReference Include="MD.PersianDateTime" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EmployeeTrafficControl.Core\EmployeeTrafficControl.Core.csproj" />
    <ProjectReference Include="..\EmployeeTrafficControl.Data\EmployeeTrafficControl.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\fonts\" />
  </ItemGroup>

</Project>
