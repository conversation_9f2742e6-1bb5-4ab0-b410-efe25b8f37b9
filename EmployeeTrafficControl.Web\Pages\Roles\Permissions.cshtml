@page
@model EmployeeTrafficControl.Web.Pages.Roles.PermissionsModel
@{
    ViewData["Title"] = $"مدیریت مجوزهای نقش {Model.Role?.Name}";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title mb-0">
                                <i class="bi bi-shield-lock"></i>
                                مدیریت مجوزهای نقش: @Model.Role?.Name
                            </h4>
                            <small class="text-muted">کد نقش: @Model.Role?.Code</small>
                        </div>
                        <div>
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-right"></i>
                                بازگشت
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        <input type="hidden" asp-for="RoleId" />
                        
                        @if (Model.PermissionsByCategory.Any())
                        {
                            @foreach (var category in Model.PermissionsByCategory)
                            {
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="text-primary mb-0">
                                            <i class="bi bi-folder"></i>
                                            @category.Key
                                        </h5>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="selectAllInCategory('@category.Key', true)">
                                                انتخاب همه
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="selectAllInCategory('@category.Key', false)">
                                                لغو انتخاب همه
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        @foreach (var permission in category.Value)
                                        {
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="card h-100 @(Model.RolePermissionIds.Contains(permission.PermissionId) ? "border-success" : "")">
                                                    <div class="card-body">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox" 
                                                                   type="checkbox" 
                                                                   name="SelectedPermissionIds" 
                                                                   value="@permission.PermissionId"
                                                                   id="<EMAIL>"
                                                                   data-category="@category.Key"
                                                                   @(Model.RolePermissionIds.Contains(permission.PermissionId) ? "checked" : "") />
                                                            <label class="form-check-label fw-bold" for="<EMAIL>">
                                                                @permission.Name
                                                            </label>
                                                        </div>
                                                        <small class="text-muted d-block mt-1">
                                                            کد: @permission.Code
                                                        </small>
                                                        @if (!string.IsNullOrEmpty(permission.Description))
                                                        {
                                                            <small class="text-muted d-block mt-1">
                                                                @permission.Description
                                                            </small>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                            
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-check-circle"></i>
                                    ذخیره تغییرات
                                </button>
                                <a asp-page="./Index" class="btn btn-secondary btn-lg">
                                    <i class="bi bi-x-circle"></i>
                                    انصراف
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="bi bi-shield-lock display-1 text-muted"></i>
                                <h4 class="text-muted mt-3">هیچ مجوزی یافت نشد</h4>
                                <p class="text-muted">ابتدا مجوزهای سیستم را تعریف کنید.</p>
                                <a asp-page="/Permissions/Index" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i>
                                    مدیریت مجوزها
                                </a>
                            </div>
                        }
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function selectAllInCategory(category, select) {
            const checkboxes = document.querySelectorAll(`input[data-category="${category}"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = select;
                updateCardBorder(checkbox);
            });
        }

        function updateCardBorder(checkbox) {
            const card = checkbox.closest('.card');
            if (checkbox.checked) {
                card.classList.add('border-success');
            } else {
                card.classList.remove('border-success');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to all checkboxes
            const checkboxes = document.querySelectorAll('.permission-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateCardBorder(this);
                });
            });
        });
    </script>
}
