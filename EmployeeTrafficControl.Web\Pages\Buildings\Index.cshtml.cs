using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    [AuthorizePermission("VIEW_BUILDINGS")]
    public class IndexModel : PageModel
    {
        private readonly BuildingService _buildingService;

        public IndexModel(BuildingService buildingService)
        {
            _buildingService = buildingService;
        }

        public IList<Building> Buildings { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Buildings = await _buildingService.GetAllBuildingsAsync();
        }


    }
}
