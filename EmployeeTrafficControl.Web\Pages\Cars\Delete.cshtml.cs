using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    [AuthorizePermission("DELETE_CAR")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class DeleteModel : PageModel
    {
        private readonly CarService _carService;

        public DeleteModel(CarService carService)
        {
            _carService = carService;
        }

        [BindProperty]
        public int CarId { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            CarId = id;

            var car = await _carService.GetCarByIdAsync(id);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var car = await _carService.GetCarByIdAsync(CarId);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                bool deleteResult = await _carService.DeleteCarAsync(CarId);
                if (deleteResult)
                {
                    TempData["SuccessMessage"] = "خودرو با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این خودرو وجود ندارد زیرا دارای سوابق تردد است.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف خودرو: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
