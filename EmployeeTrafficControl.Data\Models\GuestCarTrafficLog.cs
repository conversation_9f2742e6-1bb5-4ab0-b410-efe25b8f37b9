﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EmployeeTrafficControl.Data.Models;

namespace EmployeeTrafficControl.Data.Models
{
    public class GuestCarTrafficLog
    {
        [Key]
        [Display(Name = "شناسه تردد خودرو مهمان")]
        public int GuestCarTrafficLogId { get; set; }

        [Required(ErrorMessage = "شماره پلاک اجباری است.")]
        [StringLength(20, ErrorMessage = "شماره پلاک حداکثر 20 کاراکتر باشد.")]
        [Display(Name = "شماره پلاک")]
        public string PlateNumber { get; set; }

        [StringLength(4000, ErrorMessage = "توضیحات حداکثر 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Description { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان ورود")]
        public DateTime? EntryTime { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان خروج")]
        public DateTime? ExitTime { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;

        public ApplicationUser User { get; set; }
    }
}