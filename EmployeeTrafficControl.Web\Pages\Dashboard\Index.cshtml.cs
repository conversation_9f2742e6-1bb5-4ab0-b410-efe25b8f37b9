using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Data.Data;

namespace EmployeeTrafficControl.Web.Pages.Dashboard
{
    [Authorize]
    public class IndexModel : PageModel
    {
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly DailyAttendanceService _attendanceService;
        private readonly SystemSettingsService _systemSettingsService;
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly BuildingService _buildingService;
        private readonly JobService _jobService;
        private readonly EmployeeService _employeeService;
        private readonly CarService _carService;

        private readonly TrafficLogService _trafficLogService;

        public IndexModel(
            EmployeeStatusService employeeStatusService,
            DailyAttendanceService attendanceService,
            SystemSettingsService systemSettingsService,
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            BuildingService buildingService,
            JobService jobService,
            EmployeeService employeeService,
            CarService carService,
            TrafficLogService trafficLogService)
        {
            _employeeStatusService = employeeStatusService;
            _attendanceService = attendanceService;
            _systemSettingsService = systemSettingsService;
            _context = context;
            _userManager = userManager;
            _buildingService = buildingService;
            _jobService = jobService;
            _employeeService = employeeService;
            _carService = carService;
            _trafficLogService = trafficLogService;
        }

        [BindProperty(SupportsGet = true)]
        public string SearchTerm { get; set; }

        public SearchResultModel SearchResult { get; set; }

        public List<Employee> AvailableEmployees { get; set; } = new List<Employee>();
        public List<Employee> AvailableDrivers { get; set; } = new List<Employee>();
        public CarTrafficLog LastCarExitLog { get; set; }

        public ApplicationUser CurrentUser { get; set; } = default!;
        public List<EmployeeStatus> PresentEmployees { get; set; } = new();
        public List<EmployeeStatus> EmployeesOutOfBuilding { get; set; } = new();
        public List<Car> CarsInParking { get; set; } = new();
        public List<CarTrafficLog> CarsOutOfParking { get; set; } = new();
        public AttendanceStats AttendanceStats { get; set; } = new();
        public SystemSettings SystemSettings { get; set; } = default!;
        public bool HasAnyEmployeeEnteredToday { get; set; } = false;

        public int TotalBuildings { get; set; }
        public int TotalJobs { get; set; }
        public int TotalEmployees { get; set; }
        public int TotalCars { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // Cache prevention headers
            Response.Headers["Cache-Control"] = "no-cache, no-store, must-revalidate";
            Response.Headers["Pragma"] = "no-cache";
            Response.Headers["Expires"] = "0";

            try
            {
                // Get current user
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return RedirectToPage("/Account/Login");
                }

                CurrentUser = user;

                // Load dashboard data based on user's building access
                await LoadDashboardDataAsync(user.BuildingId);

                // Load statistics
                await LoadStatisticsAsync();

                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات داشبورد: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDashboardDataAsync(int? buildingId)
        {
            // Load system settings
            SystemSettings = await _systemSettingsService.GetSystemSettingsAsync();

            // Load employees present in building (HasInitialEntry=true و IsPresentInBuilding=true و FinalExitTime=null)
            PresentEmployees = await _employeeStatusService.GetPresentEmployeesAsync(buildingId);

            // Load employees out of building (HasInitialEntry=true و IsPresentInBuilding=false و FinalExitTime=null)
            EmployeesOutOfBuilding = await _employeeStatusService.GetEmployeesOutOfBuildingExcludingFinalExitAsync(buildingId);

            // Check if any employees have entered today (HasInitialEntry=true)
            HasAnyEmployeeEnteredToday = await _employeeStatusService.HasAnyEmployeeEnteredTodayAsync(buildingId);

            // Load attendance stats
            AttendanceStats = await _attendanceService.GetAttendanceStatsAsync(DateTime.Today, buildingId);

            // Load car data
            await LoadCarDataAsync(buildingId);
        }

        private async Task LoadCarDataAsync(int? buildingId)
        {
            var carsQuery = _context.Cars
                                   .Include(c => c.Building)
                                   .AsQueryable();

            if (buildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == buildingId.Value);
            }

            // Get cars in parking
            CarsInParking = await carsQuery
                                 .Where(c => c.IsInParking)
                                 .OrderBy(c => c.PlateNumber)
                                 .ToListAsync();

            // فقط آخرین لاگ باز هر خودرو که خودرو خارج از پارکینگ است
            var carsOutOfParking = await carsQuery
                .Where(c => !c.IsInParking)
                .Select(c => _context.CarTrafficLogs
                    .Include(l => l.Car)
                    .Include(l => l.CarPassengers).ThenInclude(cp => cp.Employee)
                    .Include(l => l.DriverEmployee)
                    .Include(l => l.Building)
                    .Where(l => l.CarId == c.CarId && !l.IsInParking)
                    .OrderByDescending(l => l.ExitTime)
                    .FirstOrDefault())
                .Where(l => l != null)
                .ToListAsync();

            CarsOutOfParking = carsOutOfParking!;
        }

        private async Task LoadStatisticsAsync()
        {
            var buildings = await _buildingService.GetAllBuildingsAsync();
            var jobs = await _jobService.GetAllJobsAsync();
            var employees = await _employeeService.GetAllEmployeesAsync();
            var cars = await _carService.GetAllCarsAsync();

            TotalBuildings = buildings.Count;
            TotalJobs = jobs.Count;
            TotalEmployees = employees.Count;
            TotalCars = cars.Count;
        }

        public string GetUserRoleDisplay()
        {
            return User.IsInRole("Admin") ? "مدیر سیستم" :
                   User.IsInRole("Manager") ? "مدیر ساختمان" :
                   User.IsInRole("Guard") ? "نگهبان" :
                   User.IsInRole("User") ? "کاربر" : "نامشخص";
        }

        public bool CanAccessAdminFeatures() => User.IsInRole("Admin");

        public bool CanAccessManagerFeatures() => User.IsInRole("Admin") || User.IsInRole("Manager");

        public bool CanRegisterTraffic() => User.IsInRole("Admin") || User.IsInRole("Manager") || User.IsInRole("Guard");

        public async Task<IActionResult> OnPostSearchAsync()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                return RedirectToPage();
            }

            SearchResult = new SearchResultModel();

            // Search for a car by name, plate or model
            SearchResult.Car = await _context.Cars
                .FirstOrDefaultAsync(c => c.Type.Contains(SearchTerm) || c.PlateNumber.Contains(SearchTerm) || c.Model.Contains(SearchTerm));

            // Search for an employee
            SearchResult.Employee = await _context.Employees
                .FirstOrDefaultAsync(e => (e.FirstName + " " + e.LastName).Contains(SearchTerm) || e.PersonnelCode.Contains(SearchTerm));

            if (SearchResult.Employee != null)
            {
                SearchResult.EmployeeStatus = await _employeeStatusService.GetEmployeeStatusAsync(SearchResult.Employee.EmployeeId);
            }

            if (SearchResult.Car != null && SearchResult.Car.IsInParking)
            {
                // Load available employees for exit registration
                var presentStatuses = await _employeeStatusService.GetPresentEmployeesAsync();
                if(presentStatuses?.Any() ?? false)
                {
                    AvailableEmployees = presentStatuses.Select(es => es.Employee).ToList();
                    AvailableDrivers = AvailableEmployees.Where(e => e.HasDrivingLicense || (e.Job != null && e.Job.Title.Contains("راننده"))).ToList();
                }
            }
            else if (SearchResult.Car != null && !SearchResult.Car.IsInParking)
            {
                LastCarExitLog = await _context.CarTrafficLogs
                    .Include(l => l.DriverEmployee)
                    .Include(l => l.CarPassengers)
                    .ThenInclude(p => p.Employee)
                    .Where(l => l.CarId == SearchResult.Car.CarId && !l.IsInParking)
                    .OrderByDescending(l => l.ExitTime)
                    .FirstOrDefaultAsync();
            }
            await OnGetAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostRegisterCarExitAsync(int carId, int driverId, List<int> passengerIds, string notes)
        {
            var car = await _context.Cars.FindAsync(carId);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو یافت نشد.";
                return RedirectToPage();
            }

            // Server-side validation for passenger capacity
            if (passengerIds.Count > car.PassengerCapacity - 1)
            {
                TempData["ErrorMessage"] = "تعداد سرنشینان انتخاب شده بیش از ظرفیت مجاز خودرو است.";
                return RedirectToPage();
            }

            var user = await _userManager.GetUserAsync(User);
            var building = await _context.Buildings.FirstOrDefaultAsync();
            if (building != null && user != null)
            {
                await _trafficLogService.RegisterCarExitAsync(carId, driverId, passengerIds, building.BuildingId, user.Id, notes);
                TempData["SuccessMessage"] = "خروج خودرو با موفقیت ثبت شد.";
            }
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterCarEntryAsync(int carId, List<int> passengerIds)
        {
            var user = await _userManager.GetUserAsync(User);
            await _trafficLogService.RegisterCarEntryAsync(carId, user.Id, passengerIds);
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterCarEntryFromModalAsync(int carTrafficLogId, bool includeDriver, List<int> passengerIds)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    TempData["ErrorMessage"] = "کاربر یافت نشد.";
                    return RedirectToPage();
                }

                // دریافت اطلاعات log خروج خودرو
                var carTrafficLog = await _context.CarTrafficLogs
                    .Include(log => log.Car)
                    .Include(log => log.DriverEmployee)
                    .Include(log => log.CarPassengers)
                    .FirstOrDefaultAsync(log => log.CarTrafficLogId == carTrafficLogId);

                if (carTrafficLog == null || carTrafficLog.IsInParking)
                {
                    TempData["ErrorMessage"] = "خودرو یافت نشد یا قبلاً وارد شده است.";
                    return RedirectToPage();
                }

                // ثبت ورود خودرو
                carTrafficLog.EntryTime = DateTime.Now;
                carTrafficLog.IsInParking = true;

                // به‌روزرسانی وضعیت خودرو
                carTrafficLog.Car.IsInParking = true;

                // ثبت ورود راننده (در صورت انتخاب)
                if (includeDriver && carTrafficLog.DriverEmployeeId > 0)
                {
                    await _employeeStatusService.RegisterEmployeeEntryAsync(carTrafficLog.DriverEmployeeId, user.Id);
                }

                // ثبت ورود سرنشینان انتخاب شده
                foreach (var passenger in carTrafficLog.CarPassengers)
                {
                    if (passengerIds.Contains(passenger.EmployeeId))
                    {
                        await _employeeStatusService.RegisterEmployeeEntryAsync(passenger.EmployeeId, user.Id);
                        passenger.IsEntered = true; // علامت‌گذاری به عنوان وارد شده
                    }
                }

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "ورود خودرو با موفقیت ثبت شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت ورود خودرو: " + ex.Message;
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterIndividualExitAsync(int employeeId)
        {
            var user = await _userManager.GetUserAsync(User);
            var building = await _context.Buildings.FirstOrDefaultAsync();
            await _employeeStatusService.RegisterHourlyExitAsync(employeeId, user.Id, null, "خروج ساعتی", null);
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterIndividualEntryAsync(int employeeId)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user != null) {
                await _employeeStatusService.RegisterEmployeeEntryAsync(employeeId, user.Id);
            }
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterEmployeeHourlyExitAsync(int employeeId, string exitType, string permitNumber, string notes)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    TempData["ErrorMessage"] = "کاربر یافت نشد.";
                    return RedirectToPage();
                }

                await _employeeStatusService.RegisterHourlyExitAsync(employeeId, user.Id, permitNumber, notes, null);
                TempData["SuccessMessage"] = "خروج ساعتی با موفقیت ثبت شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت خروج ساعتی: " + ex.Message;
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterEmployeeMissionAsync(int employeeId, string exitType, string permitNumber, string notes)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    TempData["ErrorMessage"] = "کاربر یافت نشد.";
                    return RedirectToPage();
                }

                await _employeeStatusService.RegisterHourlyExitAsync(employeeId, user.Id, permitNumber, notes, null);
                TempData["SuccessMessage"] = "ماموریت اداری با موفقیت ثبت شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت ماموریت: " + ex.Message;
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterEmployeeFinalExitAsync(int employeeId, string exitType, string permitNumber, string notes)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    TempData["ErrorMessage"] = "کاربر یافت نشد.";
                    return RedirectToPage();
                }

                await _employeeStatusService.RegisterFinalExitAsync(employeeId, user.Id, notes);
                TempData["SuccessMessage"] = "خروج نهایی با موفقیت ثبت شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت خروج نهایی: " + ex.Message;
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostRegisterOfficialMissionAsync(int employeeId)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user != null) {
                await _employeeStatusService.RegisterOfficialMissionAsync(employeeId, user.Id, "ماموریت اداری");
            }
            return RedirectToPage();
        }



        public async Task<IActionResult> OnPostRegisterQuickEntryAsync(int employeeId)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user != null) {
                await _employeeStatusService.RegisterEmployeeEntryAsync(employeeId, user.Id);
            }
            return RedirectToPage();
        }

        public string GetWelcomeMessage()
        {
            var hour = DateTime.Now.Hour;
            var greeting = hour switch
            {
                >= 5 and < 12 => "صبح بخیر",
                >= 12 and < 17 => "ظهر بخیر",
                >= 17 and < 20 => "عصر بخیر",
                _ => "شب بخیر"
            };

            return $"{greeting}، {CurrentUser.UserName}";
        }

        // متد دیباگ برای بررسی وضعیت کارمندان
        public async Task<IActionResult> OnPostDebugEmployeeStatusesAsync()
        {
            try
            {
                var today = DateTime.Today;
                var allStatuses = await _context.EmployeeStatuses
                    .Include(es => es.Employee)
                    .Where(es => es.Date.Date == today)
                    .ToListAsync();

                var debugInfo = new
                {
                    TotalStatuses = allStatuses.Count,
                    HasInitialEntryTrue = allStatuses.Count(s => s.HasInitialEntry),
                    HasInitialEntryFalse = allStatuses.Count(s => !s.HasInitialEntry),
                    IsPresentInBuildingTrue = allStatuses.Count(s => s.IsPresentInBuilding),
                    IsPresentInBuildingFalse = allStatuses.Count(s => !s.IsPresentInBuilding),
                    FinalExitTimeNotNull = allStatuses.Count(s => s.FinalExitTime.HasValue),
                    Statuses = allStatuses.Select(s => new
                    {
                        EmployeeName = $"{s.Employee.FirstName} {s.Employee.LastName}",
                        HasInitialEntry = s.HasInitialEntry,
                        IsPresentInBuilding = s.IsPresentInBuilding,
                        CurrentStatus = s.CurrentStatus.ToString(),
                        FinalExitTime = s.FinalExitTime
                    }).ToList()
                };

                TempData["DebugInfo"] = $"اطلاعات دیباگ: {debugInfo.TotalStatuses} کل رکورد، {debugInfo.HasInitialEntryTrue} ورود اولیه، {debugInfo.IsPresentInBuildingTrue} حاضر در ساختمان، {debugInfo.FinalExitTimeNotNull} خروج نهایی";
                
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در دیباگ: " + ex.Message;
                return RedirectToPage();
            }
        }

        // متد برای به‌روزرسانی رکوردهای موجود
        public async Task<IActionResult> OnPostFixEmployeeStatusesAsync()
        {
            try
            {
                var today = DateTime.Today;
                var allStatuses = await _context.EmployeeStatuses
                    .Where(es => es.Date.Date == today)
                    .ToListAsync();

                int updatedCount = 0;

                foreach (var status in allStatuses)
                {
                    bool needsUpdate = false;

                    // اگر EntryTime دارد، یعنی ورود اولیه داشته
                    if (status.EntryTime.HasValue && !status.HasInitialEntry)
                    {
                        status.HasInitialEntry = true;
                        needsUpdate = true;
                    }

                    // اگر EntryTime دارد ولی FinalExitTime هم دارد، این اشتباه است
                    // برای ورود اولیه نباید FinalExitTime داشته باشد
                    if (status.EntryTime.HasValue && status.FinalExitTime.HasValue)
                    {
                        status.FinalExitTime = null; // پاک کردن زمان خروج نهایی
                        status.CurrentStatus = EmployeeCurrentStatus.PresentInBuilding;
                        status.IsPresentInBuilding = true;
                        needsUpdate = true;
                    }

                    // اگر CurrentStatus = PresentInBuilding است، باید در ساختمان باشد
                    if (status.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding && !status.IsPresentInBuilding)
                    {
                        status.IsPresentInBuilding = true;
                        needsUpdate = true;
                    }

                    if (needsUpdate)
                    {
                        status.LastUpdated = DateTime.Now;
                        updatedCount++;
                    }
                }

                if (updatedCount > 0)
                {
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = $"{updatedCount} رکورد با موفقیت به‌روزرسانی شد.";
                }
                else
                {
                    TempData["InfoMessage"] = "هیچ رکوردی نیاز به به‌روزرسانی نداشت.";
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در به‌روزرسانی: " + ex.Message;
                return RedirectToPage();
            }
        }
    }

    public class SearchResultModel
    {
        public Car Car { get; set; }
        public Employee Employee { get; set; }
        public EmployeeStatus EmployeeStatus { get; set; }
    }
}
