using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    [AuthorizePermission("DELETE_EMPLOYEE")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class DeleteModel : PageModel
    {
        private readonly EmployeeService _employeeService;

        public DeleteModel(EmployeeService employeeService)
        {
            _employeeService = employeeService;
        }

        [BindProperty]
        public int EmployeeId { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            EmployeeId = id;

            var employee = await _employeeService.GetEmployeeByIdAsync(id);
            if (employee == null)
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var employee = await _employeeService.GetEmployeeByIdAsync(EmployeeId);
            if (employee == null)
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _employeeService.DeleteEmployeeAsync(EmployeeId);
                TempData["SuccessMessage"] = "کارمند با موفقیت حذف شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف کارمند: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
