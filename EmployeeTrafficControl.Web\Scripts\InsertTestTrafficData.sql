-- ==========================================
-- اسکریپت اضافه کردن داده‌های تست تردد
-- Employee Traffic Control System
-- ==========================================

USE [EmployeeTrafficControlDb]
GO

PRINT '=========================================='
PRINT 'اضافه کردن داده‌های تست تردد'
PRINT 'تاریخ اجرا: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- متغیرهای مورد نیاز
DECLARE @EmployeeId INT
DECLARE @BuildingId INT
DECLARE @UserId NVARCHAR(450)
DECLARE @Today DATE = GETDATE()
DECLARE @Yesterday DATE = DATEADD(DAY, -1, GETDATE())

-- دریافت اولین کارمند
SELECT TOP 1 @EmployeeId = EmployeeId, @BuildingId = BuildingId FROM Employees ORDER BY EmployeeId
SELECT TOP 1 @UserId = Id FROM AspNetUsers ORDER BY Id

IF @EmployeeId IS NULL OR @BuildingId IS NULL OR @UserId IS NULL
BEGIN
    PRINT 'خطا: کارمند، ساختمان یا کاربر یافت نشد!'
    RETURN
END

PRINT 'کارمند انتخاب شده: ' + CAST(@EmployeeId AS VARCHAR)
PRINT 'ساختمان: ' + CAST(@BuildingId AS VARCHAR)
PRINT 'کاربر: ' + @UserId

BEGIN TRANSACTION TestTrafficData

BEGIN TRY
    -- حذف داده‌های تست قبلی (اختیاری)
    DELETE FROM TrafficLogs 
    WHERE EmployeeId = @EmployeeId 
      AND (Description LIKE '%تست%' OR Description LIKE '%test%')
    
    PRINT 'داده‌های تست قبلی حذف شدند: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' رکورد'

    -- 1. ترددهای امروز
    PRINT '1. اضافه کردن ترددهای امروز...'
    
    -- ورود صبح
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, EntryTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 8, @Today), 
            'ورود روزانه', 
            'تست ورود صبح', 
            @UserId)
    
    -- خروج ساعتی برای ناهار
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 12, @Today), 
            'خروج ساعتی', 
            'تست خروج برای ناهار', 
            @UserId)
    
    -- ورود بعد از ناهار
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, EntryTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(MINUTE, 30, DATEADD(HOUR, 12, @Today)), 
            'ورود از خروج ساعتی', 
            'تست ورود بعد از ناهار', 
            @UserId)
    
    -- خروج برای ماموریت
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 14, @Today), 
            'ماموریت', 
            'تست خروج برای ماموریت', 
            @UserId)
    
    -- ورود از ماموریت
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, EntryTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 15, @Today), 
            'ورود از ماموریت', 
            'تست ورود از ماموریت', 
            @UserId)
    
    -- خروج نهایی
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 16, @Today), 
            'خروج نهایی', 
            'تست خروج پایان کار', 
            @UserId)

    PRINT 'ترددهای امروز اضافه شدند: 6 رکورد'

    -- 2. ترددهای دیروز
    PRINT '2. اضافه کردن ترددهای دیروز...'
    
    -- ورود صبح
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, EntryTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 8, @Yesterday), 
            'ورود روزانه', 
            'تست ورود صبح دیروز', 
            @UserId)
    
    -- خروج ساعتی 1
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 10, @Yesterday), 
            'مرخصی', 
            'تست مرخصی کوتاه', 
            @UserId)
    
    -- ورود از خروج ساعتی 1
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, EntryTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(MINUTE, 15, DATEADD(HOUR, 10, @Yesterday)), 
            'ورود از مرخصی', 
            'تست ورود از مرخصی', 
            @UserId)
    
    -- خروج ساعتی 2
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 12, @Yesterday), 
            'خروج ساعتی', 
            'تست خروج ناهار دیروز', 
            @UserId)
    
    -- ورود از خروج ساعتی 2
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, EntryTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(MINUTE, 45, DATEADD(HOUR, 12, @Yesterday)), 
            'ورود از خروج ساعتی', 
            'تست ورود از ناهار دیروز', 
            @UserId)
    
    -- خروج نهایی
    INSERT INTO TrafficLogs (EmployeeId, BuildingId, ExitTime, TrafficType, Description, UserId)
    VALUES (@EmployeeId, @BuildingId, 
            DATEADD(HOUR, 16, @Yesterday), 
            'خروج نهایی', 
            'تست خروج پایان کار دیروز', 
            @UserId)

    PRINT 'ترددهای دیروز اضافه شدند: 6 رکورد'

    -- 3. اضافه کردن وضعیت‌های کارمند
    PRINT '3. اضافه کردن وضعیت‌های کارمند...'
    
    -- حذف وضعیت‌های قبلی
    DELETE FROM EmployeeStatuses 
    WHERE EmployeeId = @EmployeeId 
      AND Date IN (@Today, @Yesterday)
    
    -- وضعیت امروز
    INSERT INTO EmployeeStatuses (EmployeeId, Date, EntryTime, FinalExitTime, CurrentStatus, LastUpdated)
    VALUES (@EmployeeId, @Today, 
            DATEADD(HOUR, 8, @Today), 
            NULL, -- هنوز خروج نهایی نکرده
            'Present', 
            GETDATE())
    
    -- وضعیت دیروز
    INSERT INTO EmployeeStatuses (EmployeeId, Date, EntryTime, FinalExitTime, CurrentStatus, LastUpdated)
    VALUES (@EmployeeId, @Yesterday, 
            DATEADD(HOUR, 8, @Yesterday), 
            DATEADD(HOUR, 16, @Yesterday), 
            'FinalExit', 
            @Yesterday)

    PRINT 'وضعیت‌های کارمند اضافه شدند: 2 رکورد'

    COMMIT TRANSACTION TestTrafficData

    PRINT '=========================================='
    PRINT 'داده‌های تست با موفقیت اضافه شدند!'
    PRINT '=========================================='
    PRINT 'خلاصه:'
    PRINT 'کارمند: ' + CAST(@EmployeeId AS VARCHAR)
    PRINT 'امروز: 6 تردد (شامل 1 خروج ساعتی + 1 ماموریت)'
    PRINT 'دیروز: 6 تردد (شامل 2 خروج ساعتی)'
    PRINT 'وضعیت‌ها: 2 رکورد'
    PRINT '=========================================='

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION TestTrafficData
    
    PRINT '=========================================='
    PRINT 'خطا در اضافه کردن داده‌های تست!'
    PRINT 'شماره خطا: ' + CAST(ERROR_NUMBER() AS VARCHAR)
    PRINT 'پیام خطا: ' + ERROR_MESSAGE()
    PRINT 'تمام تغییرات برگردانده شد'
    PRINT '=========================================='
END CATCH

-- نمایش نتیجه نهایی
PRINT 'بررسی نهایی:'
SELECT 
    'TrafficLogs' AS [جدول],
    COUNT(*) AS [تعداد_کل],
    COUNT(CASE WHEN Description LIKE '%تست%' THEN 1 END) AS [تعداد_تست]
FROM TrafficLogs
WHERE EmployeeId = @EmployeeId

UNION ALL

SELECT 
    'EmployeeStatuses',
    COUNT(*),
    COUNT(CASE WHEN Date IN (@Today, @Yesterday) THEN 1 END)
FROM EmployeeStatuses
WHERE EmployeeId = @EmployeeId
