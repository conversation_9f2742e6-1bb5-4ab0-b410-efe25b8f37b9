 using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Web.Attributes;
using System.Linq;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    [AuthorizePermission("VIEW_REPORTS")]
    public class OutOfBuildingModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public OutOfBuildingModel(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public List<EmployeeStatus> EmployeesOutOfBuilding { get; set; } = new();
        public List<Building> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        public int TotalOutOfBuilding { get; set; }
        public int HourlyExitCount { get; set; }
        public int OfficialMissionCount { get; set; }
        public int InVehicleCount { get; set; }
        public bool HasAnyEmployeeEnteredToday { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
                return RedirectToPage("/Account/Login");

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return RedirectToPage("/Account/Login");

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanViewReports(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            await LoadDataAsync(currentUser);
            return Page();
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            var today = DateTime.Today;
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            Buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();

            // وضعیت‌های امروز
            var statusQuery = _context.EmployeeStatuses
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                .Include(es => es.Employee)
                    .ThenInclude(e => e.Building)
                .Include(es => es.CurrentVehicle)
                .Where(es => es.Date.Date == today &&
                             (es.CurrentStatus == EmployeeCurrentStatus.OutOfOffice ||
                              es.CurrentStatus == EmployeeCurrentStatus.HourlyExit ||
                              es.CurrentStatus == EmployeeCurrentStatus.OfficialMission));

            if (accessibleBuildingIds != null)
                statusQuery = statusQuery.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            if (BuildingId.HasValue)
                statusQuery = statusQuery.Where(es => es.Employee.BuildingId == BuildingId.Value);

            EmployeesOutOfBuilding = await statusQuery.ToListAsync();

            // آمار
            TotalOutOfBuilding = EmployeesOutOfBuilding.Count;
            HourlyExitCount = EmployeesOutOfBuilding.Count(es => es.CurrentStatus == EmployeeCurrentStatus.HourlyExit);
            OfficialMissionCount = EmployeesOutOfBuilding.Count(es => es.CurrentStatus == EmployeeCurrentStatus.OfficialMission);
            InVehicleCount = EmployeesOutOfBuilding.Count(es => es.IsInVehicle);

            // آیا امروز کسی ورود داشته است؟
            HasAnyEmployeeEnteredToday = await _context.EmployeeStatuses.AnyAsync(es => es.Date.Date == today && es.EntryTime != null);
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            if (user.Role?.Code == "ADMIN")
                return null;
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };
            return null;
        }

        private bool CanViewReports(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") ||
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }
    }
}