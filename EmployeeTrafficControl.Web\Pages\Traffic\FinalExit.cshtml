
@page
@model EmployeeTrafficControl.Web.Pages.Traffic.FinalExitModel
@{
    ViewData["Title"] = "ثبت خروج نهایی گروهی";
}

<h1 class="h3 mb-4 text-gray-800">@ViewData["Title"]</h1>

@if (!Model.PresentEmployees.Any())
{
    <div class="alert alert-info">در حال حاضر هیچ کارمندی در ساختمان حضور ندارد.</div>
}
else
{
    <form method="post">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">لیست کارمندان حاضر در ساختمان</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" /></th>
                                <th>نام</th>
                                <th>کد پرسنلی</th>
                                <th>وضعیت</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var status in Model.PresentEmployees)
                            {
                                <tr>
                                    <td><input type="checkbox" name="selectedEmployeeIds" value="@status.EmployeeId" class="employee-checkbox" /></td>
                                    <td>@status.Employee.FirstName @status.Employee.LastName</td>
                                    <td>@status.Employee.PersonnelCode</td>
                                    <td><span class="badge @status.GetStatusBadgeClass()">@status.GetStatusDisplayName()</span></td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <button type="submit" class="btn btn-danger">ثبت خروج نهایی برای انتخاب شده‌ها</button>
            </div>
        </div>
    </form>
}

@section Scripts {
    <script>
        document.getElementById('selectAll').addEventListener('change', function (e) {
            const checkboxes = document.querySelectorAll('.employee-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });
    </script>
}
