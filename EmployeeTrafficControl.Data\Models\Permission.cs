using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Data.Models
{
    /// <summary>
    /// مدل مجوزهای سیستم
    /// </summary>
    public class Permission
    {
        [Key]
        [Display(Name = "شناسه مجوز")]
        public int PermissionId { get; set; }

        [Required(ErrorMessage = "نام مجوز اجباری است.")]
        [StringLength(100, ErrorMessage = "نام مجوز حداکثر 100 کاراکتر باشد.")]
        [Display(Name = "نام مجوز")]
        public string Name { get; set; }

        [Required(ErrorMessage = "کد مجوز اجباری است.")]
        [StringLength(100, ErrorMessage = "کد مجوز حداکثر 100 کاراکتر باشد.")]
        [Display(Name = "کد مجوز")]
        public string Code { get; set; }

        [StringLength(500, ErrorMessage = "توضیحات حداکثر 500 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "دسته‌بندی مجوز اجباری است.")]
        [StringLength(50, ErrorMessage = "دسته‌بندی حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "دسته‌بندی")]
        public string Category { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاریخ ایجاد")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
        public ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }
}
