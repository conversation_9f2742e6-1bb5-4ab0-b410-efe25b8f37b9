﻿using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Services
{
    public class JobService
    {
        private readonly ApplicationDbContext _context;

        public JobService(ApplicationDbContext context)
        {
            _context = context;
        }

        // --- CRUD Operations for Jobs ---

        public async Task<List<Job>> GetAllJobsAsync()
        {
            return await _context.Jobs
                                 .Include(j => j.Building)
                                 .OrderBy(j => j.Building!.Name)
                                 .ThenBy(j => j.Title)
                                 .ToListAsync();
        }

        public async Task<Job?> GetJobByIdAsync(int jobId)
        {
            return await _context.Jobs
                                 .Include(j => j.Building)
                                 .FirstOrDefaultAsync(j => j.JobId == jobId);
        }

        public async Task<Job> AddJobAsync(Job job)
        {
            _context.Jobs.Add(job);
            await _context.SaveChangesAsync();
            return job;
        }

        public async Task<bool> UpdateJobAsync(Job job)
        {
            var existingJob = await _context.Jobs.FindAsync(job.JobId);
            if (existingJob == null)
            {
                return false;
            }

            existingJob.Title = job.Title;
            existingJob.BuildingId = job.BuildingId;

            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (DbUpdateConcurrencyException)
            {
                return false;
            }
        }

        public async Task<bool> DeleteJobAsync(int jobId)
        {
            var jobToDelete = await _context.Jobs.FindAsync(jobId);
            if (jobToDelete == null)
            {
                return false;
            }
            // Check if there are any employees assigned to this job before deleting
            var hasEmployees = await _context.Employees.AnyAsync(e => e.JobId == jobId);
            if (hasEmployees)
            {
                // In a real application, you might want to show an error message to the user
                // or offer to reassign employees before deletion.
                Console.WriteLine("Cannot delete job: Employees are assigned to it.");
                return false;
            }

            _context.Jobs.Remove(jobToDelete);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Checks if a job with the given title already exists (case-insensitive).
        /// </summary>
        /// <param name="jobTitle">The title of the job to check.</param>
        /// <param name="excludeJobId">Optional: An ID to exclude during the check (useful for updates).</param>
        /// <returns>True if a job with the title exists, otherwise false.</returns>
        public async Task<bool> JobExistsAsync(string jobTitle, int? excludeJobId = null)
        {
            if (excludeJobId.HasValue)
            {
                return await _context.Jobs.AnyAsync(j =>
                    j.Title.ToLower() == jobTitle.ToLower() && j.JobId != excludeJobId.Value);
            }
            return await _context.Jobs.AnyAsync(j => j.Title.ToLower() == jobTitle.ToLower());
        }

        /// <summary>
        /// Checks if a job with the given title already exists in a specific building (case-insensitive).
        /// </summary>
        /// <param name="jobTitle">The title of the job to check.</param>
        /// <param name="buildingId">The ID of the building to check within.</param>
        /// <param name="excludeJobId">Optional: An ID to exclude during the check (useful for updates).</param>
        /// <returns>True if a job with the title exists in the building, otherwise false.</returns>
        public async Task<bool> JobExistsInBuildingAsync(string jobTitle, int buildingId, int? excludeJobId = null)
        {
            if (excludeJobId.HasValue)
            {
                return await _context.Jobs.AnyAsync(j =>
                    j.Title.ToLower() == jobTitle.ToLower() &&
                    j.BuildingId == buildingId &&
                    j.JobId != excludeJobId.Value);
            }
            return await _context.Jobs.AnyAsync(j =>
                j.Title.ToLower() == jobTitle.ToLower() &&
                j.BuildingId == buildingId);
        }
    }
}