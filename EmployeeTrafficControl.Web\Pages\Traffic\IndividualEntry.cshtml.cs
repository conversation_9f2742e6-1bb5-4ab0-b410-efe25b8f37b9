using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services; // For DailyAttendanceService (if needed, though TrafficLogService should handle it)

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    //[AuthorizePermission("REGISTER_HOURLY_ENTRY")]
    public class IndividualEntryModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService; // New service

        public IndividualEntryModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            TrafficLogService trafficLogService) // Inject new service
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
        }

        public List<EmployeeStatus> EmployeesCanEnter { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        public int OutOfOfficeCount { get; set; }
        public int HourlyExitCount { get; set; }
        public int MissionCount { get; set; }
        public int PresentCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            var today = DateTime.Today;

            // Build the query to fetch employees and their statuses, applying filters server-side
            var employeesQuery = _context.Employees
                .Include(e => e.Building)
                .Include(e => e.Job)
                .Where(e => e.IsActive);

            // Apply accessible building filter
            if (accessibleBuildingIds != null)
            {
                employeesQuery = employeesQuery.Where(e => accessibleBuildingIds.Contains(e.BuildingId));
            }

            // Apply building filter
            if (BuildingId.HasValue)
            {
                employeesQuery = employeesQuery.Where(e => e.BuildingId == BuildingId.Value);
            }

            // Left join with EmployeeStatuses for today
            var todayStatuses = await _context.EmployeeStatuses.Where(es => es.Date.Date == today).ToListAsync();
            var employeesList = await employeesQuery.ToListAsync();
            var employeesWithStatuses = employeesList
                .GroupJoin(todayStatuses,
                           emp => emp.EmployeeId,
                           status => status.EmployeeId,
                           (emp, statuses) => new { Employee = emp, Status = statuses.FirstOrDefault() })
                .ToList(); // Now in memory

            // Apply status filter in memory
            if (!string.IsNullOrWhiteSpace(StatusFilter))
            {
                employeesWithStatuses = StatusFilter.ToLower() switch
                {
                    "out" => employeesWithStatuses.Where(item => item.Status == null || (item.Status.CurrentStatus == EmployeeCurrentStatus.OutOfOffice && !item.Status.IsPresentInBuilding)).ToList(),
                    "hourly" => employeesWithStatuses.Where(item => item.Status != null && item.Status.CurrentStatus == EmployeeCurrentStatus.HourlyExit && !item.Status.IsPresentInBuilding).ToList(),
                    "mission" => employeesWithStatuses.Where(item => item.Status != null && item.Status.CurrentStatus == EmployeeCurrentStatus.OfficialMission && !item.Status.IsPresentInBuilding).ToList(),
                    _ => employeesWithStatuses.Where(item => item.Status == null || (item.Status.CurrentStatus == EmployeeCurrentStatus.OutOfOffice && !item.Status.IsPresentInBuilding) || (item.Status.CurrentStatus == EmployeeCurrentStatus.HourlyExit && !item.Status.IsPresentInBuilding) || (item.Status.CurrentStatus == EmployeeCurrentStatus.OfficialMission && !item.Status.IsPresentInBuilding)).ToList()
                };
            }
            else
            {
                // Default filtering when no status filter is applied: show OutOfOffice (including those with no status), HourlyExit, and OfficialMission
                employeesWithStatuses = employeesWithStatuses.Where(item => item.Status == null || (item.Status.CurrentStatus == EmployeeCurrentStatus.OutOfOffice && !item.Status.IsPresentInBuilding) || (item.Status.CurrentStatus == EmployeeCurrentStatus.HourlyExit && !item.Status.IsPresentInBuilding) || (item.Status.CurrentStatus == EmployeeCurrentStatus.OfficialMission && !item.Status.IsPresentInBuilding)).ToList();
            }

            // Apply search filter in memory
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                employeesWithStatuses = employeesWithStatuses.Where(item =>
                    item.Employee.FirstName.ToLower().Contains(searchLower) ||
                    item.Employee.LastName.ToLower().Contains(searchLower) ||
                    item.Employee.PersonnelCode.ToLower().Contains(searchLower)).ToList();
            }

            // Materialize the results and project into EmployeeStatus objects (creating default if needed)
            EmployeesCanEnter = employeesWithStatuses
                .Select(item => item.Status ?? new EmployeeStatus
                {
                    StatusId = 0, // Default value for new status
                    EmployeeId = item.Employee.EmployeeId,
                    Employee = item.Employee,
                    Date = today,
                    HasInitialEntry = false,
                    IsPresentInBuilding = false,
                    CurrentStatus = EmployeeCurrentStatus.OutOfOffice, // Default status if no entry for today
                    EntryTime = null,
                    FinalExitTime = null,
                    LastUpdated = DateTime.Now, // Or DateTime.UtcNow
                    UpdatedByUserId = null,
                    Notes = null,
                    IsInVehicle = false,
                    CurrentVehicleId = null,
                    ExitPermitNumber = null,
                    UpdatedByUser = null,
                    CurrentVehicle = null
                })
                .OrderBy(es => es.Employee.Building.Name) // Order by Building Name
                .ThenBy(es => es.Employee.FirstName)
                .ThenBy(es => es.Employee.LastName)
                .ToList(); // Execute the query

            // Calculate stats based on the materialized list
            OutOfOfficeCount = EmployeesCanEnter.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice && !s.IsPresentInBuilding);
            PresentCount = EmployeesCanEnter.Count(s => s.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding && s.IsPresentInBuilding && !s.FinalExitTime.HasValue);
            HourlyExitCount = EmployeesCanEnter.Count(s => s.CurrentStatus == EmployeeCurrentStatus.HourlyExit && !s.IsPresentInBuilding && !s.FinalExitTime.HasValue);
            MissionCount = EmployeesCanEnter.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OfficialMission && !s.IsPresentInBuilding && !s.FinalExitTime.HasValue);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;
            
            // Fetch all active employees with their building and job
            var allEmployeesQuery = _context.Employees
                .Include(e => e.Building)
                .Include(e => e.Job)
                .Where(e => e.IsActive);

            // Fetch today's statuses for all employees
            var todayStatuses = await _context.EmployeeStatuses
                .Where(es => es.Date.Date == today)
                .ToDictionaryAsync(es => es.EmployeeId);

            // Combine employees and their statuses, creating default status if none exists for today
            var combinedEmployees = await allEmployeesQuery
                .Select(e => new
                {
                    Employee = e,
                    Status = todayStatuses.ContainsKey(e.EmployeeId) ? todayStatuses[e.EmployeeId] : null
                })
                .ToListAsync();

            // Calculate stats based on the combined list
            var statuses = combinedEmployees
                .Select(item => item.Status ?? new EmployeeStatus
                {
                    CurrentStatus = EmployeeCurrentStatus.OutOfOffice, // Default status if no entry for today
                    IsPresentInBuilding = false // Default presence if no entry for today
                })
                .AsQueryable(); // Convert back to AsQueryable for filtering

            // Apply accessible building filter for stats
            if (accessibleBuildingIds != null)
            {
                statuses = statuses.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            }

            // Apply building filter for stats
            if (BuildingId.HasValue)
            {
                statuses = statuses.Where(es => es.Employee.BuildingId == BuildingId.Value);
            }

            // کارمندانی که خارج از ساختمان هستند (شامل کسانی که ورود اولیه نکرده‌اند یا خروج موقت داشته‌اند)
            OutOfOfficeCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice && !s.IsPresentInBuilding);
            
            // کارمندانی که در ساختمان حضور دارند
            PresentCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding && s.IsPresentInBuilding && !s.FinalExitTime.HasValue);
            
            // کارمندانی که خروج ساعتی دارند
            HourlyExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.HourlyExit && !s.IsPresentInBuilding && !s.FinalExitTime.HasValue);
            
            // کارمندانی که ماموریت اداری دارند
            MissionCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OfficialMission && !s.IsPresentInBuilding && !s.FinalExitTime.HasValue);
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") ||
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }

        public async Task<IActionResult> OnPostRegisterEntryAsync(int employeeId)
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                var success = await _trafficLogService.RegisterEntryAsync(
                    employeeId,
                    currentUser.BuildingId ?? 1, // Assuming a default building ID
                    currentUser.Id,
                    DateTime.Now,
                    "ورود از طریق فرم IndividualEntry");

                if (success)
                {
                    TempData["SuccessMessage"] = "ورود کارمند با موفقیت ثبت شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "خطا در ثبت ورود کارمند.";
                }
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در ثبت ورود اولیه: " + ex.Message;
                return RedirectToPage();
            }
        }
    }
}