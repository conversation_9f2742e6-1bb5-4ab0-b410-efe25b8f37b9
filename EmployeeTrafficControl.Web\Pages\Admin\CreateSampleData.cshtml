@page
@model EmployeeTrafficControl.Web.Pages.Admin.CreateSampleDataModel
@{
    ViewData["Title"] = "ایجاد داده‌های نمونه";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="bi bi-database-add"></i> ایجاد داده‌های نمونه
            </h2>
            <p class="text-muted mb-0">ایجاد ساختمان، شغل‌ها، کارمندان و خودروهای نمونه برای تست سیستم</p>
        </div>
        <div>
            <a href="/dashboard" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>

    <!-- نمایش پیام -->
    @if (!string.IsNullOrEmpty(Model.Message))
    {
        <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
            <i class="bi @(Model.IsSuccess ? "bi-check-circle" : "bi-exclamation-triangle")"></i>
            @Model.Message
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- کارت اصلی -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i> اطلاعات مهم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">
                            <i class="bi bi-exclamation-triangle"></i> توجه!
                        </h6>
                        <p class="mb-0">
                            این عملیات داده‌های نمونه زیر را ایجاد می‌کند:
                        </p>
                        <ul class="mt-2 mb-0">
                            <li>یک ساختمان نمونه (در صورت عدم وجود)</li>
                            <li>چهار شغل نمونه: نگهبان، راننده، خزانه‌دار، کارمند اداری</li>
                            <li>چهار کارمند نمونه</li>
                            <li>سه خودرو نمونه (شامل یک خودرو پولرسان)</li>
                        </ul>
                        <hr>
                        <p class="mb-0">
                            <strong>نکته:</strong> اگر داده‌هایی از قبل وجود داشته باشند، تکرار نخواهند شد.
                        </p>
                    </div>

                    <div class="text-center">
                        <form method="post">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-database-add"></i>
                                ایجاد داده‌های نمونه
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- راهنمای استفاده -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-question-circle"></i> راهنمای استفاده
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-1-circle text-primary"></i> پس از ایجاد داده‌های نمونه:</h6>
                            <ul>
                                <li>می‌توانید از صفحه خروج خودرو استفاده کنید</li>
                                <li>کارمندان نمونه در لیست‌ها نمایش داده می‌شوند</li>
                                <li>خودروهای نمونه در پارکینگ قرار دارند</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-2-circle text-success"></i> مدیریت داده‌ها:</h6>
                            <ul>
                                <li>از منوی مدیریت کارمندان برای ویرایش استفاده کنید</li>
                                <li>از منوی مدیریت خودروها برای تغییرات استفاده کنید</li>
                                <li>می‌توانید داده‌های جدید اضافه کنید</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }

    .btn {
        border-radius: 0.375rem;
    }

    .alert {
        border-radius: 0.5rem;
    }
</style>
