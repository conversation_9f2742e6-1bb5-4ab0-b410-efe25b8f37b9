using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services; // Keep this for other services if needed
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using static EmployeeTrafficControl.Data.Models.EmployeeStatus;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    [AuthorizePermission("CAR_ENTRY")]
    public class CarEntryNewModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService; // New service

        public CarEntryNewModel(ApplicationDbContext context, UserManager<ApplicationUser> userManager, TrafficLogService trafficLogService)
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
        }

        [BindProperty]
        public int? CarId { get; set; }
        [BindProperty]
        public int? DriverEmployeeId { get; set; }
        [BindProperty]
        public int? BuildingId { get; set; }
        [BindProperty]
        public string? PlateNumber { get; set; }
        [BindProperty]
        public string? Notes { get; set; }
        public string? SearchTerm { get; set; }
        public int TotalCarsOutside { get; set; }
        public int EmployeeCarsOutside { get; set; }
        public int GuestCarsOutside { get; set; }
        public List<CarTrafficLog> CarsOutside { get; set; } = new();
        public CarTrafficLog? SelectedCar { get; set; }

        [BindProperty]
        public List<int> SelectedPassengers { get; set; } = new();

        public List<SelectListItem> Cars { get; set; } = new();
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        public async Task<IActionResult> OnGetAsync(int? selectedCarId = null)
        {
            if (!User.Identity?.IsAuthenticated == true)
                return RedirectToPage("/Account/Login");

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return RedirectToPage("/Account/Login");

            // اگر خودرو انتخاب شده باشد
            if (selectedCarId.HasValue)
            {
                SelectedCar = await _context.CarTrafficLogs
                    .Include(ctl => ctl.Car)
                    .Include(ctl => ctl.DriverEmployee)
                    .Include(ctl => ctl.Building)
                    .Include(ctl => ctl.CarPassengers)
                        .ThenInclude(cp => cp.Employee)
                            .ThenInclude(e => e.Job)
                    .FirstOrDefaultAsync(ctl => ctl.CarTrafficLogId == selectedCarId.Value);

                // فیلتر کردن سرنشینانی که ورود فردی ثبت کرده‌اند
                if (SelectedCar != null)
                {
                    // دریافت کارمندان حاضر در ساختمان (که ورود فردی ثبت کرده‌اند)
                    // This logic is still relevant for filtering passengers for display.
                    var presentEmployees = await _context.EmployeeStatuses
                        .Where(es => es.Date.Date == DateTime.Today &&
                                   es.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding &&
                                   es.Employee.BuildingId == SelectedCar.BuildingId)
                        .Select(es => es.Employee.EmployeeId)
                        .ToListAsync();
   
                    // حذف سرنشینانی که ورود فردی ثبت کرده‌اند
                    var filteredPassengers = SelectedCar.CarPassengers
                        .Where(cp => !cp.IsEntered && !presentEmployees.Contains(cp.EmployeeId))
                        .ToList();
   
                    // جایگزینی لیست سرنشینان با لیست فیلتر شده
                    SelectedCar.CarPassengers = filteredPassengers;
                }
            }

            await LoadSelectListsAsync();
            await LoadCarsOutsideAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!User.Identity?.IsAuthenticated == true)
                return RedirectToPage("/Account/Login");

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return RedirectToPage("/Account/Login");

            if (!ModelState.IsValid)
            {
                await LoadSelectListsAsync();
                await LoadCarsOutsideAsync();
                return Page();
            }

            var car = await _context.Cars.FindAsync(CarId);
            if (car == null)
            {
                ModelState.AddModelError("CarId", "خودرو انتخاب شده یافت نشد.");
                await LoadSelectListsAsync();
                await LoadCarsOutsideAsync();
                return Page();
            }

            var driver = await _context.Employees.FindAsync(DriverEmployeeId);
            if (driver == null)
            {
                ModelState.AddModelError("DriverEmployeeId", "راننده انتخاب شده یافت نشد.");
                await LoadSelectListsAsync();
                await LoadCarsOutsideAsync();
                return Page();
            }

            TempData["InfoMessage"] = "لطفا از داشبورد یکپارچه تردد برای ثبت ورود و خروج استفاده کنید.";
            return RedirectToPage("/Traffic/TrafficDashboard");

            TempData["SuccessMessage"] = "ورود خودرو با موفقیت ثبت شد.";
            return RedirectToPage("/dashboard/index");
        }

        private async Task LoadSelectListsAsync()
        {
            Cars = await _context.Cars
                .OrderBy(c => c.PlateNumber)
                .Select(c => new SelectListItem
                {
                    Value = c.CarId.ToString(),
                    Text = $"{c.PlateNumber} - {c.Model}"
                }).ToListAsync();

            Employees = await _context.Employees
                .Where(e => e.IsActive)
                .OrderBy(e => e.FirstName)
                .ThenBy(e => e.LastName)
                .Select(e => new SelectListItem
                {
                    Value = e.EmployeeId.ToString(),
                    Text = $"{e.FirstName} {e.LastName} ({e.PersonnelCode})"
                }).ToListAsync();

            Buildings = await _context.Buildings
                .OrderBy(b => b.Name)
                .Select(b => new SelectListItem
                {
                    Value = b.BuildingId.ToString(),
                    Text = b.Name
                }).ToListAsync();
        }

        private async Task LoadCarsOutsideAsync()
        {
            CarsOutside = await _context.CarTrafficLogs
                .Include(ctl => ctl.Car)
                .Include(ctl => ctl.DriverEmployee)
                .Include(ctl => ctl.Building)
                .Include(ctl => ctl.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                .Where(ctl => !ctl.IsInParking)
                .OrderByDescending(ctl => ctl.ExitTime)
                .ToListAsync();

            TotalCarsOutside = CarsOutside.Count;
            EmployeeCarsOutside = CarsOutside.Count(c => c.DriverEmployeeId > 0);
            GuestCarsOutside = CarsOutside.Count(c => c.DriverEmployeeId == 0);
        }
    }
}