using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    [AuthorizePermission("VIEW_EMPLOYEES")]
  
    public class IndexModel : PageModel
    {
        private readonly EmployeeService _employeeService;

        public IndexModel(EmployeeService employeeService)
        {
            _employeeService = employeeService;
        }

        public IList<Employee> Employees { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Employees = await _employeeService.GetAllEmployeesAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var employee = await _employeeService.GetEmployeeByIdAsync(id);
            if (employee == null)
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
                return RedirectToPage();
            }

            try
            {
                bool deleteResult = await _employeeService.DeleteEmployeeAsync(id);
                if (deleteResult)
                {
                    TempData["SuccessMessage"] = "کارمند با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این کارمند وجود ندارد زیرا دارای سوابق تردد یا حساب کاربری است.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف کارمند: " + ex.Message;
            }

            return RedirectToPage();
        }
    }
}
