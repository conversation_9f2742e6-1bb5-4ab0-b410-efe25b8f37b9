@page "/traffic/carentrynew"
@model EmployeeTrafficControl.Web.Pages.Traffic.CarEntryNewModel
@{
    ViewData["Title"] = "ثبت ورود خودرو";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-car-front-fill"></i> ثبت ورود خودرو</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">انتخاب خودرو و ثبت ورود راننده و سرنشینان</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">جستجو</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="شماره پلاک، راننده یا کد پرسنلی" />
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="/traffic/car-entry-new" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> بازنشانی
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card stats-total">
            <div class="stats-icon">
                <i class="bi bi-car-front"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TotalCarsOutside</h3>
                <p>کل خودروها خارج</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card stats-employee">
            <div class="stats-icon">
                <i class="bi bi-person-badge"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeeCarsOutside</h3>
                <p>خودرو کارمندان</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card stats-guest">
            <div class="stats-icon">
                <i class="bi bi-person-plus"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.GuestCarsOutside</h3>
                <p>خودرو مهمانان</p>
            </div>
        </div>
    </div>
</div>

@if (Model.SelectedCar != null)
{
    <!-- اطلاعات خودرو انتخاب شده -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-check-circle"></i> خودرو انتخاب شده: @Model.SelectedCar.Car.PlateNumber
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>اطلاعات خودرو:</h6>
                            <ul class="list-unstyled">
                                <li><strong>شماره پلاک:</strong> @Model.SelectedCar.Car.PlateNumber</li>
                                <li><strong>مدل:</strong> @(Model.SelectedCar.Car.Model ?? "نامشخص")</li>
                                <li><strong>رنگ:</strong> @(Model.SelectedCar.Car.Color ?? "نامشخص")</li>
                                <li><strong>نوع:</strong> @(Model.SelectedCar.Car.Type ?? "نامشخص")</li>
                                @if (Model.SelectedCar.Car.IsMoneyTransport)
                                {
                                    <li><span class="badge bg-warning">خودرو پولرسان</span></li>
                                }
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>اطلاعات راننده:</h6>
                            @if (Model.SelectedCar.DriverEmployee != null)
                            {
                                <ul class="list-unstyled">
                                    <li><strong>نام:</strong> @Model.SelectedCar.DriverEmployee.FirstName @Model.SelectedCar.DriverEmployee.LastName</li>
                                    <li><strong>کد پرسنلی:</strong> @Model.SelectedCar.DriverEmployee.PersonnelCode</li>
                                    <li><strong>سمت:</strong> @(Model.SelectedCar.DriverEmployee.Job?.Title ?? "نامشخص")</li>
                                    <li><strong>تلفن:</strong> @(Model.SelectedCar.DriverEmployee.PhoneNumber ?? "نامشخص")</li>
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted">خودرو مهمان</p>
                            }
                        </div>
                    </div>

                    @if (Model.SelectedCar.CarPassengers.Any())
                    {
                        <hr>
                        <h6>سرنشینان:</h6>
                        <form method="post">
                            <input type="hidden" asp-for="CarId" value="@Model.SelectedCar.Car.CarId" />
                            <input type="hidden" asp-for="DriverEmployeeId" value="@Model.SelectedCar.DriverEmployeeId" />
                            <input type="hidden" asp-for="BuildingId" value="@Model.SelectedCar.BuildingId" />
                            <input type="hidden" asp-for="Notes" value="@Model.SelectedCar.Notes" />

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAllPassengers">
                                        <label class="form-check-label fw-bold" for="selectAllPassengers">
                                            انتخاب همه سرنشینان
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                @foreach (var passenger in Model.SelectedCar.CarPassengers.Where(cp => !cp.IsEntered))
                                {
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input passenger-checkbox" type="checkbox"
                                                           id="<EMAIL>"
                                                           name="SelectedPassengers"
                                                           value="@passenger.EmployeeId">
                                                    <label class="form-check-label" for="<EMAIL>">
                                                        <strong>@passenger.Employee.FirstName @passenger.Employee.LastName</strong>
                                                        <br>
                                                        <small class="text-muted">
                                                            کد پرسنلی: @passenger.Employee.PersonnelCode |
                                                            سمت: @(passenger.Employee.Job?.Title ?? "نامشخص")
                                                        </small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="bi bi-check-circle"></i> ثبت ورود خودرو و سرنشینان انتخاب شده
                                    </button>
                                    <a href="/traffic/car-entry-new" class="btn btn-secondary btn-lg ms-2">
                                        <i class="bi bi-arrow-left"></i> انتخاب خودرو دیگر
                                    </a>
                                </div>
                            </div>
                        </form>
                    }
                    else
                    {
                        <hr>
                        <p class="text-muted">این خودرو سرنشین ندارد.</p>
                        <form method="post">
                            <input type="hidden" asp-for="CarId" value="@Model.SelectedCar.Car.CarId" />
                            <input type="hidden" asp-for="DriverEmployeeId" value="@Model.SelectedCar.DriverEmployeeId" />
                            <input type="hidden" asp-for="BuildingId" value="@Model.SelectedCar.BuildingId" />
                            <input type="hidden" asp-for="Notes" value="@Model.SelectedCar.Notes" />
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-check-circle"></i> ثبت ورود خودرو
                            </button>
                            <a href="/traffic/car-entry-new" class="btn btn-secondary btn-lg ms-2">
                                <i class="bi bi-arrow-left"></i> انتخاب خودرو دیگر
                            </a>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
}
else
{
    <!-- لیست خودروهای خارج از پارکینگ -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul"></i> خودروهای خارج از پارکینگ
                    </h5>
                    <span class="badge bg-warning">@Model.CarsOutside.Count خودرو</span>
                </div>
                <div class="card-body">
                    @if (Model.CarsOutside.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>شماره پلاک</th>
                                        <th>نوع</th>
                                        <th>راننده/مهمان</th>
                                        <th>سرنشینان</th>
                                        <th>ساختمان</th>
                                        <th>زمان خروج</th>
                                        <th>مدت خروج</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var car in Model.CarsOutside)
                                    {
                                        <tr>
                                            <td>
                                                <strong class="text-primary">@car.Car.PlateNumber</strong>
                                                @if (car.Car.IsMoneyTransport)
                                                {
                                                    <br><span class="badge bg-warning">پولرسان</span>
                                                }
                                            </td>
                                            <td>
                                                @if (car.DriverEmployeeId != 0)
                                                {
                                                    <span class="badge bg-info">خودرو کارمند</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">خودرو مهمان</span>
                                                }
                                            </td>
                                            <td>
                                                @if (car.DriverEmployee != null)
                                                {
                                                    <div>
                                                        <strong>@car.DriverEmployee.FirstName @car.DriverEmployee.LastName</strong>
                                                        <br><small class="text-muted">@car.DriverEmployee.PersonnelCode</small>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-warning">مهمان</span>
                                                }
                                            </td>
                                            <td>
                                                @{
                                                    var passengersNotEntered = car.CarPassengers.Where(cp => !cp.IsEntered).ToList();
                                                }
                                                @if (passengersNotEntered.Any())
                                                {
                                                    <span class="badge bg-secondary">@passengersNotEntered.Count سرنشین</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">بدون سرنشین</span>
                                                }
                                            </td>
                                            <td>
                                                @if (car.Building != null)
                                                {
                                                    <span class="badge bg-secondary">@car.Building.Name</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="text-danger">@(car.ExitTime?.ToString("HH:mm") ?? "-")</span>
                                                <br><small class="text-muted">@(car.ExitTime?.ToString("yyyy/MM/dd") ?? "-")</small>
                                            </td>
                                            <td>
                                                @{
                                                    var duration = car.ExitTime.HasValue ? DateTime.Now - car.ExitTime.Value : TimeSpan.Zero;
                                                    var hours = (int)duration.TotalHours;
                                                    var minutes = duration.Minutes;
                                                }
                                                <span class="text-warning">@hours:@minutes.ToString("D2")</span>
                                            </td>
                                            <td>
                                                <a href="/traffic/carentrynew?SelectedCarId=@car.CarTrafficLogId" 
                                                   class="btn btn-sm btn-success">
                                                    <i class="bi bi-check-circle"></i> انتخاب
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-car-front-fill" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">همه خودروها در پارکینگ هستند</h5>
                            <p>هیچ خودرویی خارج از پارکینگ نیست.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-total {
        border-right: 4px solid #6c757d;
    }
    .stats-total .stats-icon { color: #6c757d; }

    .stats-employee {
        border-right: 4px solid #007bff;
    }
    .stats-employee .stats-icon { color: #007bff; }

    .stats-guest {
        border-right: 4px solid #ffc107;
    }
    .stats-guest .stats-icon { color: #ffc107; }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .passenger-card {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .passenger-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .passenger-card.selected {
        border-color: #28a745;
        background-color: #f8fff9;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // انتخاب همه سرنشینان
        const selectAllCheckbox = document.getElementById('selectAllPassengers');
        const passengerCheckboxes = document.querySelectorAll('.passenger-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                passengerCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                    updatePassengerCardStyle(checkbox);
                });
            });
        }

        // تغییر استایل کارت سرنشین
        passengerCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updatePassengerCardStyle(this);
                updateSelectAllCheckbox();
            });
        });

        function updatePassengerCardStyle(checkbox) {
            const card = checkbox.closest('.card');
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        }

        function updateSelectAllCheckbox() {
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.passenger-checkbox:checked').length;
                const totalCount = passengerCheckboxes.length;

                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }
        }
    });
</script>
