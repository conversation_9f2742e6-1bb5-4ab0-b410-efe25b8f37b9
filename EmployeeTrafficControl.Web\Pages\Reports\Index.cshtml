@page "/reports"
@model EmployeeTrafficControl.Web.Pages.Reports.IndexModel
@{
    ViewData["Title"] = "گزارشات";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-graph-up"></i> گزارشات جامع</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">گزارشات کامل از ترددهای کارمندان و خودروها</p>
</div>

<!-- فرم فیلتر گزارشات -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i> فیلتر گزارشات
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label asp-for="Filter.FromDate" class="form-label"></label>
                            <input asp-for="Filter.FromDate" class="form-control" type="date" />
                            <span asp-validation-for="Filter.FromDate" class="text-danger"></span>
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.ToDate" class="form-label"></label>
                            <input asp-for="Filter.ToDate" class="form-control" type="date" />
                            <span asp-validation-for="Filter.ToDate" class="text-danger"></span>
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.ReportType" class="form-label">نوع گزارش</label>
                            <select asp-for="Filter.ReportType" class="form-select" id="reportType">
                                <option value="employee">ترافیک کارمندان</option>
                                <option value="car">ترافیک خودروها</option>
                                <option value="attendance">حضور و غیاب</option>
                                <option value="detailed">گزارش تفصیلی روزانه</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.BuildingId" class="form-label"></label>
                            <select asp-for="Filter.BuildingId" class="form-select" asp-items="Model.Buildings">
                                <option value="">همه ساختمان‌ها</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="employeeFilter">
                            <label asp-for="Filter.EmployeeId" class="form-label"></label>
                            <select asp-for="Filter.EmployeeId" class="form-select" asp-items="Model.Employees">
                                <option value="">همه کارمندان</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="carFilter" style="display: none;">
                            <label asp-for="Filter.CarId" class="form-label"></label>
                            <select asp-for="Filter.CarId" class="form-select" asp-items="Model.Cars">
                                <option value="">همه خودروها</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.ExportFormat" class="form-label">فرمت خروجی</label>
                            <select asp-for="Filter.ExportFormat" class="form-select">
                                <option value="html">نمایش در صفحه</option>
                                <option value="excel">Excel</option>
                                <option value="pdf">PDF</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> تولید گزارش
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@if (Model.EmployeeTrafficReport != null)
{
    <!-- گزارش ترافیک کارمندان -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people"></i> گزارش ترافیک کارمندان
                    </h5>
                    <div>
                        <span class="badge bg-info">@Model.EmployeeTrafficReport.TotalRecords رکورد</span>
                        <span class="badge bg-success">@Model.EmployeeTrafficReport.TotalEmployees کارمند</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>کارمند</th>
                                    <th>تاریخ</th>
                                    <th>زمان ورود</th>
                                    <th>زمان خروج</th>
                                    <th>مدت کار</th>
                                    <th>وضعیت</th>
                                    <th>توضیحات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.EmployeeTrafficReport.Items)
                                {
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>@item.Employee.FirstName @item.Employee.LastName</strong>
                                                <br><small class="text-muted">@item.Employee.PersonnelCode - @item.Employee.Job?.Title</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-primary">@item.Date.ToString("yyyy/MM/dd")</span>
                                            <br><small class="text-muted">@item.Date.ToString("dddd", new System.Globalization.CultureInfo("fa-IR"))</small>
                                        </td>
                                        <td>
                                            @if (item.EntryTime.HasValue)
                                            {
                                                <span class="text-success">@item.EntryTime.Value.ToString("HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.FinalExitTime.HasValue)
                                            {
                                                <span class="text-danger">@item.FinalExitTime.Value.ToString("HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.WorkDuration.HasValue)
                                            {
                                                <span class="text-info">@((int)item.WorkDuration.Value.TotalHours):@item.WorkDuration.Value.Minutes.ToString("D2")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @item.StatusBadgeClass">@item.StatusDisplayName</span>
                                            @if (item.IsInVehicle)
                                            {
                                                <br><small class="text-warning"><i class="bi bi-car-front"></i> در خودرو</small>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Notes))
                                            {
                                                <small class="text-muted">@item.Notes</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@if (Model.CarTrafficReport != null)
{
    <!-- گزارش ترافیک خودروها -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-car-front"></i> گزارش ترافیک خودروها
                    </h5>
                    <div>
                        <span class="badge bg-info">@Model.CarTrafficReport.TotalRecords رکورد</span>
                        <span class="badge bg-warning">@Model.CarTrafficReport.TotalCars خودرو</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>خودرو</th>
                                    <th>راننده</th>
                                    <th>زمان خروج</th>
                                    <th>زمان ورود</th>
                                    <th>مدت خروج</th>
                                    <th>نوع خروج</th>
                                    <th>سرنشینان</th>
                                    <th>وضعیت</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.CarTrafficReport.Items)
                                {
                                    <tr>
                                        <td>
                                            <div>
                                                <strong class="text-primary">@item.Car.PlateNumber</strong>
                                                <br><small class="text-muted">@item.Car.Model - @item.Car.Type</small>
                                                @if (item.Car.IsMoneyTransport)
                                                {
                                                    <br><span class="badge bg-danger">پولرسان</span>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            @if (item.DriverEmployee != null)
                                            {
                                                <div>
                                                    <strong>@item.DriverEmployee.FirstName @item.DriverEmployee.LastName</strong>
                                                    <br><small class="text-muted">@item.DriverEmployee.PersonnelCode</small>
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="text-warning">مهمان</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.ExitTime.HasValue)
                                            {
                                                <span class="text-danger">@item.ExitTime.Value.ToString("HH:mm")</span>
                                                <br><small class="text-muted">@item.ExitTime.Value.ToString("yyyy/MM/dd")</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.EntryTime.HasValue)
                                            {
                                                <span class="text-success">@item.EntryTime.Value.ToString("HH:mm")</span>
                                                <br><small class="text-muted">@item.EntryTime.Value.ToString("yyyy/MM/dd")</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.Duration.HasValue)
                                            {
                                                <span class="text-info">@((int)item.Duration.Value.TotalHours):@item.Duration.Value.Minutes.ToString("D2")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.ExitType))
                                            {
                                                <span class="badge @item.ExitTypeBadgeClass">@item.ExitType</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.PassengerCount > 0)
                                            {
                                                <span class="badge bg-secondary">@item.PassengerCount سرنشین</span>
                                                @foreach (var passenger in item.Passengers.Take(2))
                                                {
                                                    <br><small class="text-muted">@passenger.FirstName @passenger.LastName</small>
                                                }
                                                @if (item.Passengers.Count > 2)
                                                {
                                                    <br><small class="text-muted">و @(item.Passengers.Count - 2) نفر دیگر...</small>
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-muted">بدون سرنشین</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @item.StatusBadgeClass">@item.CurrentStatus</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@if (Model.AttendanceReport != null)
{
    <!-- گزارش حضور و غیاب -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-check"></i> آمار حضور و غیاب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card stats-total">
                                <div class="stats-icon">
                                    <i class="bi bi-people"></i>
                                </div>
                                <div class="stats-content">
                                    <h3>@Model.AttendanceReport.TotalEmployees</h3>
                                    <p>کل کارمندان</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card stats-present">
                                <div class="stats-icon">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div class="stats-content">
                                    <h3>@Model.AttendanceReport.TotalPresent</h3>
                                    <p>کل حضورها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card stats-absent">
                                <div class="stats-icon">
                                    <i class="bi bi-x-circle"></i>
                                </div>
                                <div class="stats-content">
                                    <h3>@Model.AttendanceReport.TotalAbsent</h3>
                                    <p>کل غیبت‌ها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card stats-percentage">
                                <div class="stats-icon">
                                    <i class="bi bi-percent"></i>
                                </div>
                                <div class="stats-content">
                                    <h3>@Model.AttendanceReport.AttendancePercentage.ToString("F1")%</h3>
                                    <p>درصد حضور</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-check"></i> جزئیات حضور و غیاب
                    </h5>
                    <span class="badge bg-info">@Model.AttendanceReport.TotalRecords رکورد</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>کارمند</th>
                                    <th>تاریخ</th>
                                    <th>ورود</th>
                                    <th>خروج</th>
                                    <th>مدت کار</th>
                                    <th>تاخیر</th>
                                    <th>زودتر رفتن</th>
                                    <th>خروج ساعتی</th>
                                    <th>وضعیت</th>
                                    <th>تایید</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.AttendanceReport.Items)
                                {
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>@item.Employee.FirstName @item.Employee.LastName</strong>
                                                <br><small class="text-muted">@item.Employee.PersonnelCode - @item.Employee.Job?.Title</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-primary">@item.Date.ToString("yyyy/MM/dd")</span>
                                            <br><small class="text-muted">@item.Date.ToString("dddd", new System.Globalization.CultureInfo("fa-IR"))</small>
                                        </td>
                                        <td>
                                            @if (item.CheckInTime.HasValue)
                                            {
                                                <span class="text-success">@item.CheckInTime.Value.ToString("HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.CheckOutTime.HasValue)
                                            {
                                                <span class="text-danger">@item.CheckOutTime.Value.ToString("HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.TotalWorkHours.HasValue)
                                            {
                                                <span class="text-info">@((int)item.TotalWorkHours.Value.TotalHours):@item.TotalWorkHours.Value.Minutes.ToString("D2")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.LateMinutes > 0)
                                            {
                                                <span class="text-warning">@item.LateMinutes دقیقه</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.EarlyLeaveMinutes > 0)
                                            {
                                                <span class="text-warning">@item.EarlyLeaveMinutes دقیقه</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.HourlyExitCount > 0)
                                            {
                                                <span class="badge bg-info">@item.HourlyExitCount بار</span>
                                                @if (item.TotalHourlyExitTime.HasValue)
                                                {
                                                    <br><small class="text-muted">@((int)item.TotalHourlyExitTime.Value.TotalHours):@item.TotalHourlyExitTime.Value.Minutes.ToString("D2")</small>
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @item.AttendanceStatusBadgeClass">@item.AttendanceStatusText</span>
                                        </td>
                                        <td>
                                            <span class="badge @item.ApprovalStatusBadgeClass">@item.ApprovalStatusText</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@if (Model.DetailedDailyReport != null)
{
    <!-- گزارش تفصیلی روزانه -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-day"></i> گزارش تفصیلی روز @Model.DetailedDailyReport.Date.ToString("yyyy/MM/dd")
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="stats-card stats-employee">
                                <div class="stats-icon">
                                    <i class="bi bi-people"></i>
                                </div>
                                <div class="stats-content">
                                    <h3>@Model.DetailedDailyReport.TotalEmployeeMovements</h3>
                                    <p>ترددهای کارمندان</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stats-card stats-car">
                                <div class="stats-icon">
                                    <i class="bi bi-car-front"></i>
                                </div>
                                <div class="stats-content">
                                    <h3>@Model.DetailedDailyReport.TotalCarMovements</h3>
                                    <p>ترددهای خودروها</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ترددهای کارمندان -->
    @if (Model.DetailedDailyReport.EmployeeStatuses.Any())
    {
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-people"></i> ترددهای کارمندان
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>کارمند</th>
                                        <th>زمان ورود</th>
                                        <th>زمان خروج</th>
                                        <th>وضعیت فعلی</th>
                                        <th>توضیحات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var status in Model.DetailedDailyReport.EmployeeStatuses)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@status.Employee.FirstName @status.Employee.LastName</strong>
                                                <br><small class="text-muted">@status.Employee.PersonnelCode</small>
                                            </td>
                                            <td>
                                                @if (status.EntryTime.HasValue)
                                                {
                                                    <span class="text-success">@status.EntryTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (status.FinalExitTime.HasValue)
                                                {
                                                    <span class="text-danger">@status.FinalExitTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @{
                                                    var statusText = status.CurrentStatus switch
                                                    {
                                                        EmployeeCurrentStatus.PresentInBuilding => "حاضر در ساختمان",
                                                        EmployeeCurrentStatus.HourlyExit => "خروج ساعتی",
                                                        EmployeeCurrentStatus.OfficialMission => "ماموریت اداری",
                                                        EmployeeCurrentStatus.OutOfOffice => "خارج از اداره",
                                                        EmployeeCurrentStatus.OnLeave => "مرخصی",
                                                        _ => "نامشخص"
                                                    };
                                                    var badgeClass = status.CurrentStatus switch
                                                    {
                                                        EmployeeCurrentStatus.PresentInBuilding => "bg-success",
                                                        EmployeeCurrentStatus.HourlyExit => "bg-warning",
                                                        EmployeeCurrentStatus.OfficialMission => "bg-info",
                                                        EmployeeCurrentStatus.OutOfOffice => "bg-danger",
                                                        EmployeeCurrentStatus.OnLeave => "bg-secondary",
                                                        _ => "bg-light"
                                                    };
                                                }
                                                <span class="badge @badgeClass">@statusText</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">@(status.Notes ?? "-")</small>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- ترددهای خودروها -->
    @if (Model.DetailedDailyReport.CarTrafficLogs.Any())
    {
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-car-front"></i> ترددهای خودروها
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>خودرو</th>
                                        <th>راننده</th>
                                        <th>زمان خروج</th>
                                        <th>زمان ورود</th>
                                        <th>نوع خروج</th>
                                        <th>سرنشینان</th>
                                        <th>وضعیت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var log in Model.DetailedDailyReport.CarTrafficLogs)
                                    {
                                        <tr>
                                            <td>
                                                <strong class="text-primary">@log.Car.PlateNumber</strong>
                                                @if (log.Car.IsMoneyTransport)
                                                {
                                                    <br><span class="badge bg-danger">پولرسان</span>
                                                }
                                            </td>
                                            <td>
                                                @if (log.DriverEmployee != null)
                                                {
                                                    <div>
                                                        <strong>@log.DriverEmployee.FirstName @log.DriverEmployee.LastName</strong>
                                                        <br><small class="text-muted">@log.DriverEmployee.PersonnelCode</small>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-warning">مهمان</span>
                                                }
                                            </td>
                                            <td>
                                                @if (log.ExitTime.HasValue)
                                                {
                                                    <span class="text-danger">@log.ExitTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (log.EntryTime.HasValue)
                                                {
                                                    <span class="text-success">@log.EntryTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(log.ExitType))
                                                {
                                                    var exitBadgeClass = log.ExitType switch
                                                    {
                                                        "اداری" => "bg-info",
                                                        "ماموریت" => "bg-primary",
                                                        "پولرسانی" => "bg-danger",
                                                        _ => "bg-secondary"
                                                    };
                                                    <span class="badge @exitBadgeClass">@log.ExitType</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (log.CarPassengers.Any())
                                                {
                                                    <span class="badge bg-secondary">@log.CarPassengers.Count سرنشین</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">بدون سرنشین</span>
                                                }
                                            </td>
                                            <td>
                                                @{
                                                    var statusBadgeClass = log.CurrentStatus switch
                                                    {
                                                        "در پارکینگ" => "bg-success",
                                                        "خارج از پارکینگ" => "bg-warning",
                                                        _ => "bg-secondary"
                                                    };
                                                }
                                                <span class="badge @statusBadgeClass">@log.CurrentStatus</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
        margin-bottom: 1rem;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-total {
        border-right: 4px solid #6c757d;
    }
    .stats-total .stats-icon { color: #6c757d; }

    .stats-employee {
        border-right: 4px solid #007bff;
    }
    .stats-employee .stats-icon { color: #007bff; }

    .stats-car {
        border-right: 4px solid #28a745;
    }
    .stats-car .stats-icon { color: #28a745; }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-absent {
        border-right: 4px solid #dc3545;
    }
    .stats-absent .stats-icon { color: #dc3545; }

    .stats-percentage {
        border-right: 4px solid #17a2b8;
    }
    .stats-percentage .stats-icon { color: #17a2b8; }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
    }

    .table td {
        font-size: 0.9rem;
        vertical-align: middle;
    }

    .badge {
        font-size: 0.75rem;
    }

    .page-header {
        margin-bottom: 2rem;
    }

    .page-header h1 {
        color: #2c3e50;
        font-weight: 700;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
    }

    .btn-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        border-radius: 8px;
        font-weight: 600;
    }

    .btn-secondary {
        border-radius: 8px;
        font-weight: 600;
    }

    @@media print {
        .page-header .btn,
        .card:first-child {
            display: none !important;
        }

        .card {
            box-shadow: none;
            border: 1px solid #dee2e6;
        }

        .table {
            font-size: 0.8rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تغییر نوع گزارش
        const reportTypeSelect = document.getElementById('reportType');
        const employeeFilter = document.getElementById('employeeFilter');
        const carFilter = document.getElementById('carFilter');

        if (reportTypeSelect) {
            reportTypeSelect.addEventListener('change', function() {
                const reportType = this.value;

                if (reportType === 'car') {
                    employeeFilter.style.display = 'none';
                    carFilter.style.display = 'block';
                } else {
                    employeeFilter.style.display = 'block';
                    carFilter.style.display = 'none';
                }
            });

            // اجرای اولیه
            reportTypeSelect.dispatchEvent(new Event('change'));
        }

        // دکمه چاپ
        const printButton = document.createElement('button');
        printButton.className = 'btn btn-outline-secondary ms-2';
        printButton.innerHTML = '<i class="bi bi-printer"></i> چاپ';
        printButton.onclick = function() {
            window.print();
        };

        const headerButtons = document.querySelector('.page-header .d-flex div');
        if (headerButtons) {
            headerButtons.appendChild(printButton);
        }

        // دکمه export Excel (برای آینده)
        const excelButton = document.createElement('button');
        excelButton.className = 'btn btn-outline-success ms-2';
        excelButton.innerHTML = '<i class="bi bi-file-earmark-excel"></i> Excel';
        excelButton.onclick = function() {
            alert('قابلیت export به Excel در نسخه بعدی اضافه خواهد شد.');
        };

        if (headerButtons) {
            headerButtons.appendChild(excelButton);
        }
    });
</script>
