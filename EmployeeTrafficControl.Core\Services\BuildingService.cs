﻿using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Services
{
    public class BuildingService
    {
        private readonly ApplicationDbContext _context;

        // Constructor for dependency injection
        public BuildingService(ApplicationDbContext context)
        {
            _context = context;
        }

        // --- CRUD Operations for Buildings ---

        /// <summary>
        /// Retrieves all buildings from the database.
        /// </summary>
        /// <returns>A list of Building objects.</returns>
        public async Task<List<Building>> GetAllBuildingsAsync()
        {
            return await _context.Buildings
                                 .Include(b => b.Employees)
                                 .Include(b => b.Jobs)
                                 .ToListAsync();
        }

        /// <summary>
        /// Retrieves a single building by its ID.
        /// </summary>
        /// <param name="buildingId">The ID of the building to retrieve.</param>
        /// <returns>The Building object if found, otherwise null.</returns>
        public async Task<Building?> GetBuildingByIdAsync(int buildingId)
        {
            return await _context.Buildings
                                 .Include(b => b.Employees)
                                 .Include(b => b.Jobs)
                                     .ThenInclude(j => j.Employees)
                                 .FirstOrDefaultAsync(b => b.BuildingId == buildingId);
        }

        /// <summary>
        /// Adds a new building to the database.
        /// </summary>
        /// <param name="building">The Building object to add.</param>
        /// <returns>The added Building object.</returns>
        public async Task<Building> AddBuildingAsync(Building building)
        {
            _context.Buildings.Add(building);
            await _context.SaveChangesAsync();
            return building;
        }

        /// <summary>
        /// Updates an existing building in the database.
        /// </summary>
        /// <param name="building">The Building object with updated information.</param>
        /// <returns>True if the update was successful, otherwise false.</returns>
        public async Task<bool> UpdateBuildingAsync(Building building)
        {
            // Check if the building exists
            var existingBuilding = await _context.Buildings.FindAsync(building.BuildingId);
            if (existingBuilding == null)
            {
                return false; // Building not found
            }

            // Update properties
            existingBuilding.Name = building.Name;
            existingBuilding.Description = building.Description;

            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (DbUpdateConcurrencyException)
            {
                // Handle concurrency conflicts if needed
                // For simplicity, we just return false here
                return false;
            }
        }

        /// <summary>
        /// Deletes a building from the database by its ID.
        /// </summary>
        /// <param name="buildingId">The ID of the building to delete.</param>
        /// <returns>True if the deletion was successful, otherwise false.</returns>
        public async Task<bool> DeleteBuildingAsync(int buildingId)
        {
            var buildingToDelete = await _context.Buildings.FindAsync(buildingId);
            if (buildingToDelete == null)
            {
                return false; // Building not found
            }

            _context.Buildings.Remove(buildingToDelete);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Checks if a building with the given name already exists (case-insensitive).
        /// </summary>
        /// <param name="buildingName">The name of the building to check.</param>
        /// <param name="excludeBuildingId">Optional: An ID to exclude during the check (useful for updates).</param>
        /// <returns>True if a building with the name exists, otherwise false.</returns>
        public async Task<bool> BuildingExistsAsync(string buildingName, int? excludeBuildingId = null)
        {
            if (excludeBuildingId.HasValue)
            {
                return await _context.Buildings.AnyAsync(b =>
                    b.Name.ToLower() == buildingName.ToLower() && b.BuildingId != excludeBuildingId.Value);
            }
            return await _context.Buildings.AnyAsync(b => b.Name.ToLower() == buildingName.ToLower());
        }
    }
}