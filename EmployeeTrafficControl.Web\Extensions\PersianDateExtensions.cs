using EmployeeTrafficControl.Web.Helpers;

namespace EmployeeTrafficControl.Web.Extensions
{
    /// <summary>
    /// Extension Methods برای تبدیل تاریخ شمسی در Razor Pages
    /// </summary>
    public static class PersianDateExtensions
    {
        /// <summary>
        /// تبدیل DateTime به تاریخ شمسی
        /// </summary>
        public static string ToPersianDate(this DateTime? date)
        {
            return PersianDateHelper.ToPersianDate(date);
        }

        /// <summary>
        /// تبدیل DateTime به تاریخ شمسی
        /// </summary>
        public static string ToPersianDate(this DateTime date)
        {
            return PersianDateHelper.ToPersianDate(date);
        }

        /// <summary>
        /// تبدیل DateTime به تاریخ شمسی کامل
        /// </summary>
        public static string ToFullPersianDate(this DateTime? date)
        {
            return PersianDateHelper.ToFullPersianDate(date);
        }

        /// <summary>
        /// تبدیل DateTime به تاریخ شمسی کامل
        /// </summary>
        public static string ToFullPersianDate(this DateTime date)
        {
            return PersianDateHelper.ToFullPersianDate(date);
        }

        /// <summary>
        /// دریافت نام روز هفته به فارسی
        /// </summary>
        public static string GetPersianDayOfWeek(this DateTime date)
        {
            return PersianDateHelper.GetPersianDayOfWeek(date);
        }

        /// <summary>
        /// تبدیل اعداد انگلیسی به فارسی
        /// </summary>
        public static string ToPersianNumbers(this string input)
        {
            return PersianDateHelper.ToPersianNumbers(input);
        }

        /// <summary>
        /// تبدیل اعداد فارسی به انگلیسی
        /// </summary>
        public static string ToEnglishNumbers(this string input)
        {
            return PersianDateHelper.ToEnglishNumbers(input);
        }

        /// <summary>
        /// محاسبه سن
        /// </summary>
        public static int CalculateAge(this DateTime birthDate)
        {
            return PersianDateHelper.CalculateAge(birthDate);
        }

        /// <summary>
        /// اعتبارسنجی تاریخ شمسی
        /// </summary>
        public static bool IsValidPersianDate(this string persianDate)
        {
            return PersianDateHelper.IsValidPersianDate(persianDate);
        }
    }
}
