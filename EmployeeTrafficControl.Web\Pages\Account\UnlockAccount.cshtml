@page "/Account/UnlockAccount"
@model EmployeeTrafficControl.Web.Pages.Account.UnlockAccountModel
@{
    ViewData["Title"] = "بازگشایی حساب کاربری";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header text-center">
            <i class="bi bi-unlock text-success" style="font-size: 4rem;"></i>
            <h2 class="mt-3">بازگشایی حساب کاربری</h2>
            <p class="text-muted">برای بازگشایی حساب کاربری قفل شده از این صفحه استفاده کنید</p>
        </div>

        @if (!string.IsNullOrEmpty(Model.Message))
        {
            <div class="alert @(Model.IsSuccess ? "alert-success" : "alert-danger")">
                <i class="bi @(Model.IsSuccess ? "bi-check-circle" : "bi-exclamation-triangle")"></i>
                @Model.Message
            </div>
        }

        <form method="post" class="mt-4">
            <div class="form-group mb-3">
                <label asp-for="Username" class="form-label">نام کاربری</label>
                <input asp-for="Username" class="form-control" placeholder="نام کاربری را وارد کنید" />
                <span asp-validation-for="Username" class="text-danger"></span>
            </div>

            <button type="submit" class="btn btn-success w-100">
                <i class="bi bi-unlock"></i>
                بازگشایی حساب
            </button>
        </form>

        <div class="text-center mt-4">
            <a href="/login" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i>
                بازگشت به صفحه ورود
            </a>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
