@page "/traffic/carexitnew"
@model EmployeeTrafficControl.Web.Pages.Traffic.CarExitNewModel
@{
    ViewData["Title"] = "خروج خودرو";
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
}

@if (ViewData["ErrorMessage"] != null)
{
    <div class="alert alert-danger">@ViewData["ErrorMessage"]</div>
}

<div class="container-fluid">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="dashboard-title">
                    <i class="bi bi-car-front-fill"></i> خروج خودرو
                </h1>
                <p class="dashboard-subtitle">ثبت خروج خودرو از پارکینگ ساختمان</p>
            </div>
            <div class="col-md-4">
                <div class="dashboard-date">
                    <i class="bi bi-calendar3"></i>
                    @DateTime.Now.ToString("yyyy/MM/dd", new System.Globalization.CultureInfo("fa-IR"))<br>
                    <small>@DateTime.Now.ToString("HH:mm")</small>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.Message))
    {
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            @Model.Message
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- آمار کلی -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0" style="color:black">
                        <i class="bi bi-info-circle"></i> اطلاعات سیستم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4>@Model.TotalCarsInside</h4>
                                <p>کل خودروهای داخل پارکینگ</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4>@Model.EmployeeCarsInside</h4>
                                <p>خودروهای کارمندی</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4>@Model.GuestCarsInside</h4>
                                <p>خودروهای مهمان</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4>@Model.TodayExits</h4>
                                <p>خروجی امروز</p>
                            </div>
                        </div>
                    </div>
                    @if (Model.AvailableCars.Count == 0)
                    {
                        <div class="alert alert-warning mt-3">
                            <h6>هیچ خودرویی در پارکینگ یافت نشد!</h6>
                            <form method="post" asp-page-handler="DebugCars" class="d-inline">
                                <button type="submit" class="btn btn-info btn-sm">
                                    <i class="bi bi-bug"></i> بررسی وضعیت خودروها
                                </button>
                            </form>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- انتخاب خودرو -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0" style="color:black">
                        <i class="bi bi-1-circle"></i> انتخاب خودرو
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" asp-page-handler="SelectCar">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">خودرو</label>
                                <select name="CarId" class="form-select" required>
                                    <option value="">انتخاب خودرو...</option>
                                    @foreach (var car in Model.AvailableCars)
                                    {
                                        <option value="@car.Value" selected="@(car.Value == Model.SelectedCarId?.ToString())">
                                            @car.Text
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-arrow-right"></i> ادامه
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @if (Model.SelectedCar != null)
    {
        <script>
            const maxPassengers = @(Model.SelectedCar.Car.PassengerCapacity - 1);
        </script>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0" style="color:black">
                            <i class="bi bi-2-circle"></i> جزئیات خروج - @Model.SelectedCar.Car.Model
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" asp-page-handler="RegisterExit" id="exitForm">
                            <input type="hidden" name="CarTrafficLogId" value="@Model.SelectedCar?.CarTrafficLogId" />
                            <input type="hidden" name="CarId" value="@Model.SelectedCar?.CarId" />

                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">راننده <span class="text-danger">*</span></label>
                                    <select name="DriverId" class="form-select" required>
                                        <option value="">انتخاب راننده...</option>
                                        @foreach (var driver in Model.AvailableDrivers)
                                        {
                                            <option value="@driver.Value">@driver.Text</option>
                                        }
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">نوع خروج <span class="text-danger">*</span></label>
                                    <select name="ExitType" class="form-select" required id="exitTypeSelect">
                                        <option value="">انتخاب نوع خروج...</option>
                                        <option value="اداری">اداری</option>
                                        <option value="ماموریت">ماموریت</option>
                                        @if (Model.SelectedCar.Car.IsMoneyTransport)
                                        {
                                            <option value="پولرسانی">پولرسانی</option>
                                        }
                                    </select>
                                </div>
                            </div>

                            @if (Model.SelectedCar.Car.IsMoneyTransport && Model.IsFirstExitToday)
                            {
                                <div class="row g-3 mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label">کیلومتر فعلی خودرو <span class="text-danger">*</span></label>
                                        <input type="number" name="CurrentKilometer" class="form-control" placeholder="کیلومتر فعلی" min="0" max="9999999" required />
                                        <small class="text-info">این اولین خروج خودرو پولرسان در روز جاری است</small>
                                    </div>
                                </div>
                            }

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label class="form-label">سرنشینان (حداکثر @(Model.SelectedCar.Car.PassengerCapacity - 1) نفر)</label>
                                    <div id="selectedCount" style="margin-bottom: 10px; font-weight: 600;">
  تعداد سرنشینان انتخاب شده: 0
</div>

                                    <div id="passengersSection"></div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label class="form-label">توضیحات</label>
                                    <textarea name="Notes" class="form-control" rows="3" placeholder="توضیحات اضافی (اختیاری)"></textarea>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-check-circle"></i> ثبت خروج
                                        </button>
                                        <a href="/dashboard" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left"></i> انصراف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- CDN DataTables -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css" />
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const exitTypeSelect = document.getElementById('exitTypeSelect');
        const driverSelect = document.querySelector('select[name="DriverId"]');
        if (exitTypeSelect && driverSelect) {
            exitTypeSelect.addEventListener('change', () => loadPassengers(exitTypeSelect.value));
            driverSelect.addEventListener('change', () => loadPassengers(exitTypeSelect.value));
        }
    });

    async function loadPassengers(exitType)
 {
        const passengersSection = document.getElementById('passengersSection');
        const driverSelect = document.querySelector('select[name="DriverId"]');
        const selectedDriverId = driverSelect?.value ?? '';
        const selectedDriverText = driverSelect?.selectedOptions[0]?.text ?? '';

        if (!exitType) {
            passengersSection.innerHTML = '<p class="text-muted">ابتدا نوع خروج را انتخاب کنید</p>';
            return;
        }

        let url = `/api/traffic/get-available-passengers?exitType=${exitType}&buildingId=@Model.SelectedCar?.BuildingId`;
        if (selectedDriverId) url += `&driverId=${selectedDriverId}`;

        try {
            const response = await fetch(url);
            const passengers = await response.json();
            if (!passengers.length) {
                passengersSection.innerHTML = '<p class="text-muted">هیچ سرنشینی یافت نشد</p>';
                return;
            }

            const jobTitles = [...new Set(passengers.map(p => p.jobTitle))];
            let html = `
                <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle"></i> حداکثر ظرفیت مجاز سرنشینان: ${maxPassengers} نفر
                    ${selectedDriverId ? `<br><strong>توجه:</strong> راننده (${selectedDriverText}) از لیست سرنشینان حذف شده است.` : ''}
                </div>
                <div class="mb-3">
                    <label class="form-label">فیلتر عنوان شغلی:</label>
                    <select id="jobTitleFilter" class="form-select form-select-sm" style="max-width:300px;">
                        <option value="">همه</option>
                        ${jobTitles.map(j => `<option value="${j}">${j}</option>`).join('')}
                    </select>
                </div>
                <table id="passengersTable" class="table table-bordered table-sm">
                    <thead>
                       <tr>
                           <th class="text-center" style="text-align: right;"><input type="checkbox" id="selectAllCheckbox" /></th>

                            <th class="text-center">نام</th>
                            <th class="text-center">نام خانوادگی</th>
                            <th class="text-center">سمت</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${passengers.map(p => {
                            const isDriver = p.employeeId == selectedDriverId;
                            const checkbox = isDriver
                                ? `<span class="text-muted">راننده</span>`
                                : `<input type="checkbox" class="passenger-checkbox" name="PassengerIds" value="${p.employeeId}" />`;
                            const rowClass = isDriver ? 'table-warning' : '';
                            return `<tr class="${rowClass}"><td>${checkbox}</td><td>${p.firstName}</td><td>${p.lastName}</td><td>${p.jobTitle}</td></tr>`;
                        }).join('')}
                    </tbody>
                </table>`;

            passengersSection.innerHTML = html;
            const table = $('#passengersTable').DataTable({
                paging: true,
                searching: true,
                ordering: false,
                language: {
                    search: "جستجو:",
                    lengthMenu: "نمایش _MENU_ نفر",
                    info: "نمایش _START_ تا _END_ از _TOTAL_",
                    paginate: { next: "بعدی", previous: "قبلی" }
                }
            });

            $('#jobTitleFilter').on('change', function () {
                table.column(3).search(this.value).draw();
            });

            $('#selectAllCheckbox').on('change', function () {
                const isChecked = this.checked;
                let count = 0;
                $('.passenger-checkbox').each(function () {
                    if (isChecked && count >= maxPassengers) return;
                    $(this).prop('checked', isChecked && count < maxPassengers);
                    if (!$(this).is(':disabled')) count++;
                });
            });

            $(document).on('change', '.passenger-checkbox', function () {
                const checkedCount = $('.passenger-checkbox:checked').length;
                if (checkedCount >= maxPassengers) {
                    $('.passenger-checkbox:not(:checked)').prop('disabled', true);
                } else {
                    $('.passenger-checkbox').prop('disabled', false);
                }
            });

        } catch (err) {
            console.error('خطا:', err);
            passengersSection.innerHTML = '<p class="text-danger">خطا در بارگذاری لیست</p>';
        }
    }

    function updateSelectedCount() {
    const maxPassengers = window.maxPassengers || 0;
    const checkboxes = document.querySelectorAll('.passenger-checkbox');
    const selectedCountElem = document.getElementById('selectedCount');
    const checkedBoxes = Array.from(checkboxes).filter(chk => chk.checked);

    // به‌روزرسانی متن شمارنده
    if (selectedCountElem) {
        selectedCountElem.textContent = `تعداد سرنشینان انتخاب شده: ${checkedBoxes.length}`;
    }

    // غیرفعال کردن چک‌باکس‌های اضافی اگر تعداد انتخابی بیش از حد شد
    checkboxes.forEach(chk => {
        if (!chk.checked) {
            chk.disabled = checkedBoxes.length >= maxPassengers;
        } else {
            chk.disabled = false;
        }
    });
}

// فراخوانی این تابع وقتی صفحه بارگذاری شد و وقتی چک‌باکس‌ها تغییر کردند
document.addEventListener('change', function (e) {
    if (e.target.classList.contains('passenger-checkbox')) {
        updateSelectedCount();
    }
});

// بعد از بارگذاری لیست سرنشینان (داخل loadPassengers بعد از ساخت جدول):
updateSelectedCount();

</script>
