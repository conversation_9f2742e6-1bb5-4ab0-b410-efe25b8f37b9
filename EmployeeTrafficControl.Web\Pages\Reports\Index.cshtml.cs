 using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    [AuthorizePermission("VIEW_REPORTS")]
    public class IndexModel : PageModel
    {
        private readonly ReportService _reportService;
        private readonly UserManager<ApplicationUser> _userManager;

        public IndexModel(ReportService reportService, UserManager<ApplicationUser> userManager)
        {
            _reportService = reportService;
            _userManager = userManager;
        }

        [BindProperty]
        public ReportFilterModel Filter { get; set; } = new();

        public List<SelectListItem> Buildings { get; set; } = new();
        public List<SelectListItem> Employees { get; set; } = new();
        public List<SelectListItem> Cars { get; set; } = new();

        // گزارشات
        public EmployeeTrafficReportData? EmployeeTrafficReport { get; set; }
        public CarTrafficReportData? CarTrafficReport { get; set; }
        public AttendanceReportData? AttendanceReport { get; set; }
        public DetailedDailyReportData? DetailedDailyReport { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanViewReports(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            await LoadFilterDataAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanViewReports(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadFilterDataAsync();
                await LoadReportDataAsync();
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در تولید گزارش: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadFilterDataAsync()
        {
            // بارگذاری ساختمان‌ها
            var buildings = await _reportService.GetBuildingsAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == Filter.BuildingId
            }).ToList();

            // بارگذاری کارمندان
            var employees = await _reportService.GetEmployeesAsync(Filter.BuildingId);
            Employees = employees.Select(e => new SelectListItem
            {
                Value = e.EmployeeId.ToString(),
                Text = $"{e.FirstName} {e.LastName} ({e.PersonnelCode})",
                Selected = e.EmployeeId == Filter.EmployeeId
            }).ToList();

            // بارگذاری خودروها
            var cars = await _reportService.GetCarsAsync(Filter.BuildingId);
            Cars = cars.Select(c => new SelectListItem
            {
                Value = c.CarId.ToString(),
                Text = $"{c.PlateNumber} - {c.Model}",
                Selected = c.CarId == Filter.CarId
            }).ToList();
        }

        private async Task LoadReportDataAsync()
        {
            switch (Filter.ReportType.ToLower())
            {
                case "employee":
                    EmployeeTrafficReport = await _reportService.GetEmployeeTrafficReportAsync(
                        Filter.FromDate, Filter.ToDate, Filter.BuildingId, Filter.EmployeeId);
                    break;

                case "car":
                    CarTrafficReport = await _reportService.GetCarTrafficReportAsync(
                        Filter.FromDate, Filter.ToDate, Filter.BuildingId, Filter.CarId);
                    break;

                case "attendance":
                    AttendanceReport = await _reportService.GetAttendanceReportAsync(
                        Filter.FromDate, Filter.ToDate, Filter.BuildingId, Filter.EmployeeId);
                    break;

                case "detailed":
                    if (Filter.FromDate.Date == Filter.ToDate.Date)
                    {
                        DetailedDailyReport = await _reportService.GetDetailedDailyReportAsync(
                            Filter.FromDate, Filter.BuildingId);
                    }
                    else
                    {
                        TempData["ErrorMessage"] = "گزارش تفصیلی فقط برای یک روز قابل تولید است.";
                    }
                    break;

                default:
                    EmployeeTrafficReport = await _reportService.GetEmployeeTrafficReportAsync(
                        Filter.FromDate, Filter.ToDate, Filter.BuildingId, Filter.EmployeeId);
                    break;
            }
        }

        private bool CanViewReports(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") || 
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }
    }
}