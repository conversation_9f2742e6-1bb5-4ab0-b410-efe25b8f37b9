﻿using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;
using Microsoft.Extensions.Logging;

namespace EmployeeTrafficControl.Core.Services
{
    public class CarService
    {
        private readonly ApplicationDbContext _context;

        public CarService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Retrieves all cars from the database.
        /// </summary>
        /// <returns>A list of Car objects.</returns>
        public async Task<List<Car>> GetAllCarsAsync()
        {
            return await _context.Cars
                                .Include(c => c.Building)
                                .OrderBy(c => c.PlateNumber)
                                .ToListAsync();
        }

        /// <summary>
        /// Retrieves a single car by its ID.
        /// </summary>
        /// <param name="carId">The ID of the car to retrieve.</param>
        /// <returns>The Car object if found, otherwise null.</returns>
        public async Task<Car?> GetCarByIdAsync(int carId)
        {
            return await _context.Cars
                                .Include(c => c.Building)
                                .FirstOrDefaultAsync(c => c.CarId == carId);
        }

        /// <summary>
        /// Adds a new car to the database.
        /// </summary>
        /// <param name="car">The Car object to add.</param>
        /// <returns>The added Car object.</returns>
        public async Task<Car> AddCarAsync(Car car)
        {
            _context.Cars.Add(car);
            await _context.SaveChangesAsync();
            return car;
        }

        /// <summary>
        /// Updates an existing car in the database.
        /// </summary>
        /// <param name="car">The Car object with updated information.</param>
        /// <returns>True if the update was successful, otherwise false.</returns>
        public async Task<bool> UpdateCarAsync(Car car)
        {
            var existingCar = await _context.Cars.FindAsync(car.CarId);
            if (existingCar == null)
            {
                return false;
            }

            existingCar.PlateNumber = car.PlateNumber;
            existingCar.Model = car.Model;
            existingCar.Color = car.Color;
            existingCar.Type = car.Type;
            existingCar.PassengerCapacity = car.PassengerCapacity;

            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (DbUpdateConcurrencyException)
            {
                // Handle concurrency conflicts if needed
                return false;
            }
        }

        /// <summary>
        /// Deletes a car from the database by its ID.
        /// </summary>
        /// <param name="carId">The ID of the car to delete.</param>
        /// <returns>True if the deletion was successful, otherwise false.</returns>
        public async Task<bool> DeleteCarAsync(int carId)
        {
            var carToDelete = await _context.Cars.FindAsync(carId);
            if (carToDelete == null)
            {
                return false;
            }

            // Check for related data (e.g., CarTrafficLogs)
            var hasTrafficLogs = await _context.CarTrafficLogs.AnyAsync(ctl => ctl.CarId == carId);
            if (hasTrafficLogs)
            {
                Console.WriteLine("Cannot delete car: Related traffic logs exist.");
                return false;
            }

            _context.Cars.Remove(carToDelete);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Checks if a car with the given plate number already exists (case-insensitive).
        /// </summary>
        /// <param name="plateNumber">The plate number to check.</param>
        /// <param name="excludeCarId">Optional: An ID to exclude during the check (useful for updates).</param>
        /// <returns>True if a car with the plate number exists, otherwise false.</returns>
        public async Task<bool> CarExistsAsync(string plateNumber, int? excludeCarId = null)
        {
            if (excludeCarId.HasValue)
            {
                return await _context.Cars.AnyAsync(c =>
                    c.PlateNumber.ToLower() == plateNumber.ToLower() && c.CarId != excludeCarId.Value);
            }
            return await _context.Cars.AnyAsync(c => c.PlateNumber.ToLower() == plateNumber.ToLower());
        }

        /// <summary>
        /// دریافت لیست خودروهای خارج از پارکینگ
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <returns>لیست خودروهای خارج از پارکینگ</returns>
        public async Task<List<CarTrafficLog>> GetCarsOutsideParkingAsync(int? buildingId = null)
        {
            var query = _context.CarTrafficLogs
                .Include(c => c.Car)
                .Include(c => c.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(c => c.Building)
                .Include(c => c.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                        .ThenInclude(e => e.Job)
                .Where(c => !c.IsInParking);

            if (buildingId.HasValue)
            {
                query = query.Where(c => c.BuildingId == buildingId.Value);
            }

            return await query
                .OrderBy(c => c.ExitTime)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت اطلاعات کامل یک خودرو خارج از پارکینگ
        /// </summary>
        /// <param name="carTrafficLogId">شناسه لاگ ترافیک خودرو</param>
        /// <returns>اطلاعات کامل خودرو</returns>
        public async Task<CarTrafficLog?> GetCarTrafficLogWithDetailsAsync(int carTrafficLogId)
        {
            return await _context.CarTrafficLogs
                .Include(c => c.Car)
                .Include(c => c.DriverEmployee)
                    .ThenInclude(e => e.Job)
                .Include(c => c.Building)
                .Include(c => c.CarPassengers)
                    .ThenInclude(cp => cp.Employee)
                        .ThenInclude(e => e.Job)
                .FirstOrDefaultAsync(c => c.CarTrafficLogId == carTrafficLogId);
        }

        /// <summary>
        /// ثبت ورود خودرو و سرنشینان انتخاب شده
        /// </summary>
        /// <param name="carTrafficLogId">شناسه لاگ ترافیک خودرو</param>
        /// <param name="selectedPassengerIds">لیست شناسه سرنشینان انتخاب شده</param>
        /// <param name="userId">شناسه کاربر ثبت کننده</param>
        /// <returns>نتیجه عملیات</returns>
        public async Task<bool> RegisterCarEntryAsync(int carTrafficLogId, List<int> selectedPassengerIds, int userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var carTrafficLog = await GetCarTrafficLogWithDetailsAsync(carTrafficLogId);
                if (carTrafficLog == null || carTrafficLog.IsInParking)
                {
                    return false;
                }

                // ثبت ورود خودرو
                carTrafficLog.EntryTime = DateTime.Now;
                carTrafficLog.IsInParking = true;

                // به‌روزرسانی وضعیت خودرو در جدول Cars
                if (carTrafficLog.Car != null)
                {
                    carTrafficLog.Car.IsInParking = true;
                }

                // ثبت ورود راننده
                if (carTrafficLog.DriverEmployeeId > 0)
                {
                    await RegisterEmployeeEntryAsync(carTrafficLog.DriverEmployeeId, userId,
                        $"ورود با خودرو {carTrafficLog.Car?.PlateNumber}", carTrafficLog.Car?.CarId);
                }

                // ثبت ورود سرنشینان انتخاب شده
                foreach (var passenger in carTrafficLog.CarPassengers)
                {
                    if (selectedPassengerIds.Contains(passenger.EmployeeId))
                    {
                        passenger.IsEntered = true;
                        await RegisterEmployeeEntryAsync(passenger.EmployeeId, userId,
                            $"ورود با خودرو {carTrafficLog.Car?.PlateNumber}", carTrafficLog.Car?.CarId);
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                return false;
            }
        }

        /// <summary>
        /// ثبت ورود کارمند (کمکی)
        /// </summary>
        private async Task RegisterEmployeeEntryAsync(int employeeId, int userId, string notes, int? vehicleId = null)
        {
            // ثبت در جدول وضعیت کارمندان
            var employeeStatus = await _context.EmployeeStatuses
                .FirstOrDefaultAsync(es => es.EmployeeId == employeeId && es.Date.Date == DateTime.Today);

            if (employeeStatus != null)
            {
                employeeStatus.CurrentStatus = EmployeeCurrentStatus.PresentInBuilding;
                employeeStatus.EntryTime = DateTime.Now;
                employeeStatus.CurrentVehicleId = vehicleId;
                employeeStatus.IsInVehicle = false;
                employeeStatus.Notes = notes;
                employeeStatus.UpdatedByUserId = userId;
                employeeStatus.LastUpdated = DateTime.Now;
            }
            else
            {
                var employee = await _context.Employees.FindAsync(employeeId);
                if (employee != null)
                {
                    employeeStatus = new EmployeeStatus
                    {
                        EmployeeId = employeeId,
                        Date = DateTime.Today,
                        CurrentStatus = EmployeeCurrentStatus.PresentInBuilding,
                        EntryTime = DateTime.Now,
                        CurrentVehicleId = vehicleId,
                        IsInVehicle = false,
                        Notes = notes,
                        UpdatedByUserId = userId,
                        LastUpdated = DateTime.Now
                    };
                    _context.EmployeeStatuses.Add(employeeStatus);
                }
            }

            // ثبت در جدول حضور روزانه
            var dailyAttendance = await _context.DailyAttendances
                .FirstOrDefaultAsync(da => da.EmployeeId == employeeId && da.Date.Date == DateTime.Today);

            if (dailyAttendance != null)
            {
                if (!dailyAttendance.CheckInTime.HasValue)
                {
                    dailyAttendance.CheckInTime = DateTime.Now;
                    dailyAttendance.IsPresent = true;
                }
            }
            else
            {
                dailyAttendance = new DailyAttendance
                {
                    EmployeeId = employeeId,
                    Date = DateTime.Today,
                    CheckInTime = DateTime.Now,
                    IsPresent = true,
                    RegisteredByUserId = userId,
                    RegisteredTime = DateTime.Now
                };
                _context.DailyAttendances.Add(dailyAttendance);
            }
        }
    }
}