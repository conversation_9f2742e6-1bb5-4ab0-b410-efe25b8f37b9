@page
@model EmployeeTrafficControl.Web.Pages.Roles.IndexModel
@{
    ViewData["Title"] = "مدیریت نقش‌ها";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-person-badge"></i>
                        مدیریت نقش‌ها
                    </h4>
                    <a asp-page="./Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        نقش جدید
                    </a>
                </div>
                <div class="card-body">
                    @if (Model.Roles.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>نام نقش</th>
                                        <th>کد نقش</th>
                                        <th>توضیحات</th>
                                        <th>نوع</th>
                                        <th>تعداد کاربران</th>
                                        <th>وضعیت</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var role in Model.Roles)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@role.Name</strong>
                                            </td>
                                            <td>
                                                <code class="text-muted">@role.Code</code>
                                            </td>
                                            <td>
                                                @(role.Description ?? "-")
                                            </td>
                                            <td>
                                                @if (role.IsSystemRole)
                                                {
                                                    <span class="badge bg-info">سیستمی</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">سفارشی</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@Model.GetUserCountForRole(role.RoleId)</span>
                                            </td>
                                            <td>
                                                @if (role.IsActive)
                                                {
                                                    <span class="badge bg-success">فعال</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غیرفعال</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="./Details" asp-route-id="@role.RoleId" 
                                                       class="btn btn-sm btn-outline-info" title="جزئیات">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a asp-page="./Permissions" asp-route-id="@role.RoleId" 
                                                       class="btn btn-sm btn-outline-primary" title="مدیریت مجوزها">
                                                        <i class="bi bi-shield-lock"></i>
                                                    </a>
                                                    @if (!role.IsSystemRole)
                                                    {
                                                        <a asp-page="./Edit" asp-route-id="@role.RoleId" 
                                                           class="btn btn-sm btn-outline-warning" title="ویرایش">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        @if (Model.GetUserCountForRole(role.RoleId) == 0)
                                                        {
                                                            <a asp-page="./Delete" asp-route-id="@role.RoleId" 
                                                               class="btn btn-sm btn-outline-danger" title="حذف">
                                                                <i class="bi bi-trash"></i>
                                                            </a>
                                                        }
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-person-badge display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">هیچ نقشی یافت نشد</h4>
                            <p class="text-muted">برای شروع، نقش جدیدی ایجاد کنید.</p>
                            <a asp-page="./Create" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i>
                                ایجاد اولین نقش
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-refresh functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add any JavaScript functionality here if needed
        });
    </script>
}
