using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    //[AuthorizePermission("REGISTER_CAR_EXIT")]
    public class CarExitModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly TrafficLogService _trafficLogService; // New service

        public CarExitModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            TrafficLogService trafficLogService) // Inject new service
        {
            _context = context;
            _userManager = userManager;
            _trafficLogService = trafficLogService;
        }

        public List<CarTrafficLog> CarsInside { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? CarType { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? DurationFilter { get; set; }

        public int TotalCarsInside { get; set; }
        public int EmployeeCarsInside { get; set; }
        public int GuestCarsInside { get; set; }
        public int TodayExits { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری خودروهای داخل پارکینگ
            var carsQuery = _context.CarTrafficLogs
                                   .Include(c => c.DriverEmployee)
                                   .Include(c => c.Building)
                                   .Where(c => c.IsInParking); // Use IsInParking directly

            if (accessibleBuildingIds != null)
            {
                carsQuery = carsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            // اعمال فیلترها
            if (BuildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            if (!string.IsNullOrEmpty(CarType))
            {
                if (CarType == "employee")
                {
                    carsQuery = carsQuery.Where(c => c.DriverEmployeeId > 0);
                }
                else if (CarType == "guest")
                {
                    carsQuery = carsQuery.Where(c => c.DriverEmployeeId == 0);
                }
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                carsQuery = carsQuery.Where(c =>
                    c.PlateNumber.ToLower().Contains(searchLower) ||
                    (c.DriverEmployee != null &&
                     (c.DriverEmployee.FirstName.ToLower().Contains(searchLower) ||
                      c.DriverEmployee.LastName.ToLower().Contains(searchLower) ||
                      c.DriverEmployee.PersonnelCode.ToLower().Contains(searchLower))) ||
                    (c.GuestName != null && c.GuestName.ToLower().Contains(searchLower)));
            }

            var allCarsInside = await carsQuery.OrderBy(c => c.EntryTime).ToListAsync();

            // اعمال فیلتر مدت حضور
            if (!string.IsNullOrEmpty(DurationFilter))
            {
                var now = DateTime.Now;
                allCarsInside = DurationFilter switch
                {
                    "short" => allCarsInside.Where(c => c.EntryTime.HasValue && (now - c.EntryTime.Value).TotalHours < 2).ToList(),
                    "medium" => allCarsInside.Where(c => c.EntryTime.HasValue && (now - c.EntryTime.Value).TotalHours >= 2 && (now - c.EntryTime.Value).TotalHours <= 8).ToList(),
                    "long" => allCarsInside.Where(c => c.EntryTime.HasValue && (now - c.EntryTime.Value).TotalHours > 8).ToList(),
                    _ => allCarsInside
                };
            }

            CarsInside = allCarsInside;

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;

            // آمار خودروهای داخل پارکینگ
            var carsInsideQuery = _context.CarTrafficLogs
                                         .Where(c => c.IsInParking); // Use IsInParking directly

            if (accessibleBuildingIds != null)
            {
                carsInsideQuery = carsInsideQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carsInsideQuery = carsInsideQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            var carsInsideList = await carsInsideQuery.ToListAsync();

            TotalCarsInside = carsInsideList.Count();
            EmployeeCarsInside = carsInsideList.Count(c => c.DriverEmployeeId != 0);
            GuestCarsInside = carsInsideList.Count(c => c.DriverEmployeeId == 0);

            // آمار خروجی امروز
            var todayExitsQuery = _context.CarTrafficLogs
                                         .Where(c => c.ExitTime.HasValue && c.ExitTime.Value.Date == today);

            if (accessibleBuildingIds != null)
            {
                todayExitsQuery = todayExitsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                todayExitsQuery = todayExitsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            TodayExits = await todayExitsQuery.CountAsync();
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") ||
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }

        public async Task<IActionResult> OnPostRegisterCarExitAsync([FromBody] RegisterCarExitRequest request)
        {
            // بررسی احراز هویت
            if (!User.Identity?.IsAuthenticated == true)
            {
                return StatusCode(401, new { message = "احراز هویت نشده" });
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return StatusCode(401, new { message = "کاربر یافت نشد" });
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanRegisterTraffic(userRoles))
            {
                return Forbid("دسترسی غیرمجاز");
            }

            try
            {
                var success = await _trafficLogService.RegisterCarExitLogAsync(
                    request.CarTrafficLogId,
                    currentUser.Id,
                    DateTime.Now,
                    "خروج از طریق فرم CarExit"
                );

                if (success)
                {
                    return new JsonResult(new { success = true, message = "خروج خودرو با موفقیت ثبت شد" });
                }
                else
                {
                    return BadRequest(new { message = "خطا در ثبت خروج خودرو." });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnPostRegisterCarExitAsync: {ex.Message}");
                return BadRequest(new { message = "خطا در ثبت خروج خودرو: " + ex.Message });
            }
        }
    }

    public class RegisterCarExitRequest
    {
        public int CarTrafficLogId { get; set; }
    }
}