using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Services
{
    /// <summary>
    /// سرویس مدیریت مجوزها
    /// </summary>
    public class PermissionService
    {
        private readonly ApplicationDbContext _context;

        public PermissionService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// دریافت تمام مجوزها
        /// </summary>
        public async Task<List<Permission>> GetAllPermissionsAsync()
        {
            return await _context.Permissions
                .Where(p => p.IsActive)
                .OrderBy(p => p.Category)
                .ThenBy(p => p.Name)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت مجوزها بر اساس دسته‌بندی
        /// </summary>
        public async Task<List<Permission>> GetPermissionsByCategoryAsync(string category)
        {
            return await _context.Permissions
                .Where(p => p.IsActive && p.Category == category)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت مجوز بر اساس کد
        /// </summary>
        public async Task<Permission?> GetPermissionByCodeAsync(string code)
        {
            return await _context.Permissions
                .FirstOrDefaultAsync(p => p.Code == code && p.IsActive);
        }

        /// <summary>
        /// دریافت مجوز بر اساس شناسه
        /// </summary>
        public async Task<Permission?> GetPermissionByIdAsync(int permissionId)
        {
            return await _context.Permissions
                .FirstOrDefaultAsync(p => p.PermissionId == permissionId);
        }

        /// <summary>
        /// ایجاد مجوز جدید
        /// </summary>
        public async Task<Permission> CreatePermissionAsync(Permission permission)
        {
            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync();
            return permission;
        }

        /// <summary>
        /// به‌روزرسانی مجوز
        /// </summary>
        public async Task<Permission> UpdatePermissionAsync(Permission permission)
        {
            _context.Permissions.Update(permission);
            await _context.SaveChangesAsync();
            return permission;
        }

        /// <summary>
        /// حذف مجوز (غیرفعال کردن)
        /// </summary>
        public async Task<bool> DeletePermissionAsync(int permissionId)
        {
            var permission = await GetPermissionByIdAsync(permissionId);
            if (permission == null)
                return false;

            permission.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// دریافت دسته‌بندی‌های مجوزها
        /// </summary>
        public async Task<List<string>> GetPermissionCategoriesAsync()
        {
            return await _context.Permissions
                .Where(p => p.IsActive)
                .Select(p => p.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();
        }

        /// <summary>
        /// بررسی وجود مجوز با کد مشخص
        /// </summary>
        public async Task<bool> PermissionExistsAsync(string code, int? excludeId = null)
        {
            var query = _context.Permissions.Where(p => p.Code == code);
            
            if (excludeId.HasValue)
                query = query.Where(p => p.PermissionId != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
