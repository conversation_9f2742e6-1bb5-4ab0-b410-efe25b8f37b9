
@page "/traffic/dashboard"
@model EmployeeTrafficControl.Web.Pages.Traffic.TrafficDashboardModel
@{
    ViewData["Title"] = "داشبورد یکپارچه تردد";
}

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">@ViewData["Title"]</h1>

    <!-- Search Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-search"></i> جستجوی هوشمند
            </h6>
        </div>
        <div class="card-body">
            <form method="post" asp-page-handler="Search">
                <div class="input-group">
                    <input type="text" asp-for="SearchTerm" class="form-control" placeholder="نام خودرو، پلاک، نام کارمند یا کد پرسنلی را وارد کنید..." />
                    <button class="btn btn-primary" type="submit">جستجو</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Results Area -->
    @if (Model.SearchResult != null)
    {
        <!-- Car Result -->
        @if (Model.SearchResult.Car != null)
        {
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">نتیجه جستجوی خودرو</h6>
                </div>
                <div class="card-body">
                    <h4>@Model.SearchResult.Car.Model - @Model.SearchResult.Car.PlateNumber</h4>
                    @if (Model.SearchResult.Car.IsInParking)
                    {
                        <p>وضعیت: <span class="badge bg-success">در پارکینگ</span></p>
                        <hr />
                        <h5>ثبت خروج خودرو</h5>
                        <form method="post" asp-page-handler="RegisterCarExit">
                            <input type="hidden" name="carId" value="@Model.SearchResult.Car.CarId" />
                            <div class="mb-3">
                                <label class="form-label">راننده</label>
                                <select name="driverId" class="form-control">
                                    @foreach (var employee in Model.AvailableEmployees)
                                    {
                                        <option value="@employee.EmployeeId">@employee.FirstName @employee.LastName</option>
                                    }
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">سرنشینان</label>
                                <select name="passengerIds" class="form-control" multiple>
                                    @foreach (var employee in Model.AvailableEmployees)
                                    {
                                        <option value="@employee.EmployeeId">@employee.FirstName @employee.LastName</option>
                                    }
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">توضیحات</label>
                                <textarea name="notes" class="form-control"></textarea>
                            </div>
                            <button type="submit" class="btn btn-danger">ثبت خروج</button>
                        </form>
                    }
                    else
                    {
                        <p>وضعیت: <span class="badge bg-warning">خارج از پارکینگ</span></p>
                        <hr />
                        <h5>��بت ورود خودرو</h5>
                        <form method="post" asp-page-handler="RegisterCarEntry">
                            <input type="hidden" name="carId" value="@Model.SearchResult.Car.CarId" />
                            <p>این خودرو به همراه راننده و سرنشینان زیر خارج شده است. آیا ورود آنها را تایید می‌کنید؟</p>
                            <button type="submit" class="btn btn-success">ثبت ورود</button>
                        </form>
                    }
                </div>
            </div>
        }

        <!-- Employee Result -->
        @if (Model.SearchResult.Employee != null)
        {
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">نتیجه جستجوی کارمند</h6>
                </div>
                <div class="card-body">
                    <h4>@Model.SearchResult.Employee.FirstName @Model.SearchResult.Employee.LastName</h4>
                    <p>کد پرسنلی: @Model.SearchResult.Employee.PersonnelCode</p>
                    @if (Model.SearchResult.EmployeeStatus?.IsPresentInBuilding ?? false)
                    {
                        <p>وضعیت: <span class="badge bg-success">حاضر در ساختمان</span></p>
                        <hr />
                        <h5>ثبت خروج فردی</h5>
                        <form method="post" asp-page-handler="RegisterIndividualExit">
                            <input type="hidden" name="employeeId" value="@Model.SearchResult.Employee.EmployeeId" />
                            <button type="submit" class="btn btn-warning">ثبت خروج ساعتی</button>
                        </form>
                    }
                    else
                    {
                        <p>وضعیت: <span class="badge bg-secondary">خارج از ساختمان</span></p>
                        <hr />
                        <h5>ثبت ورود فردی</h5>
                        <form method="post" asp-page-handler="RegisterIndividualEntry">
                            <input type="hidden" name="employeeId" value="@Model.SearchResult.Employee.EmployeeId" />
                            <button type="submit" class="btn btn-info">ثبت ورود</button>
                        </form>
                    }
                </div>
            </div>
        }

        @if (Model.SearchResult.Car == null && Model.SearchResult.Employee == null)
        {
            <div class="alert alert-warning">موردی با عبارت جستجو شده یافت نشد.</div>
        }
    }
</div>
