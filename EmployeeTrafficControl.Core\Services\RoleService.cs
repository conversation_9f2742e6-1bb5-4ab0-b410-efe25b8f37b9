using EmployeeTrafficControl.Data.Data;

using EmployeeTrafficControl.Data.Models;

using Microsoft.EntityFrameworkCore;
using System;

namespace EmployeeTrafficControl.Core.Services
{
    /// <summary>
    /// سرویس مدیریت نقش‌ها
    /// </summary>
    public class RoleService
    {
        private readonly ApplicationDbContext _context;

        public RoleService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// دریافت تمام نقش‌ها
        /// </summary>
        public async Task<List<Role>> GetAllRolesAsync()
        {
            return await _context.Roles
                .Where(r => r.IsActive)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }

        /// <summary>
        /// دریافت نقش بر اساس شناسه
        /// </summary>
        public async Task<Role?> GetRoleByIdAsync(int roleId)
        {
            return await _context.Roles
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(r => r.RoleId == roleId);
        }

        /// <summary>
        /// دریافت نقش بر اساس کد
        /// </summary>
        public async Task<Role?> GetRoleByCodeAsync(string code)
        {
            return await _context.Roles
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(r => r.Code == code && r.IsActive);
        }

        /// <summary>
        /// دریافت نقش بر اساس نام
        /// </summary>
        public async Task<Role?> FindByNameAsync(string rolename)
        {
            return await _context.Roles
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(r => r.Name == rolename && r.IsActive);
        }


        /// <summary>
        /// ایجاد نقش جدید
        /// </summary>
        public async Task<Role> CreateRoleAsync(Role role)
        {
            _context.Roles.Add(role);
            await _context.SaveChangesAsync();
            return role;
        }

        /// <summary>
        /// به‌روزرسانی نقش
        /// </summary>
        public async Task<Role> UpdateRoleAsync(Role role)
        {
            _context.Roles.Update(role);
            await _context.SaveChangesAsync();
            return role;
        }

        /// <summary>
        /// حذف نقش (غیرفعال کردن)
        /// </summary>
        public async Task<bool> DeleteRoleAsync(int roleId)
        {
            var role = await GetRoleByIdAsync(roleId);
            if (role == null || role.IsSystemRole)
                return false;

            // بررسی اینکه آیا کاربری با این نقش وجود دارد
            var hasUsers = await _context.Users.AnyAsync(u => u.RoleId == roleId);
            if (hasUsers)
                return false;

            role.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// اعطای مجوز به نقش
        /// </summary>
        public async Task<bool> GrantPermissionToRoleAsync(int roleId, int permissionId, int grantedByUserId)
        {
            // بررسی وجود نقش و مجوز
            var role = await GetRoleByIdAsync(roleId);
            var permission = await _context.Permissions.FindAsync(permissionId);
            
            if (role == null || permission == null)
                return false;

            // بررسی اینکه آیا این مجوز قبلاً اعطا شده
            var existingRolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId);

            if (existingRolePermission != null)
                return false;

            var rolePermission = new RolePermission
            {
                RoleId = roleId,
                PermissionId = permissionId,
                GrantedByUserId = grantedByUserId,
                GrantedAt = DateTime.Now
            };

            _context.RolePermissions.Add(rolePermission);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// سلب مجوز از نقش
        /// </summary>
        public async Task<bool> RevokePermissionFromRoleAsync(int roleId, int permissionId)
        {
            var rolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId);

            if (rolePermission == null)
                return false;

            _context.RolePermissions.Remove(rolePermission);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// دریافت مجوزهای یک نقش
        /// </summary>
        public async Task<List<Permission>> GetRolePermissionsAsync(int roleId)
        {
            return await _context.RolePermissions
                .Where(rp => rp.RoleId == roleId)
                .Include(rp => rp.Permission)
                .Select(rp => rp.Permission)
                .ToListAsync();
        }

        /// <summary>
        /// بررسی وجود نقش با کد مشخص
        /// </summary>
        public async Task<bool> RoleExistsAsync(string code, int? excludeId = null)
        {
            var query = _context.Roles.Where(r => r.Code == code);
            
            if (excludeId.HasValue)
                query = query.Where(r => r.RoleId != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
