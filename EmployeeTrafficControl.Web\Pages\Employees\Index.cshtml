@page
@model EmployeeTrafficControl.Web.Pages.Employees.IndexModel
@{
    ViewData["Title"] = "لیست کارمندان";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>لیست کارمندان</h1>
        <a asp-page="Create" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> افزودن کارمند جدید
        </a>
    </div>
</div>

@if (Model.Employees == null || !Model.Employees.Any())
{
    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle"></i>
        <p class="mb-0">هیچ کارمندی یافت نشد.</p>
        <a asp-page="Create" class="btn btn-primary mt-2">افزودن اولین کارمند</a>
    </div>
}
else
{
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>نام و نام خانوادگی</th>
                            <th>کد ملی</th>
                            <th>کد پرسنلی</th>
                            <th>شغل</th>
                            <th>ساختمان</th>
                            <th>وضعیت</th>
                            <th class="text-center">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in Model.Employees)
                        {
                            <tr>
                                <td>
                                    <strong>@employee.FirstName @employee.LastName</strong>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(employee.NationalCode))
                                    {
                                        <span>@employee.NationalCode</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">نامشخص</span>
                                    }
                                </td>
                                <td>
                                    <code>@employee.PersonnelCode</code>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">@employee.Job?.Title</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">@employee.Building?.Name</span>
                                </td>
                                <td>
                                    @if (employee.IsActive)
                                    {
                                        <span class="badge bg-success">فعال</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غیرفعال</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a asp-page="Edit" asp-route-id="@employee.EmployeeId" class="btn btn-sm btn-outline-primary" title="ویرایش">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-page="Details" asp-route-id="@employee.EmployeeId" class="btn btn-sm btn-outline-info" title="جزئیات">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-page="Delete" asp-route-id="@employee.EmployeeId" class="btn btn-sm btn-outline-danger" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <p class="text-muted">
                    نمایش @Model.Employees.Count مورد
                </p>
            </div>
            <div class="col-md-6 text-end">
                <a asp-page="Create" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> افزودن کارمند جدید
                </a>
            </div>
        </div>
    </div>
}
