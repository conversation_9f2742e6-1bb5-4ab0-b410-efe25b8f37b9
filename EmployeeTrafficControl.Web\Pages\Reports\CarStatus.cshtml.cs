using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EmployeeTrafficControl.Data.Data;
using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Web.Attributes;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    [AuthorizePermission("VIEW_REPORTS")]
    public class CarStatusModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public CarStatusModel(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public List<CarTrafficLog> CarTrafficLogs { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public DateTime? ReportDate { get; set; } = DateTime.Today;

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        public int TotalCars { get; set; }
        public int CarsInside { get; set; }
        public int CarsOutside { get; set; }
        public int TodayEntries { get; set; }
        public int TodayExits { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت با Microsoft Identity
            if (!User.Identity?.IsAuthenticated == true)
            {
                return RedirectToPage("/Account/Login");
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            var userRoles = await _userManager.GetRolesAsync(currentUser);
            if (!CanViewReports(userRoles))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(currentUser);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(ApplicationUser currentUser)
        {
            var date = ReportDate ?? DateTime.Today;

            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری گزارش ترافیک خودرو
            var carTrafficQuery = _context.CarTrafficLogs
                                         .Include(ctl => ctl.Car)
                                         .Include(ctl => ctl.DriverEmployee)
                                         .Include(ctl => ctl.Building)
                                         .Include(ctl => ctl.CarPassengers)
                                             .ThenInclude(cp => cp.Employee)
                                         .Where(ctl => ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date == date.Date);

            if (accessibleBuildingIds != null)
            {
                carTrafficQuery = carTrafficQuery.Where(ctl => accessibleBuildingIds.Contains(ctl.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carTrafficQuery = carTrafficQuery.Where(ctl => ctl.BuildingId == BuildingId.Value);
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                carTrafficQuery = carTrafficQuery.Where(ctl =>
                    (ctl.Car != null && ctl.Car.PlateNumber.ToLower().Contains(searchLower)) ||
                    (ctl.DriverEmployee != null &&
                     (ctl.DriverEmployee.FirstName.ToLower().Contains(searchLower) ||
                      ctl.DriverEmployee.LastName.ToLower().Contains(searchLower) ||
                      ctl.DriverEmployee.PersonnelCode.ToLower().Contains(searchLower))));
            }

            CarTrafficLogs = await carTrafficQuery.OrderByDescending(ctl => ctl.ExitTime)
                                                 .ToListAsync();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds, date);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds, DateTime date)
        {
            // آمار کلی خودروها
            var carsQuery = _context.Cars.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                carsQuery = carsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            TotalCars = await carsQuery.CountAsync();

            // آمار خودروهای داخل پارکینگ (بر اساس وضعیت فعلی خودروها)
            var carsInsideQuery = _context.Cars.Where(c => c.IsInParking);
            if (accessibleBuildingIds != null)
            {
                carsInsideQuery = carsInsideQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }
            if (BuildingId.HasValue)
            {
                carsInsideQuery = carsInsideQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            CarsInside = await carsInsideQuery.CountAsync();
            CarsOutside = TotalCars - CarsInside;

            // آمار ورودی و خروجی امروز
            var todayTrafficQuery = _context.CarTrafficLogs.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                todayTrafficQuery = todayTrafficQuery.Where(ctl => accessibleBuildingIds.Contains(ctl.BuildingId));
            }
            if (BuildingId.HasValue)
            {
                todayTrafficQuery = todayTrafficQuery.Where(ctl => ctl.BuildingId == BuildingId.Value);
            }

            TodayExits = await todayTrafficQuery.CountAsync(ctl => ctl.ExitTime.HasValue && ctl.ExitTime.Value.Date == date.Date);
            TodayEntries = await todayTrafficQuery.CountAsync(ctl => ctl.EntryTime.HasValue && ctl.EntryTime.Value.Date == date.Date);
        }

        private List<int>? GetAccessibleBuildingIds(ApplicationUser user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role?.Code == "ADMIN")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanViewReports(IList<string> userRoles)
        {
            return userRoles.Contains("Admin") || userRoles.Contains("Manager") || 
                   userRoles.Contains("Guard") || userRoles.Contains("User");
        }
    }
} 