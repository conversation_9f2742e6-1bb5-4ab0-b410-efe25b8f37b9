﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using EmployeeTrafficControl.Data.Data; // Added

using System.IO;

namespace EmployeeTrafficControl.Data
{
    // This class is used by the Entity Framework Core tools to create an instance of ApplicationDbContext
    // at design time (e.g., when running 'dotnet ef migrations add' or 'dotnet ef database update').
    // It provides a way for the tools to get the DbContextOptions, including the connection string,
    // without running the full application.
    public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
    {
        public ApplicationDbContext CreateDbContext(string[] args)
        {
            // Build configuration
            // This reads appsettings.json to get the connection string
            IConfigurationRoot configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory()) // Set the base path to the project directory
                .AddJsonFile("appsettings.json") // Add appsettings.json
                .Build();

            // Get the connection string
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            // Create DbContextOptionsBuilder
            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
            optionsBuilder.UseSqlServer(connectionString);

            // Return a new instance of ApplicationDbContext with the configured options
            return new ApplicationDbContext(optionsBuilder.Options);
        }
    }
}