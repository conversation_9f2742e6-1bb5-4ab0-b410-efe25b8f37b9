using EmployeeTrafficControl.Data.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Identity;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    [AuthorizePermission("VIEW_USERS")]
    [Authorize(Policy = "RequireAdminOrManagerRole")]
    public class DetailsModel : PageModel
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public DetailsModel(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }

        public ApplicationUser User { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            User = await _userManager.FindByIdAsync(id.ToString());
            if (User == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }
            try
            {
                var result = await _userManager.DeleteAsync(user);
                if (result.Succeeded)
                {
                TempData["SuccessMessage"] = "کاربر با موفقیت حذف شد.";
                return RedirectToPage("./Index");
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این کاربر وجود ندارد.";
                    return RedirectToPage("./Details", new { id });
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف کاربر: " + ex.Message;
                return RedirectToPage("./Details", new { id });
            }
        }
    }
}
